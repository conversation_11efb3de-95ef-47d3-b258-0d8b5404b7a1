import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class PopupComponent {
    show: boolean;
    messageAlignment: 'left' | 'center' | 'right';
    title: string;
    message: string;
    showTitle: boolean;
    showHeaderIcon: boolean;
    headerIconName: string;
    iconColor: string;
    iconSize: number;
    showClose: boolean;
    closeIconName: string;
    closeIconColor: string;
    closeIconSize: number;
    showInlineMessage: boolean;
    inlineIconName: string;
    inlineIconSize: number;
    inlineIconColor: string;
    inlineMessage: string;
    private _popupWidth;
    set popupWidth(value: string | number);
    get popupWidth(): string;
    showConfirm: boolean;
    showCancel: boolean;
    cancelButtonLabel: string;
    cancelButtonSize: 'small' | 'medium' | 'large';
    cancelButtonVariant: 'primary' | 'secondary';
    cancelButtonBackground: string;
    confirmButtonLabel: string;
    confirmButtonSize: 'small' | 'medium' | 'large';
    confirmButtonVariant: 'primary' | 'secondary';
    confirmButtonBackground: string;
    confirm: EventEmitter<void>;
    cancel: EventEmitter<void>;
    closed: EventEmitter<void>;
    onConfirm(): void;
    onCancel(): void;
    closePopup(): void;
    /**
     * Splits multiline message content using <br> tags and trims each line.
     * Used in the template to support line breaks.
     */
    getMessageLines(): string[];
    static ɵfac: i0.ɵɵFactoryDeclaration<PopupComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<PopupComponent, "ava-popup", never, { "show": { "alias": "show"; "required": false; }; "messageAlignment": { "alias": "messageAlignment"; "required": false; }; "title": { "alias": "title"; "required": false; }; "message": { "alias": "message"; "required": false; }; "showTitle": { "alias": "showTitle"; "required": false; }; "showHeaderIcon": { "alias": "showHeaderIcon"; "required": false; }; "headerIconName": { "alias": "headerIconName"; "required": false; }; "iconColor": { "alias": "iconColor"; "required": false; }; "iconSize": { "alias": "iconSize"; "required": false; }; "showClose": { "alias": "showClose"; "required": false; }; "closeIconName": { "alias": "closeIconName"; "required": false; }; "closeIconColor": { "alias": "closeIconColor"; "required": false; }; "closeIconSize": { "alias": "closeIconSize"; "required": false; }; "showInlineMessage": { "alias": "showInlineMessage"; "required": false; }; "inlineIconName": { "alias": "inlineIconName"; "required": false; }; "inlineIconSize": { "alias": "inlineIconSize"; "required": false; }; "inlineIconColor": { "alias": "inlineIconColor"; "required": false; }; "inlineMessage": { "alias": "inlineMessage"; "required": false; }; "popupWidth": { "alias": "popupWidth"; "required": false; }; "showConfirm": { "alias": "showConfirm"; "required": false; }; "showCancel": { "alias": "showCancel"; "required": false; }; "cancelButtonLabel": { "alias": "cancelButtonLabel"; "required": false; }; "cancelButtonSize": { "alias": "cancelButtonSize"; "required": false; }; "cancelButtonVariant": { "alias": "cancelButtonVariant"; "required": false; }; "cancelButtonBackground": { "alias": "cancelButtonBackground"; "required": false; }; "confirmButtonLabel": { "alias": "confirmButtonLabel"; "required": false; }; "confirmButtonSize": { "alias": "confirmButtonSize"; "required": false; }; "confirmButtonVariant": { "alias": "confirmButtonVariant"; "required": false; }; "confirmButtonBackground": { "alias": "confirmButtonBackground"; "required": false; }; }, { "confirm": "confirm"; "cancel": "cancel"; "closed": "closed"; }, never, ["*"], true, never>;
}
