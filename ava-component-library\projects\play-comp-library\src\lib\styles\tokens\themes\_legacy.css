/**
 * Theme: Legacy
 * Purpose: Preserves the original color scheme and design decisions from the legacy theme system
 */

[data-theme="legacy"] {
  /* Legacy Color Overrides */
  --color-brand-primary: var(--global-color-primary);
  --color-brand-secondary: var(--global-color-secondary);
  --color-brand-tertiary: var(--global-color-tertiary);
  --color-brand-quaternary: var(--global-color-quaternary);
  --color-brand-quinary: var(--global-color-quinary);
  --color-brand-senary: var(--global-color-senary);

  /* Legacy Text Color Overrides */
  --color-text-primary: var(--global-text-color-primary);
  --color-text-secondary: var(--global-text-color-secondary);
  --color-text-tertiary: var(--global-text-color-tertiary);
  --color-text-quaternary: var(--global-text-color-quaternary);
  --color-text-quinary: var(--global-text-color-quinary);
  --color-text-senary: var(--global-text-color-senary);

  /* Legacy Background Overrides */
  --color-background-primary: var(--global-color-secondary);
  --color-background-secondary: var(--global-color-quaternary);

  /* Legacy Surface Overrides */
  --color-surface-interactive-default: var(--global-color-primary);
  --color-surface-interactive-hover: var(--global-color-primary-600);
  --color-surface-interactive-active: var(--global-color-primary-700);
  --color-surface-subtle: var(--global-color-quaternary);
  --color-surface-subtle-hover: var(--global-color-quaternary-600);

  /* Legacy Border Overrides */
  --color-border-default: var(--global-color-tertiary);
  --color-border-subtle: var(--global-color-quaternary);
  --color-border-interactive: var(--global-color-primary);
  --color-border-focus: var(--global-color-primary);

  /* Legacy Component Overrides */
  --button-primary-background: var(--global-color-primary);
  --button-primary-text: var(--global-text-color-primary);
  --button-primary-hover-background: var(--global-color-primary-600);

  --input-border-color: var(--global-color-tertiary);
  --input-focus-border-color: var(--global-color-primary);

  --badge-success-background: var(--global-color-quinary);
  --badge-success-text: var(--global-text-color-primary);

  --toast-success-background: var(--global-color-quinary);
  --toast-error-background: var(--global-color-red-500);

  --dropdown-background: var(--global-color-secondary);
  --dropdown-border-color: var(--global-color-tertiary);

  --tooltip-background: var(--global-color-senary);
  --tooltip-text: var(--global-text-color-primary);

  --progress-track-background: var(--global-color-quaternary);
  --progress-fill-background: var(--global-color-primary);

  --slider-track-background: var(--global-color-quaternary);
  --slider-thumb-background: var(--global-color-primary);

  --radio-border-color: var(--global-color-tertiary);
  --radio-checked-background: var(--global-color-primary);

  --checkbox-border-color: var(--global-color-tertiary);
  --checkbox-checked-background: var(--global-color-primary);

  --spinner-color: var(--global-color-primary);

  /* Legacy Typography Overrides */
  --font-family-body: var(--global-font-family-body);
  --font-family-heading: var(--global-font-family-heading);
  --font-family-display: var(--global-font-family-display);

  /* Legacy Spacing Overrides */
  --global-spacing-1: 0.25rem;
  --global-spacing-2: 0.5rem;
  --global-spacing-3: 0.75rem;
  --global-spacing-4: 1rem;
  --global-spacing-5: 1.5rem;
  --global-spacing-6: 2rem;

  /* Legacy Border Radius Overrides */
  --global-radius-sm: 0.25rem;
  --global-radius-md: 0.5rem;
  --global-radius-lg: 0.75rem;

  /* Legacy Elevation Overrides */
  --global-elevation-01: 0px 1px 3px rgba(0, 0, 0, 0.12);
  --global-elevation-02: 0px 3px 6px rgba(0, 0, 0, 0.15);
  --global-elevation-03: 0px 6px 12px rgba(0, 0, 0, 0.18);
} 