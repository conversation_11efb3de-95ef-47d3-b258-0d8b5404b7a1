import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'ava-accordion',
  imports: [CommonModule,LucideAngularModule],
  standalone: true,
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AccordionComponent {
  @Input() expanded = false;
  @Input() animation = false;
  @Input() controlled = false;
  @Input() iconClosed = ''; 
  @Input() iconOpen = '';  
  @Input() titleIcon = '';  
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() type: 'default' | 'titleIcon' = 'default';
  get accordionClasses() {
    return {
      animated: this.animation,
      expanded: this.expanded,
    };
  }

  onAccordionKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      this.toggleExpand();
      event.preventDefault();
    }
  }

  toggleExpand() {
    if (!this.controlled) {
      this.expanded = !this.expanded;
    }
  }
}
