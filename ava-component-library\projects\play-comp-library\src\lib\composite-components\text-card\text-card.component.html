<div class="ava-text-card-container" [style.width.px]="width > 0 ? width : null">
    <!-- Default Type -->
    <ng-container *ngIf="type === 'default'">
        <div class="default">
            <ava-card>
                <div header class="text-card-header">
                    <div class="icon-circle">
                        <ava-icon [iconName]="iconName" [iconSize]="24"></ava-icon>
                    </div>
                    <div>
                        <h3>{{ title }}</h3>
                    </div>
                </div>

                <div content>
                    <h1>{{ value }}</h1>
                </div>

                <div footer>
                    <p>{{ description }}</p>
                </div>
            </ava-card>
        </div>

    </ng-container>

    <!-- Create Type -->
    <ng-container *ngIf="type === 'create'">
        <div class="create-type" (click)="onCardClick()">
            <ava-card>
                <div content class="card-content">
                    <div class="icon-container">
                        <ava-icon [iconName]="'plus'" [iconColor]="iconColor" [iconSize]="24"></ava-icon>
                    </div>
                    <div>
                        <h3>{{ title }}</h3>
                    </div>
                </div>
            </ava-card>
        </div>
    </ng-container>

    <!-- Prompt Type -->
    <ng-container *ngIf="type === 'prompt'">
        <div class="prompt-type">
            <ava-card>
                <div header class="text-card-header">
                    <div class="top-icons">
                        <span>
                            <ava-icon [iconName]="'wrench'" [iconColor]="iconColor" [iconSize]="15"></ava-icon> Tool
                        </span>
                        <span>
                            <ava-icon [iconName]="'users'" [iconColor]="iconColor" [iconSize]="15"></ava-icon> {{
                            userCount }}
                        </span>
                    </div>
                    <div>
                        <h3>{{ title }}</h3>
                    </div>
                </div>

                <div content class="card-content">
                    <p class="description">{{ description }}</p>
                </div>

                <div footer class="footer">
                    <div class="name-date-container">
                        <p>
                            <span><ava-icon [iconName]="'user'" [iconSize]="10" [iconColor]="iconColor"></ava-icon>
                            </span> {{ name }}
                        </p>

                        <p>
                            <span>
                                <ava-icon [iconName]="'calendar-days'" [iconSize]="10"
                                    [iconColor]="iconColor"></ava-icon>
                            </span>
                            {{ date }}
                        </p>

                    </div>
                    <div class="action-icon-container">
                        <ng-container *ngFor="let icon of iconList">
                            <ng-container *ngIf="icon !== 'square-play'; else customPlay">
                                <span>
                                    <ava-icon [iconName]="icon" [iconSize]="20" iconColor="#6489BF"></ava-icon>
                                </span>
                            </ng-container>

                            <ng-template #customPlay>
                                <span class="play-icon-wrapper">
                                    <ava-icon [iconName]="'play'" [iconSize]="15" iconColor="#ffffff"></ava-icon>
                                </span>
                            </ng-template>
                        </ng-container>

                    </div>
                </div>
            </ava-card>
        </div>
    </ng-container>
</div>