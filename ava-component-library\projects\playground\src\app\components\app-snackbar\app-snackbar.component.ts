import { Component, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SnackbarService } from '../../../../../play-comp-library/src/lib/components/snackbar/snackbar.service';

@Component({
  selector: 'app-app-snackbar',
  standalone: true,
  imports: [CommonModule,],
  templateUrl: './app-snackbar.component.html',
  styleUrls: ['./app-snackbar.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppSnackbarComponent {
  constructor(private snackbarService: SnackbarService) { }

  triggerSnackBar() {
    this.snackbarService.show(
      'Your order is being prepared, please hold on a moment...',
      'bottom-center',
      3000,

    );
  }


  sections = [
    {
      title: 'Basic Snackbar',
      description: 'A standard snackbar with message, action, and duration.',
      showCode: false
    },
    {
      title: 'Animated Snackbar',
      description: 'Snackbar with optional animation control.',
      showCode: false
    },
    {
      title: 'Snackbar Sizes',
      description: 'Demonstrates various snackbar sizes (small, medium, large, extra-large).',
      showCode: false
    },
    {
      title: 'Extended Sizes',
      description: 'Real-world snackbar examples with longer messages and different sizes.',
      showCode: false
    }
  ];

  snackbarMessage = '';
  snackbarActionLabel = '';
  snackbarDuration = 3000;
  snackbarSize: 'small' | 'medium' | 'large' | 'extra-large' = 'medium';
  snackbarAnimate = true;

  showSnackbar(message: string, actionLabel: string, duration: number, size: 'small' | 'medium' | 'large' | 'extra-large', animate = true) {
    this.snackbarMessage = message;
    this.snackbarActionLabel = actionLabel;
    this.snackbarDuration = duration;
    this.snackbarSize = size;
    this.snackbarAnimate = animate;

    // this.snackbar.message = message;
    // this.snackbar.actionLabel = actionLabel;
    // this.snackbar.duration = duration;
    // this.snackbar.size = size;
    // this.snackbar.animate = animate;

    // this.snackbar.show();
  }

  onSnackbarAction() {
    // console.log('Snackbar action clicked!');
  }

  onSnackbarDismiss() {
    // console.log('Snackbar dismissed');
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getSnackbarCode(sectionTitle: string): string {
    const codeMap: Record<string, string> = {
      'basic snackbar': `
<awe-snackbar
  #snackbar
  [message]="snackbarMessage"
  [actionLabel]="snackbarActionLabel"
  [duration]="snackbarDuration"
  [size]="snackbarSize"
  [animate]="snackbarAnimate"
  (action)="onSnackbarAction()"
  (dismiss)="onSnackbarDismiss()">
</awe-snackbar>

<section class="bottom-panel">
  <awe-button variant="primary" (click)="showSnackbar('Save your progress?', 'Yes', 10000, 'medium')">Normal Snackbar</awe-button>
</section>`,

      'animated snackbar': `
<section class="bottom-panel">
  <awe-button variant="secondary" (click)="showSnackbar('Your order is being prepared, please hold on a moment...', 'Cancel', 5000, 'medium', true)">Animated Snackbar</awe-button>
  <awe-button variant="primary" (click)="showSnackbar('Your order has been successfully placed! Do you want to track it?', 'Track Order', 5000, 'medium', false)">Non-Animated Snackbar</awe-button>
</section>`
      ,
      'snackbar sizes': `
<section class="bottom-panel">
  <awe-button variant="primary" (click)="showSnackbar('Error: Payment failed due to insufficient balance. Try another method?', 'Undo', 5000, 'extra-large')">Extra Large</awe-button>
  <awe-button variant="secondary" (click)="showSnackbar('Your session will expire soon. Save your progress?', 'View', 5000, 'large')">Large</awe-button>
  <awe-button variant="primary" (click)="showSnackbar('Your file has been uploaded.', 'Extend', 5000, 'medium')">Medium</awe-button>
  <awe-button variant="secondary" (click)="showSnackbar('Saved', 'Retry', 5000, 'small')">Small</awe-button>
</section>`,

      'extended sizes': `
<section class="bottom-panel">
  <awe-button variant="primary" (click)="showSnackbar('Your payment failed due to insufficient balance. Would you like to try another method?', 'Try Again', 5000, 'extra-large')">Extra Large snackbar</awe-button>
  <awe-button variant="secondary" (click)="showSnackbar('Oops! We couldn’t complete the payment. Do you want to choose a different payment option?', 'Choose Option', 5000, 'large')">Large snackbar</awe-button>
  <awe-button variant="primary" (click)="showSnackbar('Payment unsuccessful. Please try a different method.', 'Retry', 5000, 'medium')">Medium snackbar</awe-button>
  <awe-button variant="secondary" (click)="showSnackbar('Payment failed. Try again.', 'Retry', 5000, 'small')">Small snackbar</awe-button>
</section>`


    };

    return codeMap[sectionTitle.toLowerCase()] || '';
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;
    const isClickInside = clickedElement.closest('.section-header');
    if (!isClickInside) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  apiProps = [
    { name: 'message', type: 'string', default: '""', description: 'The message to be displayed in the snackbar.' },
    { name: 'actionLabel', type: 'string', default: '""', description: 'Label for the action button.' },
    { name: 'duration', type: 'number', default: '3000', description: 'Time in milliseconds for which the snackbar is displayed.' },
    { name: 'size', type: '"small" | "medium" | "large" | "extra-large"', default: '"medium"', description: 'Size of the snackbar.' },
    { name: 'animate', type: 'boolean', default: 'true', description: 'Whether to animate the appearance of the snackbar.' }
  ];

  events = [
    { name: 'action', type: 'EventEmitter<void>', description: 'Emitted when the action button is clicked.' },
    { name: 'dismiss', type: 'EventEmitter<void>', description: 'Emitted when the snackbar is dismissed automatically or manually.' }
  ];

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getSnackbarCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
