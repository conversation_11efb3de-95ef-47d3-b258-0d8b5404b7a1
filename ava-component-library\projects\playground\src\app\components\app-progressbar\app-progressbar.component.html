<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Progress Bar Component</h1>
        <p class="description">
          A versatile progress bar component that supports various configurations such as circular and linear designs, custom colors, and sizes. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ProgressBarComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Circular Progress Bar'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="30" label="Circular Bar Design" type="circular" color="#2E308E" [svgSize]="140"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="50" label="Circular Bar Design" type="circular" color="#2E308E" [svgSize]="140"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="95" label="Circular Bar Design" type="circular" color="#2E308E" [svgSize]="140"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="100" label="Circular Bar Design" type="circular" color="#2E308E" [svgSize]="140"></ava-progressbar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Linear Progress Bar'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="10" label="Linear Bar Design" type="linear" mode="determinate" color="#33364D"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="40" label="Linear Bar Design" type="linear" mode="determinate" color="#33364D"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="80" label="Linear Bar Design" type="linear" mode="determinate" color="#33364D"></ava-progressbar>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-progressbar [percentage]="100" label="Linear Bar Design" type="linear" mode="determinate" color="#33364D"></ava-progressbar>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
   
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
