.ava-snackbar {
    position: fixed;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    font-size: 14px;

    &.top-left {
        top: 20px;
        left: 20px;
    }

    &.top-right {
        top: 20px;
        right: 20px;
    }

    &.bottom-left {
        bottom: 20px;
        left: 20px;
    }

    &.bottom-right {
        bottom: 20px;
        right: 20px;
    }

    &.top-center {
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
    }

    &.bottom-center {
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
    }

    &.center {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}