import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProgressComponent } from "../../../../../play-comp-library/src/lib/components/progress/progress.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";

@Component({
  selector: 'app-progressbar',
  standalone: true,
  imports: [CommonModule, ProgressComponent, IconsComponent],
  templateUrl: './app-progressbar.component.html',
  styleUrls: ['./app-progressbar.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppProgressbarComponent {
  sections = [
    {
      title: 'Circular Progress Bar',
      description: 'Demonstrates the circular progress bar with different percentages. Use this to show progress in a circular format.',
      showCode: false
    },
    {
      title: 'Linear Progress Bar',
      description: 'Shows the linear progress bar with different percentages. Use this to show progress in a linear format.',
      showCode: false
    },
  ];

  apiProps = [
    { name: 'percentage', type: 'number', default: '0', description: 'The percentage of progress completed, ranging from 0 to 100.' },
    { name: 'label', type: 'string', default: "''", description: 'The label displayed with the progress bar, providing context to the user.' },
    { name: 'type', type: "'circular' | 'linear'", default: "'linear'", description: 'The type of progress bar: circular for round progress indicators, linear for bar-style progress.' },
    { name: 'color', type: 'string', default: "'#000'", description: 'The color of the progress bar, customizable to fit the design theme.' },
    { name: 'svgSize', type: 'number', default: '100', description: 'The size of the SVG for circular progress bars, affecting the diameter of the circle.' },
    { name: 'mode', type: "'determinate' | 'indeterminate'", default: "'determinate'", description: 'The mode of the linear progress bar: determinate for known progress, indeterminate for unknown progress.' }
  ];

  // events = [
  //   { name: 'iconClicked', type: 'EventEmitter<{ name: string, side: string, index: number }>', description: 'Emitted when an icon is clicked.' }
  // ];

  @ViewChild('codeBlock') codeBlock!: ElementRef;

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getExampleCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'circular progress bar': `
import { Component } from '@angular/core';
import { ProgressBarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-circular-progressbar',
  standalone: true,
  imports: [ProgressBarComponent],
  template: \`
    <awe-progress-bar
      [percentage]="30"
      label="Circular Bar Design"
      type="circular"
      color="#2E308E"
      [svgSize]="140">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="50"
      label="Circular Bar Design"
      type="circular"
      color="#2E308E"
      [svgSize]="140">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="95"
      label="Circular Bar Design"
      type="circular"
      color="#2E308E"
      [svgSize]="140">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="100"
      label="Circular Bar Design"
      type="circular"
      color="#2E308E"
      [svgSize]="140">
    </awe-progress-bar>
  \`
})
export class CircularProgressBarComponent {}
`,
      'linear progress bar': `
import { Component } from '@angular/core';
import { ProgressBarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-linear-progressbar',
  standalone: true,
  imports: [ProgressBarComponent],
  template: \`
    <awe-progress-bar
      [percentage]="10"
      label="Linear Bar Design"
      type="linear"
      mode="determinate"
      color="#33364D">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="40"
      label="Linear Bar Design"
      type="linear"
      mode="determinate"
      color="#33364D">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="80"
      label="Linear Bar Design"
      type="linear"
      mode="determinate"
      color="#33364D">
    </awe-progress-bar>
    <awe-progress-bar
      [percentage]="100"
      label="Linear Bar Design"
      type="linear"
      mode="determinate"
      color="#33364D">
    </awe-progress-bar>
  \`
})
export class LinearProgressBarComponent {}
`,

    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  copyCode(sectionTitle: string): void {
    const code = this.getExampleCode(sectionTitle);
    navigator.clipboard.writeText(code).then(() => {
      alert('Code copied to clipboard!');
    });
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (!this.codeBlock.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }
}
