.ava-stepper {
  display: flex;
  width: 100%;

  &.horizontal {
    flex-direction: row;
    justify-content: space-between;

    .step-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .step-circle-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .step-line {
          position: absolute;
          top: 50%;
          left: 50%;
          background-color:var(--stepper-wrapper-background);
          z-index: 1;
        }
      }

      .step-label {
        margin-top: 8px;
        font-size: var(--step-label-font);
        text-align: center;

        &.active {
          font-weight: var(--step-label-active-font-weight);
          color: var(--step-circle-text);
        }
      }
    }
  }

  &.vertical {
    flex-direction: column;

    .step-wrapper {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      gap: 1rem;
      position: relative;
      margin-bottom: 2rem;

      .step-circle-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .step-line {
          position: absolute;
          top: 100%;
          left: 50%;
          background-color: var(--stepper-wrapper-background);
          transform: translateX(-50%);
          z-index: 1;
        }
      }

      .step-label {
        margin-top: 8px;
        font-size:var(--step-label-font);
        white-space: nowrap;

        &.active {
          font-weight:var(--step-label-active-font-weight);
          color: var(--step-circle-text);
        }
      }
    }
  }

  .step-circle {
    border-radius: 50%;
    border: 3px solid var(--stepper-wrapper-background);
    background-color: var(--stepper-background);
    color: var(--stepper-wrapper-background);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;

    &.active,
    &.completed {
      background-color: var(--stepper-wrapper-background);
      color: var(--stepper-background);
    }
  }

  // Size: Small
  &.small {
    .step-circle {
      width: var(--stepper-size-sm-circle-size);
      height: var(--stepper-size-sm-circle-size);
      font-size: var(--stepper-size-sm-font);
    }

    &.horizontal {
      .step-line {
        width: 100%;
        height: 2px;
        transform: translateX(12px) translateY(-50%);
      }
    }

    &.vertical {
      .step-line {
        width: 2px;
        height: 40px;
      }
    }
  }

  // Size: Medium
  &.medium {
    .step-circle {
      width: var(--stepper-size-md-circle-size);
      height:var(--stepper-size-md-circle-size);
      font-size: var(--stepper-size-md-font);
    }

    &.horizontal {
      .step-line {
        width: 100%;
        height: 3px;
        transform: translateX(18px) translateY(-50%);
      }
    }

    &.vertical {
      .step-line {
        width: 3px;
        height: 50px;
      }
    }
  }

  // Size: Large
  &.large {
    .step-circle {
      width: var(--stepper-size-lg-circle-size);
      height: var(--stepper-size-lg-circle-size);
      font-size: var(--stepper-size-lg-font);
    }

    &.horizontal {
      .step-line {
        width: 100%;
        height: 4px;
        transform: translateX(24px) translateY(-50%);
      }
    }

    &.vertical {
      .step-line {
        width: 4px;
        height: 60px;
      }
    }
  }
}
