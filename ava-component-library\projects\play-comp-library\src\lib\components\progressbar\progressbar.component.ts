import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnChanges, AfterViewInit, HostListener, forwardRef, ChangeDetectionStrategy } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'ava-progressbar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './progressbar.component.html',
  styleUrl: './progressbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ProgressComponent),
      multi: true,
    },
  ]
})
export class ProgressComponent implements OnInit, OnChanges, AfterViewInit, ControlValueAccessor {
  @Input() percentage: number = 0;
  @Input() bufferValue: number = 0;
  @Input() label: string = '';
  @Input() type: 'circular' | 'linear' = 'circular';
  @Input() color: string = '#2E308E';
  @Input() mode: 'determinate' | 'indeterminate' | 'buffer' | 'query' = 'determinate';
  @Input() svgSize?: number; // Allow the parent to override the size

  // Generate unique progress ID
  progressId: string = `progress-${Math.random().toString(36).substring(2, 9)}`;

  private readonly radius = 45;
  public readonly circumference = 2 * Math.PI * this.radius;
  dashOffset = this.circumference;
  errorMessage: string = '';
  
  // Track the display percentage for animation
  displayPercentage: number = 0;

  private onChange = (value: number) => {};
  private onTouched = () => {};

  ngOnInit(): void {
    this.updateSvgSize();
    this.dashOffset = this.circumference;
  }

  ngAfterViewInit(): void {
    setTimeout(() => this.updateProgress(), 100);
  }

  ngOnChanges(): void {
    this.validateInputs();
    this.updateSvgSize();
    this.updateProgress();
    
    // For linear progress, start animation from 0 to target percentage
    if (this.type === 'linear' && this.mode === 'determinate') {
      this.animateLinearProgress();
    }
  }
  
  private animateLinearProgress(): void {
    // Save target percentage and reset display to 0
    const targetPercentage = this.percentage;
    this.displayPercentage = 0;
    
    // Calculate animation duration based on percentage
    const duration = Math.max(1, targetPercentage / 100 * 1.5);
    document.documentElement.style.setProperty('--progress-duration', `${duration}s`);
    
    // Start with 0 and animate to target percentage
    setTimeout(() => {
      const element = document.getElementById(this.progressId) as HTMLElement;
      if (element) {
        element.style.width = '0%';
        element.style.transition = `width var(--progress-duration, ${duration}s) ease-in-out`;
        
        // Force reflow
        void element.offsetWidth;
        
        // Set to target width
        element.style.width = `${targetPercentage}%`;
        
        // Update display percentage during animation
        const startTime = performance.now();
        const updateDisplay = (currentTime: number) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / (duration * 1000), 1);
          this.displayPercentage = Math.round(targetPercentage * progress);
          
          if (progress < 1) {
            requestAnimationFrame(updateDisplay);
          } else {
            this.displayPercentage = targetPercentage;
          }
        };
        
        requestAnimationFrame(updateDisplay);
      }
    }, 10);
  }

  public updateProgress(): void {
    if (this.mode === 'determinate' || this.mode === 'buffer') {
      this.dashOffset = this.circumference * (1 - this.percentage / 100);
  
      // Calculate duration dynamically (scale with percentage)
      const duration = (this.percentage / 100) * 1.5; // Scale transition time
      document.documentElement.style.setProperty('--progress-duration', `${duration}s`);
  
      if (this.type === 'circular') {
        setTimeout(() => {
          const circle = document.getElementById(this.progressId) as HTMLElement;
          if (circle) {
            // Force reflow before applying transition
            circle.style.strokeDashoffset = this.circumference.toString();
            void circle.offsetWidth; // Forces reflow
            circle.style.transition = `stroke-dashoffset var(--progress-duration, 1.5s) ease-in-out`;
            circle.style.strokeDashoffset = this.dashOffset.toString();
          }
        }, 10);
      }
    }
  }
  
  private validateInputs(): void {
    if (this.percentage < 0 || this.percentage > 100) {
      this.errorMessage = 'Percentage value must be between 0 and 100.';
    } else {
      this.errorMessage = '';
    }
  }
  
  // Dynamically update SVG size if not set by the parent component
  private updateSvgSize(): void {
    if (!this.svgSize) { // Only update if svgSize is NOT set by the parent
      this.svgSize = window.innerWidth < 480 ? 50 : window.innerWidth < 768 ? 75 : 100;
    }
  }

  @HostListener('window:resize')
  onResize(): void {
    if (!this.svgSize) {
      this.updateSvgSize();
    }
  }

  writeValue(value: number): void {
    this.percentage = value;
    this.validateInputs();
    this.updateProgress();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
}