.textbox-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  
  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
    }
    
    .subtitle {
      font-size: 1.125rem;
      color: var(--color-text-secondary);
      margin: 0;
    }
  }
  
  .demo-section {
    margin-bottom: 3rem;
    
    h2 {
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid var(--color-border-subtle);
    }
  }
  
  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    
    .demo-item {
      display: flex;
      flex-direction: column;
    }
  }
  
  .demo-form {
    background: var(--color-background-secondary);
    padding: 2rem;
    border-radius: var(--global-radius-lg);
    border: 1px solid var(--color-border-subtle);
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      
      .form-item {
        display: flex;
        flex-direction: column;
        
        &.full-width {
          grid-column: 1 / -1;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .textbox-demo {
    padding: 1rem;
    
    .demo-grid {
      grid-template-columns: 1fr;
    }
    
    .demo-form .form-grid {
      grid-template-columns: 1fr;
    }
  }
} 