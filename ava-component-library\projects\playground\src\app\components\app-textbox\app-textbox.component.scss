/* Standard demo background - simple and clean */

.app-textbox-demo-bg-test{
  background-color: #00b7ff;
  padding: 2rem;
}
.app-textbox-demo-bg {
  // background: #f8f9fa;
  // backdrop-filter: blur(25px);
  // -webkit-backdrop-filter: blur(25px);
  // border-radius: 12px;
  // border: 1px solid #e9ecef;
  background: rgba( 252, 252, 252, 0.54 );
  // box-shadow: rgb(31 38 135, 0.37 );
  backdrop-filter: blur(20px);
  border-radius: 10px;
  border: 1px solid rgba( 255, 255, 255, 0.18 ); 
  min-height: 180px;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

/* Dark theme for standard demo */
html[data-theme="dark"] .app-textbox-demo-bg {
  background: #2a2a2a;
  border: 1px solid #404040;
}

/* Glass effect demo background - vibrant gradient for transparency showcase */
.glass-showcase {
  position: relative;
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #f093fb 50%, 
    #f5576c 75%, 
    #4facfe 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  overflow: hidden;
  animation: backgroundShift 8s ease-in-out infinite;
}

.glass-showcase::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 25%, 
    transparent 50%, 
    rgba(255, 255, 255, 0.1) 75%, 
    transparent 100%);
  animation: shimmer 3s linear infinite;
  pointer-events: none;
}

.glass-showcase::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, 
    rgba(255, 255, 255, 0.2) 0%, 
    transparent 60%);
  pointer-events: none;
}

.glass-showcase ava-textbox {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 300px;
}

/* Light effect demo background - medium dark to show glows clearly */
.light-demo-bg {
  background: #afe90f;
  border-radius: 12px;
  border: 1px solid #4a5568;
  min-height: 180px;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.light-demo-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, 
    rgba(255, 255, 255, 0.05) 0%, 
    transparent 70%);
  border-radius: inherit;
  pointer-events: none;
}

html[data-theme="dark"] .light-demo-bg {
  background: #1a202c;
  border: 1px solid #2d3748;
}

/* Motion demo background - neutral with subtle texture */
.motion-demo-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  border: 1px solid #d1d9e6;
  min-height: 180px;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

html[data-theme="dark"] .motion-demo-bg {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid #4a4a4a;
}

/* Combined effects demo - subtle gradient */
.combined-demo-bg {
background: linear-gradient(135deg, #e1e3eb 0%, #ebf4c4 50%, #ece2d6 100%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 200px;
  padding: 2.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
}

.combined-demo-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%);
  border-radius: inherit;
  pointer-events: none;
}

html[data-theme="dark"] .combined-demo-bg {
  background: linear-gradient(135deg, #2d1b69 0%, #11998e 100%);
}

/* Ensure textbox components are positioned above background effects */
.light-demo-bg ava-textbox,
.motion-demo-bg ava-textbox,
.combined-demo-bg ava-textbox,
.app-textbox-demo-bg ava-textbox {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 300px;
}

/* Animations */
@keyframes backgroundShift {
  0%, 100% { 
    background: linear-gradient(135deg, 
      #667eea 0%, 
      #764ba2 25%, 
      #f093fb 50%, 
      #f5576c 75%, 
      #4facfe 100%);
  }
  33% { 
    background: linear-gradient(135deg, 
      #4facfe 0%, 
      #00f2fe 25%, 
      #667eea 50%, 
      #764ba2 75%, 
      #f093fb 100%);
  }
  66% { 
    background: linear-gradient(135deg, 
      #f093fb 0%, 
      #f5576c 25%, 
      #4facfe 50%, 
      #00f2fe 75%, 
      #667eea 100%);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%); }
  100% { transform: translateX(100%) translateY(100%); }
}
