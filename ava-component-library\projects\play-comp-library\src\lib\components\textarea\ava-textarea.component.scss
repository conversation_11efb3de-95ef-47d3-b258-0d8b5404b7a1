// Root textarea wrapper
.ava-textarea {
  display: flex;
  flex-direction: column;
  gap: var(--textbox-gap);
  width: 100%;

  &--full-width {
    width: 100%;
  }
  &--sm { gap: var(--textbox-gap-sm); }
  &--lg { gap: var(--textbox-gap-lg); }
}

.ava-textarea__label {
  display: block;
  font: var(--textbox-label-font);
  color: var(--textbox-label-color);
  font-weight: var(--textbox-label-weight);
  &--required { &::after { content: ''; } }
}
.ava-textarea__required {
  color: var(--textbox-required-color);
  margin-left: 0.25rem;
}

.ava-textarea__container {
  padding: 0.5rem 1rem;
  position: relative;
  display: flex;
  align-items: flex-start;
  background: var(--textbox-background);
  border: var(--textbox-border-width) solid var(--textbox-border-color);
  border-radius: var(--textbox-border-radius);
  transition: border-color 0.2s, box-shadow 0.2s;
  flex-direction: column;

  &:hover { border-color: var(--textbox-border-hover-color); }
  .ava-textarea--focused & {
    border-color: var(--textbox-border-focus-color);
    box-shadow: var(--textbox-focus-shadow);
  }
  .ava-textarea--error & { border-color: var(--textbox-border-error-color); }
  .ava-textarea--disabled & {
    background: var(--textbox-background-disabled);
    border-color: var(--textbox-border-disabled-color);
    cursor: not-allowed;
  }
  .ava-textarea--readonly & {
    background: var(--textbox-background-readonly);
    border-color: var(--textbox-border-readonly-color);
  }
}

.ava-textarea__input {
  // flex: 1; // Removed to allow textarea resizing
  border: none;
  outline: none;
  background: transparent;
  font: var(--textbox-input-font);
  color: var(--textbox-input-color);
  padding: var(--textbox-input-padding);
  min-height: var(--textbox-textarea-min-height);
  line-height: 1.5;
  resize: vertical;
  font-weight: var(--textbox-label-weight);
  width: 100%;
  box-sizing: border-box;

  &::placeholder {
    color: var(--textbox-placeholder-color);
    opacity: 1;
  }
  &:disabled {
    color: var(--textbox-input-disabled-color);
    cursor: not-allowed;
  }
  &:read-only {
    color: var(--textbox-input-readonly-color);
    cursor: default;
  }
  // Size variants
  &--sm {
    padding: var(--textbox-input-padding-sm);
    min-height: var(--textbox-textarea-min-height-sm, var(--textbox-input-min-height-sm));
    font-size: var(--textbox-input-font-size-sm);
  }
  &--lg {
    padding: var(--textbox-input-padding-lg);
    min-height: var(--textbox-textarea-min-height-lg, var(--textbox-input-min-height-lg));
    font-size: var(--textbox-input-font-size-lg);
  }
  &--full-width { width: 100%; }
}

.ava-textarea__iconsbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  margin-top: 0.25rem;
  margin-bottom: 0;
}
.ava-textarea__iconsbar-spacer {
  flex: 1 1 auto;
}
.ava-textarea__icons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.ava-textarea__icons--start {
  justify-content: flex-start;
}
.ava-textarea__icons--end {
  justify-content: flex-end;
}

.ava-textarea__icons ava-icon {
  cursor: pointer;
  transition: color 0.18s;
}
.ava-textarea__icons ava-icon:active { opacity: 0.7; }

.ava-textarea__prefix,
.ava-textarea__suffix {
  display: flex;
  align-items: center;
  padding: var(--textbox-affix-padding);
  color: var(--textbox-affix-color);
  font-size: var(--textbox-affix-font-size);
  background: var(--textbox-affix-background);
  border-radius: var(--textbox-affix-border-radius);
  .ava-textarea--disabled & {
    color: var(--textbox-affix-disabled-color);
    background: var(--textbox-affix-disabled-background);
  }
}
.ava-textarea__prefix {
  border-top-left-radius: var(--textbox-border-radius);
  border-bottom-left-radius: var(--textbox-border-radius);
}
.ava-textarea__suffix {
  border-top-right-radius: var(--textbox-border-radius);
  border-bottom-right-radius: var(--textbox-border-radius);
}

.ava-textarea__error {
  display: flex;
  align-items: flex-start;
  gap: var(--textbox-error-gap);
  color: var(--textbox-error-color);
  font-size: var(--textbox-error-font-size);
  line-height: 1.4;
}
.ava-textarea__error-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}
.ava-textarea__error-text { flex: 1; }

.ava-textarea__helper {
  display: flex;
  align-items: flex-start;
  gap: var(--textbox-helper-gap);
  color: var(--textbox-helper-color);
  font-size: var(--textbox-helper-font-size);
  line-height: 1.4;
}
.ava-textarea__helper-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}
.ava-textarea__helper-text { flex: 1; }

.ava-textarea--primary .ava-textarea__container {
  border-color: var(--textbox-border-primary-color);
}
.ava-textarea--success .ava-textarea__container {
  border-color: var(--textbox-border-success-color);
}
.ava-textarea--warning .ava-textarea__container {
  border-color: var(--textbox-border-warning-color);
}
.ava-textarea--info .ava-textarea__container {
  border-color: var(--textbox-border-info-color);
} 