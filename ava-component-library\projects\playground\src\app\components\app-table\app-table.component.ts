// import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
// import { CommonModule } from '@angular/common';
// import { FormsModule } from '@angular/forms';
// import { TableColumn, TableComponent } from '../../../../../play-comp-library/src/lib/components/table/table.component';

// @Component({
//   selector: 'app-table-documentation',
//   imports: [CommonModule, FormsModule,TableComponent],
//   templateUrl: './app-table.component.html',
//   styleUrls: ['./app-table.component.scss'],
//   encapsulation: ViewEncapsulation.None
// })
// export class AppTableComponent {
//      table1Columns: TableColumn[] = [
//     { key: 'modal', label: 'Model', type: 'text' },
//     { key: 'latency', label: 'Latency', type: 'text' },
//     { key: 'status', label: 'Status', type: 'status' },
//     { key: 'action', label: 'Actions', type: 'actions' }
//   ];

//     table1Data = [
//     {
//       modal: 'Model A',
//       latency: '100ms',
//       tags: [
//         { label: 'Active', color: 'primary', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model B',
//       latency: '150ms',
//       tags: [
//         { label: 'Inactive', color: 'warning', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model C',
//       latency: '200ms',
//       tags: [
//         { label: 'Active', color: 'primary', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model D',
//       latency: '250ms',
//       tags: [
//         { label: 'Error', color: 'error', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model E',
//       latency: '300ms',
//       tags: [
//         { label: 'Completed', color: 'success', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model F',
//       latency: '350ms',
//       tags: [
//         { label: 'Inactive', color: 'warning', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model G',
//       latency: '400ms',
//       tags: [
//         { label: 'Error', color: 'error', variant: 'filled' }
//       ],
//      actions: [ 'check','X','Trash','Settings']
//     },
//     {
//       modal: 'Model H',
//       latency: '450ms',
//       tags: [
//         { label: 'Active', color: 'primary', variant: 'filled' }
//       ],
//       actions: [ 'check','X','Trash','Settings']
//     }
//   ];


//   // Configuration and data for the second table
//   table2Columns: TableColumn[] = [
//     { key: 'productId', label: 'Product ID', type: 'text' },
//     { key: 'productName', label: 'Product Name', type: 'text' },
//     { key: 'price', label: 'Price', type: 'text' },
//     { key: 'stock', label: 'Stock', type: 'text' }
//   ];

//   table2Data = [
//     {
//       productId: 'P001',
//       productName: 'Laptop',
//       price: '$999',
//       stock: 'In Stock'
//     },
//     {
//       productId: 'P002',
//       productName: 'Smartphone',
//       price: '$699',
//       stock: 'Out of Stock'
//     },
//      {
//       productId: 'P003',
//       productName: 'phone',
//       price: '$399',
//       stock: 'Stock'
//     }
//   ];


//   orderColumns = [
//   { key: 'orderId', label: 'Order ID', type: 'text' },
//   { key: 'customer', label: 'Customer', type: 'text' },
//   { key: 'amount', label: 'Amount', type: 'text' },
//   { key: 'status', label: 'Status', type: 'status' }
// ];

// orderData = [
//   {
//     orderId: 'A001',
//     customer: 'Alice',
//     amount: '$120.00',
//     tags: [{ label: 'Completed', color: 'success', variant: 'filled' }]
//   },
//   {
//     orderId: 'A002',
//     customer: 'Bob',
//     amount: '$80.00',
//     tags: [{ label: 'Pending', color: 'warning', variant: 'filled' }]
//   },
//   {
//     orderId: 'A003',
//     customer: 'phone',
//     amount: '$50.00',
//     tags: [{ label: 'Active', color: 'primary', variant: 'filled' }]
//   }
// ];
// }


import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableColumn, TableComponent } from '../../../../../play-comp-library/src/lib/components/table/table.component';

interface TableDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-table-documentation',
  imports: [CommonModule, FormsModule, TableComponent],
  templateUrl: './app-table.component.html',
  styleUrls: ['./app-table.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppTableComponent {
  sections: TableDocSection[] = [
    {
      title: 'Basic Table',
      description: 'A simple table with text columns.',
      showCode: false,
    }
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'columns',
      type: 'TableColumn[]',
      default: '[]',
      description: 'Defines the columns of the table.'
    },
    {
      name: 'data',
      type: 'any[]',
      default: '[]',
      description: 'The data to display in the table.'
    },
    {
      name: 'rowActions',
      type: 'string[]',
      default: '[]',
      description: 'Actions available for each row.'
    },
    {
      name: 'statusTags',
      type: 'any[]',
      default: '[]',
      description: 'Tags to display status in a column.'
    },
  ];

  table1Columns: TableColumn[] = [
    { key: 'modal', label: 'Model', type: 'text' ,bgColor: '#f0f0f0', textColor: '#333'},
    { key: 'latency', label: 'Latency', type: 'text',bgColor: '#f0f0f0', textColor: '#333' },
    { key: 'status', label: 'Status', type: 'status',bgColor: '#f0f0f0', textColor: '#333' },
    { key: 'action', label: 'Actions', type: 'actions',bgColor: '#f0f0f0', textColor: '#333' }
  ];

  table1Data = [
    {
      modal: 'Model A',
      latency: '100ms',
      tags: [
        { label: 'Active', color: 'primary', variant: 'filled' }
      ],
     actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model B',
      latency: '150ms',
      tags: [
        { label: 'Inactive', color: 'warning', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model C',
      latency: '200ms',
      tags: [
        { label: 'Active', color: 'primary', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model D',
      latency: '250ms',
      tags: [
        { label: 'Error', color: 'error', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model E',
      latency: '300ms',
      tags: [
        { label: 'Completed', color: 'success', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model F',
      latency: '350ms',
      tags: [
        { label: 'Inactive', color: 'warning', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model G',
      latency: '400ms',
      tags: [
        { label: 'Error', color: 'error', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    },
    {
      modal: 'Model H',
      latency: '450ms',
      tags: [
        { label: 'Active', color: 'primary', variant: 'filled' }
      ],
       actions: [
      { icon: 'check', iconColor: 'blue' },
      { icon: 'x', iconColor: 'red' },
      { icon: 'trash', iconColor: 'green' },
      { icon: 'settings', iconColor: 'orange' }
     ]
    }
  ];

  table2Columns: TableColumn[] = [
    { key: 'productId', label: 'Product ID', type: 'text' },
    { key: 'productName', label: 'Product Name', type: 'text' },
    { key: 'price', label: 'Price', type: 'text' },
    { key: 'stock', label: 'Stock', type: 'text' }
  ];

  table2Data = [
    {
      productId: 'P001',
      productName: 'Laptop',
      price: '$999',
      stock: 'In Stock'
    },
    {
      productId: 'P002',
      productName: 'Smartphone',
      price: '$699',
      stock: 'Out of Stock'
    },
    {
      productId: 'P003',
      productName: 'phone',
      price: '$399',
      stock: 'Stock'
    }
  ];

  orderColumns = [
    { key: 'orderId', label: 'Order ID', type: 'text' },
    { key: 'customer', label: 'Customer', type: 'text' },
    { key: 'amount', label: 'Amount', type: 'text' },
    { key: 'status', label: 'Status', type: 'status' }
  ];

  orderData = [
    {
      orderId: 'A001',
      customer: 'Alice',
      amount: '$120.00',
      tags: [{ label: 'Completed', color: 'success', variant: 'filled' }]
    },
    {
      orderId: 'A002',
      customer: 'Bob',
      amount: '$80.00',
      tags: [{ label: 'Pending', color: 'warning', variant: 'filled' }]
    },
    {
      orderId: 'A003',
      customer: 'phone',
      amount: '$50.00',
      tags: [{ label: 'Active', color: 'primary', variant: 'filled' }]
    }
  ];

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getTableCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'basic table': `
<ava-table [columns]="table2Columns" [data]="table2Data"></ava-table>
`,
      'table with status': `
<ava-table [columns]="orderColumns" [data]="orderData"></ava-table>
`,
      'table with actions': `
<ava-table [columns]
`,
    };
    return examples[sectionTitle.toLowerCase()] || '';
  }

  copyCode(sectionTitle: string): void {
    const code = this.getTableCode(sectionTitle);
    const textarea = document.createElement('textarea');
    textarea.value = code;
    textarea.style.position = 'fixed';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      console.log('Code copied to clipboard');
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
    document.body.removeChild(textarea);
  }
}