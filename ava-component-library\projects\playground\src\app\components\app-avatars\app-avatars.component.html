<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Avatars Component</h1>
        <p class="description">
          A versatile avatar component that displays user images with different shapes, sizes, and badges. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} AvatarsComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="large"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                  >
                  </ava-avatars>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Large Avatars'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="large"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                    badgeState="high-priority"
                    badgeSize="large"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="large"
                    shape="square"
                    [imageUrl]="sampleImageUrl"
                    badgeState="high-priority"
                    badgeSize="large"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Medium Avatars'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="medium"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                    badgeState="medium-priority"
                    badgeSize="medium"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="medium"
                    shape="square"
                    [imageUrl]="sampleImageUrl"
                    badgeState="medium-priority"
                    badgeSize="medium"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Small Avatars'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="small"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                    badgeState="low-priority"
                    badgeSize="small"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="small"
                    shape="square"
                    [imageUrl]="sampleImageUrl"
                    badgeState="low-priority"
                    badgeSize="small"
                    [badgeCount]="1">
                  </ava-avatars>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Avatars with Text'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="large"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                    badgeState="information"
                    badgeSize="large"
                    [badgeCount]="1"
                    statusText="Online"
                    profileText="ascendion.hyderabad">
                  </ava-avatars>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    [imageUrl]="sampleImageUrl"
                    profileText="John Doe">
                  </ava-avatars>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Avatars with Events'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-avatars
                    size="large"
                    shape="pill"
                    [imageUrl]="sampleImageUrl"
                    badgeState="information"
                    badgeSize="large"
                    [badgeCount]="7"
                    statusText="Online"
                    profileText="ascendion.hyderabad"
                    (click)="handleIconClick()">
                  </ava-avatars>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getAvatarCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- Icon for copy button -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
