import { Component, signal, ViewEncapsulation, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SliderComponent } from "../../../../../play-comp-library/src/lib/components/slider/slider.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";

interface SliderDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-slider',
  standalone: true,
  imports: [
    CommonModule,
    SliderComponent,
    IconsComponent
  ],
  templateUrl: './app-slider.component.html',
  styleUrl: './app-slider.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppSliderComponent {

  // Documentation sections
  sections: SliderDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple slider with default configuration.',
      showCode: false,
    },
    {
      title: 'Slider Variants',
      description: 'Different slider variants including default, primary.',
      showCode: false,
    },
    {
      title: 'Custom Range',
      description: 'Slider with custom minimum, maximum, and step values.',
      showCode: false,
    },
    {
      title: 'Responsive Behavior',
      description: 'Slider that adapts to different screen sizes and touch devices.',
      showCode: false,
    },
    {
      title: 'Disabled State',
      description: 'Slider in disabled state with different variants.',
      showCode: false,
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'label',
      type: 'string',
      default: '"Slider"',
      description: 'Label text for the slider.',
    },
    {
      name: 'min',
      type: 'number',
      default: '0',
      description: 'Minimum value of the slider.',
    },
    {
      name: 'max',
      type: 'number',
      default: '100',
      description: 'Maximum value of the slider.',
    },
    {
      name: 'step',
      type: 'number',
      default: '1',
      description: 'Step increment value for the slider.',
    },
    {
      name: 'value',
      type: 'WritableSignal<number>',
      default: 'signal(50)',
      description: 'Current value of the slider.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Whether the slider is disabled.',
    },
    {
      name: 'variant',
      type: "'default' | 'primary'",
      default: 'default',
      description: 'Visual style variant of the slider.',
    },
    {
      name: 'tooltipPosition',
      type: "'top' | 'bottom' | 'none'",
      default: 'top',
      description: 'Position of the value tooltip.',
    },
    {
      name: 'customTickValues',
      type: 'number[]',
      default: '[]',
      description: 'Custom values for tick marks.',
    },
    {
      name: 'formatLabel',
      type: '(value: number) => string',
      default: 'value => value.toString()',
      description: 'Function to format label values.',
    },

    {
      name: 'touchTargetSize',
      type: 'string',
      default: '44px',
      description: 'Minimum touch target size for better accessibility.',
    }
  ];

  // Example state management
  basicValue: WritableSignal<number> = signal(50);
  defaultVariantValue: WritableSignal<number> = signal(0);
  customRangeValue: WritableSignal<number> = signal(25);
  temperatureValue: WritableSignal<number> = signal(20);
  volumeValue: WritableSignal<number> = signal(50);
  responsiveValue: WritableSignal<number> = signal(60);
  disabledValue: WritableSignal<number> = signal(30);

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onSliderChange(value: number): void {
    console.log('Slider value:', value);
  }

  formatTemperature(value: number): string {
    return `${value}°C`;
  }

  formatVolume(value: number): string {
    const minDb = -60;
    const maxDb = 0;
    const dbValue = Math.round(minDb + (Math.pow(value / 100, 2) * (maxDb - minDb)));
    return `${dbValue}dB`;
  }

  onDragStart(): void {
    console.log('Drag started');
  }

  onDragEnd(): void {
    console.log('Drag ended');
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `// Basic slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="slider-container">
      <awe-slider
        label="Basic Slider"
        [value]="value"
        (valueChange)="onValueChange($event)"
      ></awe-slider>
    </div>
  \`,
  styles: [\`
    .slider-container {
      padding: 1rem;
    }
  \`]
})
export class BasicSliderComponent {
  value = signal(50);

  onValueChange(value: number): void {
    console.log('Value changed:', value);
  }
}`,

      'slider variants': `// Slider variants example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-slider-variants',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="variants-container">
      <awe-slider
        label="Default Variant"
        [value]="defaultValue"
        variant="default"
        tooltipPosition="top"
      ></awe-slider>

      <awe-slider
        label="Primary Variant"
        [value]="primaryValue"
        variant="primary"
        tooltipPosition="bottom"
      ></awe-slider>
    </div>
  \`,
  styles: [\`
    .variants-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  \`]
})
export class SliderVariantsComponent {
  defaultValue = signal(30);
  primaryValue = signal(50);
}`,

      'custom range': `// Custom range example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-custom-range-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="custom-range-container">
      <awe-slider
        label="Temperature (°C)"
        [value]="temperature"
        [min]="0"
        [max]="40"
        [step]="0.5"
        [formatLabel]="formatTemperature"
        tooltipPosition="top"
      ></awe-slider>

      <awe-slider
        label="Volume Control (dB)"
        [value]="volume"
        [min]="0"
        [max]="100"
        [step]="1"
        [formatLabel]="formatVolume"
        variant="primary"
        tooltipPosition="bottom"
      ></awe-slider>
    </div>
  \`,
  styles: [\`
    .custom-range-container {
      padding: 1rem;
    }
  \`]
})
export class CustomRangeSliderComponent {
  temperature = signal(20);
  volume = signal(50);

  formatTemperature(value: number): string {
    return \`\${value}°C\`;
  }

  formatVolume(value: number): string {
    const minDb = -60;
    const maxDb = 0;
    const dbValue = Math.round(minDb + (Math.pow(value / 100, 2) * (maxDb - minDb)));
    return \`\${dbValue}dB\`;
  }
}`,

      'responsive behavior': `// Responsive behavior example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-responsive-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="responsive-container">
      <awe-slider
        label="Responsive Slider"
        [value]="value"
        mobileSize="small"
        tabletSize="medium"
        desktopSize="large"
        touchTargetSize="44px"
        [showTicks]="true"
        [customTickValues]="[0, 25, 50, 75, 100]"
        variant="primary"
        (dragStart)="onDragStart()"
        (dragEnd)="onDragEnd()"
      ></awe-slider>
    </div>
  \`,
  styles: [\`
    .responsive-container {
      padding: 1rem;
    }
  \`]
})
export class ResponsiveSliderComponent {
  value = signal(60);

  onDragStart(): void {
    console.log('Drag started');
  }

  onDragEnd(): void {
    console.log('Drag ended');
  }
}`,

      'disabled state': `// Disabled state example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-disabled-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="disabled-container">
      <awe-slider
        label="Disabled Default"
        [value]="value"
        [disabled]="true"
        variant="default"
      ></awe-slider>

      <awe-slider
        label="Disabled Custom Range"
        [value]="customRangeValue"
        [disabled]="true"
        [min]="0"
        [max]="200"
        [step]="10"
        variant="primary"
      ></awe-slider>
    </div>
  \`,
  styles: [\`
    .disabled-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  \`]
})
export class DisabledSliderComponent {
  value = signal(30);
  customRangeValue = signal(120);
}`
    };

    return examples[section.toLowerCase()] || '';
  }
}
