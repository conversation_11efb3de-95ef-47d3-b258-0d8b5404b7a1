<div class="ava-sidebar-container">
  <!-- Main Sidebar -->

  <div
    class="ava-sidebar"
    [style.width]="sidebarWidth"
    [style.height]="height"
    [class.collapsed]="collapsed"
  >
    <!-- Header Section -->

    <div class="sidebar-header" *ngIf="showHeader">
      <div class="header-content" *ngIf="!collapsed">
        <ng-content select="[slot=header]"></ng-content>
      </div>

      <!-- Inside Button Variant -->

      <div
        class="header-controls"
        *ngIf="showCollapseButton && buttonVariant === 'inside'"
      >
        <ava-button
          class="collapse-btn"
          (click)="toggleCollapse()"
          iconPosition="only"
          [iconName]="collapsed ? 'ArrowRight' : 'ArrowLeft'"
        >
        </ava-button>
      </div>
    </div>

    <!-- Main Content Section -->

    <div class="sidebar-content">
      <ng-content select="[slot=content]"></ng-content>
    </div>

    <!-- Footer Section -->

    <div class="sidebar-footer" *ngIf="showFooter">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>

  <!-- Outside Button Variant - Hover Area -->

  <div
    class="hover-area"
    [style.width]="hoverAreaWidth"
    [style.height]="height"
    *ngIf="showCollapseButton && buttonVariant === 'outside'"
  >
    <div class="hover-area-content">
      <ava-button
        class="collapse-btn"
        (click)="toggleCollapse()"
        iconPosition="only"
        [iconName]="collapsed ? 'ArrowRight' : 'ArrowLeft'"
      >
      </ava-button>
    </div>
  </div>
</div>
