<div class="ava-sidebar" [style.width]="sidebarWidth" [class.collapsed]="collapsed">
    <!-- Header Section with Collapse Button -->
    <div class="sidebar-header">
        <div class="header-content" *ngIf="!collapsed">
            <ng-content select="[slot=header]"></ng-content>
        </div>
        <div class="header-controls" *ngIf="showCollapseButton">
            <ava-button
                class="collapse-btn"
                (click)="toggleCollapse()"
                iconPosition="only"
                [iconName]="collapsed ? 'ArrowRight' : 'ArrowLeft'">
            </ava-button>
        </div>
    </div>
    
    <!-- Main Content Section -->
    <div class="sidebar-content">
        <ng-content select="[slot=content]"></ng-content>
    </div>
    
    <!-- Footer Section -->
    <div class="sidebar-footer">
        <ng-content select="[slot=footer]"></ng-content>
    </div>
</div>