/**
 * =========================================================================
 * Play+ Design System: Checkbox Component Tokens
 *
 * Component-specific semantic tokens for checkbox elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for checkbox styling.
 * =========================================================================
 */

:root {
  /* --- Checkbox Base --- */
  --checkbox-size: 20px;
  --checkbox-border-radius: var(--global-radius-sm);
  --checkbox-transition: var(--motion-pattern-fade);
  --checkbox-cursor: pointer;
  --checkbox-cursor-disabled: not-allowed;

  /* --- Checkbox Box --- */
  --checkbox-box-background: var(--color-background-primary);
  --checkbox-box-background-disabled: var(--color-surface-disabled);
  --checkbox-box-border: 2px solid var(--color-border-default);
  --checkbox-box-border-hover: 2px solid var(--color-border-focus);
  --checkbox-box-border-focus: 2px solid var(--color-brand-primary);
  --checkbox-box-border-disabled: 2px solid var(--color-border-disabled);
  --checkbox-box-border-radius: var(--checkbox-border-radius);

  /* --- Checkbox Checked State --- */
  --checkbox-box-checked-background: var(--color-brand-primary);
  --checkbox-box-checked-border: 2px solid var(--color-brand-primary);
  --checkbox-box-checked-border-hover: 2px solid var(--color-brand-primary-hover);
  --checkbox-box-checked-border-focus: 2px solid var(--color-brand-primary);
  --checkbox-box-checked-color: var(--color-text-on-brand);

  /* --- Checkbox Indeterminate State --- */
  --checkbox-box-indeterminate-background: var(--color-brand-primary);
  --checkbox-box-indeterminate-border: 2px solid var(--color-brand-primary);
  --checkbox-box-indeterminate-color: var(--color-text-on-brand);

  /* --- Checkbox Icon --- */
  --checkbox-icon-size: 14px;
  --checkbox-icon-color: var(--color-text-on-brand);
  --checkbox-icon-color-disabled: var(--color-text-disabled);
  --checkbox-icon-stroke-width: 2px;

  /* --- Checkbox Label --- */
  --checkbox-label-font: var(--font-body-2);
  --checkbox-label-color: var(--color-text-primary);
  --checkbox-label-color-disabled: var(--color-text-disabled);
  --checkbox-label-margin-left: var(--global-spacing-3);
  --checkbox-label-cursor: pointer;
  --checkbox-label-cursor-disabled: not-allowed;

  /* --- Checkbox Sizes --- */
  --checkbox-size-sm: 16px;
  --checkbox-size-sm-icon: 12px;
  --checkbox-size-sm-label: var(--font-label);

  --checkbox-size-md: 20px;
  --checkbox-size-md-icon: 14px;
  --checkbox-size-md-label: var(--font-body-2);

  --checkbox-size-lg: 24px;
  --checkbox-size-lg-icon: 16px;
  --checkbox-size-lg-label: var(--font-body-1);

  /* --- Checkbox States --- */
  --checkbox-focus-ring: var(--accessibility-focus-ring-width) var(--accessibility-focus-ring-style) var(--accessibility-focus-ring-color);
  --checkbox-focus-ring-offset: var(--accessibility-focus-ring-offset);
  --checkbox-hover-transform: scale(1.05);
  --checkbox-active-transform: scale(0.95);

  /* --- Checkbox Group --- */
  --checkbox-group-gap: var(--global-spacing-4);
  --checkbox-group-direction: column;
  --checkbox-group-align-items: flex-start;
} 