import { CommonModule } from '@angular/common';
import { Component, ViewEncapsulation } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import {
  ButtonComponent,
  IconComponent,
} from '../../../../../play-comp-library/src/public-api';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';

interface SidebarDocSection {
  title: string;
  description: string;
  showCode: boolean;
}
export interface SidebarItem {
  id: string;
  icon: string;
  text: string;
  route?: string;
  active?: boolean;
}

@Component({
  selector: 'app-app-sidebar',
  imports: [SidebarComponent, RouterModule, IconComponent, CommonModule],
  templateUrl: './app-sidebar.component.html',
  styleUrls: ['./app-sidebar.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppSidebarComponent {
  isCollapsed = false;

  sidebarItems: SidebarItem[] = [
    {
      id: '1',
      icon: 'home',
      text: 'Dashboard',
      route: '/dashboard',
      active: true,
    },
    { id: '2', icon: 'home', text: 'Users', route: '/users' },
    { id: '3', icon: 'home', text: 'Settings', route: '/settings' },
    { id: '4', icon: 'home', text: 'Analytics', route: '/analytics' },
    { id: '5', icon: 'home', text: 'Messages', route: '/messages' },
    { id: '6', icon: 'home', text: 'Calendar', route: '/calendar' },
    { id: '7', icon: 'home', text: 'Files', route: '/files' },
    { id: '8', icon: 'home', text: 'Help', route: '/help' },
  ];

  constructor(private router: Router) {}

  onCollapseToggle(isCollapsed: boolean): void {
    this.isCollapsed = isCollapsed;
    console.log('Sidebar collapsed:', isCollapsed);
  }

  onItemClick(item: SidebarItem): void {
    // Update active state
    this.sidebarItems.forEach((i) => (i.active = false));
    item.active = true;

    // Navigate if route exists
    if (item.route) {
      this.router.navigate([item.route]);
    }
  }
  basicSidebarItems: SidebarItem[] = [
    { id: '1', icon: 'home', text: 'Home', route: '/home', active: true },
    { id: '2', icon: 'home', text: 'Profile', route: '/profile' },
    { id: '3', icon: 'home', text: 'Settings', route: '/settings' },
    { id: '4', icon: 'home', text: 'About', route: '/about' },
  ];

  premiumSidebarItems: SidebarItem[] = [
    {
      id: '1',
      icon: 'home',
      text: 'Dashboard',
      route: '/dashboard',
      active: true,
    },
    { id: '2', icon: 'home', text: 'Analytics', route: '/analytics' },
    { id: '3', icon: 'home', text: 'Reports', route: '/reports' },
    { id: '4', icon: 'home', text: 'Users', route: '/users' },
    { id: '5', icon: 'home', text: 'Messages', route: '/messages' },
    { id: '6', icon: 'home', text: 'Calendar', route: '/calendar' },
    { id: '7', icon: 'home', text: 'Files', route: '/files' },
    { id: '8', icon: 'home', text: 'Support', route: '/support' },
  ];
  isBasicCollapsed = false;
  onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }


  onBasicItemClick(item: SidebarItem): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;

    if (item.route) {
      this.router.navigate([item.route]);
    }
  }

 

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }
  sections: SidebarDocSection[] = [
    {
      title: 'Basic Sidebar',
      description:
        'This is a straightforward sidebar with a simple layout.',
      showCode: false,
    },
    {
      title: 'Collapsible Sidebar',
      description:
        'This sidebar has the added functionality of being collapsible, allowing it to expand and collapse to save screen space.',
      showCode: false,
    },
   
  ];

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Example code snippets
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic sidebar': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-sidebar',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
    <div class="multi-sidebar-container">
                <!-- Basic Sidebar -->
                <div class="basic-sidebar-wrapper">
                  <h3>Basic Sidebar</h3>
                  <ava-sidebar 
                    class="basic-sidebar-theme"
                    width="280px" 
                    collapsedWidth="65px" 
                    [class.basic-collapsed]="isBasicCollapsed"
                    (collapseToggle)="onBasicCollapseToggle($event)">
                    
                    <!-- Basic Header Content -->
                    <div slot="header" class="basic-demo-header">
                      <div class="basic-logo">
                        <ava-icon iconName="home"></ava-icon>
                        <span class="basic-logo-text">Simple App</span>
                      </div>
                    </div>
                    
                    <!-- Basic Main Content -->
                    <div slot="content">
                      <div class="basic-nav-section">
                        <div class="basic-nav-section-title">Navigation</div>
                        <div *ngFor="let item of basicSidebarItems" 
                             class="basic-nav-item"
                             [class.basic-active]="item.active"
                             (click)="onBasicItemClick(item)">
                          <ava-icon [iconName]="item.icon" class="basic-nav-icon"></ava-icon>
                          <span class="basic-nav-text">{{ item.text }}</span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Basic Footer Content -->
                    <div slot="footer" class="basic-demo-footer">
                      <div class="basic-user-profile">
                        <ava-icon iconName="home" class="basic-profile-avatar"></ava-icon>
                        <div class="basic-profile-info">
                          <span class="basic-profile-name">Jane Smith</span>
                          <span class="basic-profile-role">User</span>
                        </div>
                      </div>
                    </div>
                  </ava-sidebar>
                </div>
              </div>     
  \`
})
export class BasicSidebarComponent {
  isCollapsed = false;
  sidebarItems = [
    { id: '1', icon: 'home', text: 'Dashboard', route: '/dashboard', active: true },
    { id: '2', icon: 'home', text: 'Users', route: '/users' },
    { id: '3', icon: 'home', text: 'Settings', route: '/settings' }
  ];

  onCollapseToggle(isCollapsed: boolean): void {
    this.isCollapsed = isCollapsed;
    console.log('Sidebar collapsed:', isCollapsed);
  }

  onItemClick(item: any): void {
    this.sidebarItems.forEach(i => i.active = false);
    item.active = true;
    if (item.route) {
      // Navigate to the route
    }
  }
}`,
      'collapsible sidebar': `
import { Component } from '@angular/core';
import { SidebarComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-collapsible-sidebar',
  standalone: true,
  imports: [SidebarComponent],
  template: \`
     <div class="sidebar-demo">
                <h3>Collapsible Sidebar</h3>
            
                <ava-sidebar 
                    width="300px" 
                    collapsedWidth="60px" 
                    [showCollapseButton]="true"
                    [class.collapsed]="isCollapsed"
                    (collapseToggle)="onCollapseToggle($event)">
                    
                    <!-- Header Content -->
                    <div slot="header" class="demo-header">
                        <div class="logo">
                            <ava-icon iconName="home"></ava-icon>
                            <span class="logo-text">Dashboard</span>
                        </div>
                    </div>
                    
                    <!-- Main Content -->
                    <div slot="content">
                        <div class="nav-section">
                            <div class="nav-section-title">Main</div>
                            <div *ngFor="let item of sidebarItems" 
                                 class="nav-item"
                                 [class.active]="item.active"
                                 (click)="onItemClick(item)">
                                <ava-icon [iconName]="item.icon" class="nav-icon"></ava-icon>
                                <span class="nav-text">{{ item.text }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer Content -->
                    <div slot="footer" class="demo-footer">
                        <div class="user-profile">
                            <ava-icon iconName="home" class="profile-avatar"></ava-icon>
                            <div class="profile-info">
                                <span class="profile-name">John Doe</span>
                                <span class="profile-role">Administrator</span>
                            </div>
                        </div>
                    </div>
                </ava-sidebar>
            </div> 
  \`
})
export class CollapsibleSidebarComponent {
  
  premiumSidebarItems: SidebarItem[] = [
    {
      id: '1',
      icon: 'home',
      text: 'Dashboard',
      route: '/dashboard',
      active: true,
    },
    { id: '2', icon: 'home', text: 'Analytics', route: '/analytics' },
    { id: '3', icon: 'home', text: 'Reports', route: '/reports' },
    { id: '4', icon: 'home', text: 'Users', route: '/users' },
    { id: '5', icon: 'home', text: 'Messages', route: '/messages' },
    { id: '6', icon: 'home', text: 'Calendar', route: '/calendar' },
    { id: '7', icon: 'home', text: 'Files', route: '/files' },
    { id: '8', icon: 'home', text: 'Support', route: '/support' },
  ];
  
  onItemClick(item: SidebarItem): void {
    // Update active state
    this.sidebarItems.forEach((i) => (i.active = false));
    item.active = true;

    // Navigate if route exists
    if (item.route) {
      this.router.navigate([item.route]);
    }
  }`,

};

    return examples[section.toLowerCase()] || '';
  }
}
