<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Radio Button Component</h1>
        <p class="description">
          A versatile radio button component that supports multiple configurations,
          including disabled states, animations, and custom styling.
          Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} RadioButtonComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-radio-button
                    [options]="basicOptions()"
                    (selectionChange)="onSelectionChange($event)"
                  ></awe-radio-button>
                </div>
              </div>
            </ng-container>

            <!-- Radio Button Variants -->
            <ng-container *ngSwitchCase="'Radio Button Variants'">
              <div class="row g-3">
                <div class="col-12">
                  <h3>Disabled Options</h3>
                  <awe-radio-button
                    [options]="disabledOptions()"
                  ></awe-radio-button>
                </div>
                <div class="col-12">
                  <h3>Animated Options</h3>
                  <awe-radio-button
                    [options]="animatedOptions()"
                  ></awe-radio-button>
                </div>
              </div>
            </ng-container>

            <!-- Orientation -->
            <ng-container *ngSwitchCase="'Orientation'">
              <div class="row g-3">
                <div class="col-12">
                  <h3>Vertical Layout</h3>
                  <awe-radio-button
                    [options]="verticalOptions()"
                    orientation="vertical"
                  ></awe-radio-button>
                  <h3>Horizontal Layout</h3>
                   <awe-radio-button
                    [options]="horizontalOptions()"
                    orientation="horizontal"
                  ></awe-radio-button>
                </div>
              </div>
            </ng-container>

            <!-- Customization -->
            <ng-container *ngSwitchCase="'Customization'">
              <div class="row g-3">
                <div class="col-12">
                  <h3>Theme Customization</h3>
                  <awe-radio-button
                    [options]="customizationOptions()"
                    name="theme-selector"
                  ></awe-radio-button>
                </div>
              </div>
            </ng-container>

            <!-- Accessibility -->
            <ng-container *ngSwitchCase="'Accessibility'">
              <div class="row g-3">
                <div class="col-12">
                  <h3>Accessibility Options</h3>
                  <awe-radio-button
                    [options]="accessibilityOptions()"
                    name="accessibility-settings"
                  ></awe-radio-button>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><code>selectionChange</code></td>
          <td><code>EventEmitter&lt;string&gt;</code></td>
          <td>Emitted when a radio button is selected</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
