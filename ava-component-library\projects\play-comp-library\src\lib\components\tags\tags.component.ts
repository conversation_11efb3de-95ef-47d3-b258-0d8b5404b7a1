import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

/**
 * AvaTagComponent: Modern, accessible, and highly customizable tag/chip component.
 * Supports filled/outlined, color variants, pill/rect, removable, icons, avatars, sizes, and custom styles.
 */
@Component({
  selector: 'ava-tag',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss'],
  standalone: true,
  imports: [CommonModule, IconComponent],
})
export class AvaTagComponent {
  /** Tag label text */
  @Input() label!: string;
  /** Color variant */
  @Input() color: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | 'custom' = 'default';
  /** Outlined or filled */
  @Input() variant: 'filled' | 'outlined' = 'filled';
  /** Tag size */
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  /** Pill/rounded shape */
  @Input() pill = false;
  /** Removable (shows close icon) */
  @Input() removable = false;
  /** Disabled state */
  @Input() disabled = false;
  /** Icon name (ava-icon) */
  @Input() icon?: string;
  /** Icon position (left only, close always right) */
  @Input() iconPosition: 'start' | 'end' = 'start';
  /** Avatar: image URL or initials */
  @Input() avatar?: string;
  /** Custom style object for CSS vars */
  @Input() customStyle?: Record<string, string>;
  /** Custom class for tag */
  @Input() customClass?: string;
  /** Custom icon color */
  @Input() iconColor?: string;

  /** Emits when tag is removed (close icon) */
  @Output() removed = new EventEmitter<void>();
  /** Emits when tag is clicked (if handler provided) */
  @Output() clicked = new EventEmitter<void>();

  /** True if tag is clickable (handler attached and not disabled) */
  get clickable(): boolean {
    return this.clicked.observers.length > 0 && !this.disabled;
  }

  /** Remove handler (close icon) */
  onRemove(event: Event) {
    event.stopPropagation();
    if (!this.disabled) {
      this.removed.emit();
    }
  }

  /** Click handler (entire tag) */
  onClick() {
    if (this.clickable) {
      this.clicked.emit();
    }
  }
} 