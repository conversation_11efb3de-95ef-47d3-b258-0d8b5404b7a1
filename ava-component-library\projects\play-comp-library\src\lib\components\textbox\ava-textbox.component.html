<div [class]="wrapperClasses">
  <!-- Label -->
  <label
    *ngIf="label"
    [for]="inputId"
    class="ava-textbox__label"
    [class.ava-textbox__label--required]="required"
  >
    {{ label }}
    <span *ngIf="required" class="ava-textbox__required" aria-hidden="true"
      >*</span
    >
  </label>

  <!-- Input Container -->
  <div class="ava-textbox__container" [ngStyle]="style">
    <!-- Prefix Slot -->
    <div class="ava-textbox__prefix" #prefixContainer>
      <ng-content select="[slot=prefix]"></ng-content>
    </div>

    <!-- Start Projected Icons (before input/textarea) -->
    <div
      class="ava-textbox__icons ava-textbox__icons--start"
      #iconStartContainer
    >
      <ng-content select="[slot=icon-start]"></ng-content>
    </div>

    <!-- Input Field -->
    <input
      [id]="inputId"
      [name]="name"
      [type]="type"
      [placeholder]="placeholder"
      [value]="value"
      [disabled]="disabled"
      [readonly]="readonly"
      [required]="required"
      [attr.maxlength]="maxlength"
      [attr.minlength]="minlength"
      [autocomplete]="autocomplete"
      [class]="inputClasses"
      [attr.aria-invalid]="hasError"
      [attr.aria-describedby]="ariaDescribedBy || null"
      (input)="onInput($event)"
      (focus)="onFocus($event)"
      (blur)="onBlur($event)"
      (change)="onChange_($event)"
    />

    <!-- End Projected Icons (before input/textarea) -->
    <div class="ava-textbox__icons ava-textbox__icons--end" #iconEndContainer>
      <ng-content select="[slot=icon-end]"></ng-content>
    </div>

    <!-- Suffix Slot -->
    <div class="ava-textbox__suffix" #suffixContainer>
      <ng-content select="[slot=suffix]"></ng-content>
    </div>
  </div>

  <!-- Error Message -->
  <div
    *ngIf="hasError"
    [id]="errorId"
    class="ava-textbox__error"
    role="alert"
    aria-live="polite"
  >
    <ava-icon
      iconName="alert-circle"
      [iconSize]="14"
      class="ava-textbox__error-icon"
      [cursor]="false"
      [disabled]="false"
      [iconColor]="'red'"
    ></ava-icon>
    <span class="ava-textbox__error-text">{{ error }}</span>
  </div>

  <!-- Helper Message -->
  <div *ngIf="hasHelper" [id]="helperId" class="ava-textbox__helper">
    <ava-icon
      iconName="info"
      [iconSize]="14"
      class="ava-textbox__helper-icon"
      [cursor]="false"
      [disabled]="false"
    ></ava-icon>
    <span class="ava-textbox__helper-text">{{ helper }}</span>
  </div>
</div>
