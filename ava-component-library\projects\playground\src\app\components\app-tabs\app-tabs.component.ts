import { Component, ViewEncapsulation } from '@angular/core';
import { TabsComponent, AvaTab } from '../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'awe-app-tabs',
  standalone: true,
  imports: [TabsComponent, CommonModule],
  templateUrl: './app-tabs.component.html',
  styleUrl: './app-tabs.component.scss',
  encapsulation: ViewEncapsulation.None,
})


export class AppTabsComponent {
  demoTabs: AvaTab[] = [
    { label: 'Top', icon: 'user', iconPosition: 'top', value: 'top', iconColor: 'red' },
    { label: 'Start', icon: 'user', iconPosition: 'start', value: 'start' },
    { label: 'End', icon: 'user', iconPosition: 'end', value: 'end' },
    { label: 'Bottom', icon: 'settings', iconPosition: 'bottom', value: 'bottom' },
    {
      label: 'Projects',
      icon: 'user',
      iconPosition: 'start',
      value: 'projects',
      dropdown: {
        items: [
          { label: 'All Projects', value: 'all-projects', icon: 'user', subtitle: 'All available projects', iconColor: 'purple' },
          { label: 'Owned by me', value: 'owned', icon: 'user', subtitle: 'Projects you own', iconColor: 'green' }
        ]
      }
    },
    { label: 'Disabled', icon: 'x-circle', iconPosition: 'start', value: 'disabled', disabled: true },
    { label: 'Long Tab Label', icon: 'user', iconPosition: 'end', value: 'long' },
    { label: 'Extra Long Tab Label', icon: 'user', iconPosition: 'top', value: 'extra-long' },
    { label: 'Another Tab', icon: 'settings', iconPosition: 'bottom', value: 'another' },
    { label: 'More Content', icon: 'info', iconPosition: 'start', value: 'more' },
    { label: 'Additional Tab', icon: 'user', iconPosition: 'end', value: 'additional' },
    { label: 'Even More Tabs', icon: 'settings', iconPosition: 'top', value: 'even-more' },
    { label: 'Tab Number Eleven', icon: 'info', iconPosition: 'bottom', value: 'eleven' },
    { label: 'Final Tab Here', icon: 'user', iconPosition: 'start', value: 'final' },
    { label: 'Tab 13', icon: 'user', iconPosition: 'end', value: '13' },
    { label: 'Tab 14', icon: 'settings', iconPosition: 'top', value: '14' },
    { label: 'Tab 15', icon: 'info', iconPosition: 'bottom', value: '15' }
  ];
  demoTabsWithContent: AvaTab[] = [
    { label: 'Home', icon: 'home', value: 'home', content: 'Welcome to the Home page! You can put any content here.', iconColor: 'red' },
    { label: 'Profile', icon: 'user', value: 'profile', content: 'This is your Profile page. Add forms, cards, or anything else.', iconColor: 'red' },
    { label: 'Settings', icon: 'settings', value: 'settings', content: 'Adjust your preferences in the Settings page.', iconColor: 'red' },
    { label: 'Help', icon: 'info', value: 'help', content: 'Need help? This is the Help page with useful info.', iconColor: 'red' }
  ];
  selectedTab: string | number = 'top';
  selectedTabButton: string | number = 'top';
  selectedTabIcon: string | number = 'top';
  selectedTabContainer: string | number = 'top';
  selectedTabWithContent: string | number = 'home';
  containerBg = '#e3f2fd';
  containerTestContent = '<div style="padding: 16px 0; color: #1976d2; font-weight: 500;">This is some test content inside the container. You can put anything here, such as forms, cards, or custom panels.</div>';
  containerStyle = {
    background: this.containerBg,
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    padding: '24px 16px',
    margin: '32px 0',
    boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
  };
  // Button variant with per-tab content
  demoTabsWithContentButton: AvaTab[] = [
    { label: 'Overview', icon: 'layout', iconPosition: 'start', value: 'overview', content: 'Overview page content. Button variant, icon at start.' },
    { label: 'Team', icon: 'user', iconPosition: 'top', value: 'team', content: 'Team page content. Button variant, icon at top.' },
    { label: 'Settings', icon: 'settings', iconPosition: 'bottom', value: 'settings', content: 'Settings page content. Button variant, icon at bottom.' },
    { label: 'More', icon: 'chevron-down', value: 'more', content: 'Dropdown tab with more options.', dropdown: { items: [{ label: 'Subtab 1', value: 'sub1', icon: 'user', subtitle: 'First subtab', iconColor: 'orange' }, { label: 'Subtab 2', value: 'sub2', icon: 'user', subtitle: 'Second subtab', iconColor: 'blue' }] } }
  ];
  selectedTabWithContentButton: string | number = 'overview';

  // Icon-only variant with per-tab content
  demoTabsWithContentIcon: AvaTab[] = [
    { label: 'Dashboard', icon: 'grid', value: 'dashboard', content: 'Dashboard content. Icon-only variant.' },
    { label: 'Search', icon: 'search', value: 'search', content: 'Search content. Icon-only variant.' },
    { label: 'Notifications', icon: 'bell', value: 'notifications', content: 'Notifications content. Icon-only variant.' },
    { label: 'Profile', icon: 'user', value: 'profile', content: 'Profile content. Icon-only variant.' }
  ];
  selectedTabWithContentIcon: string | number = 'dashboard';

  // Button variant demos for different visuals
  demoTabsButtonDefault: AvaTab[] = [
    { label: 'Default', icon: 'user', iconPosition: 'start', value: 'default' },
    { label: 'Glass', icon: 'settings', iconPosition: 'top', value: 'glass' },
    { label: 'Neo', icon: 'info', iconPosition: 'end', value: 'neo' },
  ];
  demoTabsButtonGlass: AvaTab[] = [
    { label: 'Glass 1', icon: 'user', iconPosition: 'start', value: 'glass1' },
    { label: 'Glass 2', icon: 'settings', iconPosition: 'top', value: 'glass2' },
    { label: 'Glass 3', icon: 'info', iconPosition: 'end', value: 'glass3' },
  ];
  demoTabsButtonNeo: AvaTab[] = [
    { label: 'Neo 1', icon: 'user', iconPosition: 'start', value: 'neo1' },
    { label: 'Neo 2', icon: 'settings', iconPosition: 'top', value: 'neo2' },
    { label: 'Neo 3', icon: 'info', iconPosition: 'end', value: 'neo3' },
  ];
  selectedTabButtonDefault: string | number = 'default';
  selectedTabButtonGlass: string | number = 'glass1';
  selectedTabButtonNeo: string | number = 'neo1';

  demoTabsPillDropdown: AvaTab[] = [
    { label: 'Dashboard', icon: 'grid', value: 'dashboard' },
    { label: 'Build', icon: 'rocket', value: 'build' },
    {
      label: 'Libraries',
      icon: 'book-open',
      value: 'libraries',
      dropdown: {
        items: [
          { label: 'Prompts', value: 'prompts', icon: 'sparkles', subtitle: 'Create, Manage and Edit Prompts', iconColor: 'var(--color-brand-primary)' },
          { label: 'Knowledge-base', value: 'kb', icon: 'book-open', subtitle: 'Add, Manage and Edit Knowledge-Base', iconColor: 'var(--color-brand-primary)' },
          { label: 'Tools', value: 'tools', icon: 'wrench', subtitle: 'Add, Manage and Edit Tools', iconColor: 'var(--color-brand-primary)' },
          { label: 'Models', value: 'models', icon: 'box', subtitle: 'Add, Manage and Edit Models', iconColor: 'var(--color-brand-primary)' },
          { label: 'Guardrails', value: 'guardrails', icon: 'shield', subtitle: 'Add, Manage and Edit Guardrails', iconColor: 'var(--color-brand-primary)' }
        ]
      }
    },
    { label: 'Approvals', icon: 'shield', value: 'approvals' },
    { label: 'User Management', icon: 'user', value: 'user-management' },
    { label: 'Analytics', icon: 'barChart2', value: 'analytics' }
  ];
  selectedTabPillDropdown: string | number = 'prompts';
  dropdownMenuStyle = {
    background: '#fff',
    borderRadius: '18px',
    boxShadow: '0 8px 24px 0 rgba(0,0,0,0.10)',
    padding: '24px',
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '16px',
    minWidth: '520px',
    marginTop: '12px',
    border: 'none',
  };

  onTabChange(newValue: string | number) {
    this.selectedTab = newValue;
    console.log('Tab changed:', newValue);
  }

  onTabChange1(newValue: string | number) {
    this.selectedTabButton = newValue;
  }

  onTabClick(tab: AvaTab) {
    console.log('Tab clicked:', tab);
  }

  onTabHover(tab: AvaTab) {
    console.log('Tab hovered:', tab);
  }

  onTabFocus(tab: AvaTab) {
    console.log('Tab focused:', tab);
  }

  onTabBlur(tab: AvaTab) {
    console.log('Tab blurred:', tab);
  }

  onDropdownItemClick(event: { parent: AvaTab; item: { label: string; value: string | number; icon?: string } }) {
    console.log('Dropdown item clicked:', event);
  }

  onDropdownItemHover(event: { parent: AvaTab; item: { label: string; value: string | number; icon?: string } }) {
    console.log('Dropdown item hovered:', event);
  }

  onDropdownItemFocus(event: { parent: AvaTab; item: { label: string; value: string | number; icon?: string } }) {
    console.log('Dropdown item focused:', event);
  }

  onDropdownItemBlur(event: { parent: AvaTab; item: { label: string; value: string | number; icon?: string } }) {
    console.log('Dropdown item blurred:', event);
  }

  get selectedTabWithContentObj() {
    return this.demoTabsWithContent.find(tab => tab.value === this.selectedTabWithContent);
  }
  get selectedTabWithContentButtonObj() {
    return this.demoTabsWithContentButton.find(tab => tab.value === this.selectedTabWithContentButton);
  }
  get selectedTabWithContentIconObj() {
    return this.demoTabsWithContentIcon.find(tab => tab.value === this.selectedTabWithContentIcon);
  }
}


