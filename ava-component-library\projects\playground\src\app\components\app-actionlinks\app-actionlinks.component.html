<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Link Component</h1>
        <p class="description">
          A versatile link component that supports multiple variants, states, sizes, icons, and animations. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
           <pre><code>import {{ '{' }} LinkComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-link label="Action Link" color="primary"></ava-link>
                </div>
                  <div class="col-12 col-sm-auto">
                  <ava-link label="Action Link" color="danger"></ava-link>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-link label="Action Link" color="success"></ava-link>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-link label="Action Link" color="warning"></ava-link>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-link label="Action Link" color="info"></ava-link>
                </div>
              </div>
            </ng-container>

            <!-- Link Sizes -->
            <ng-container *ngSwitchCase="'Link Sizes'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-link label="Small Link" size="small"color="primary"></ava-link>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-link label="Medium Link" size="medium"color="success"></ava-link>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-link label="Large Link" size="large"color="warning"></ava-link>
                </div>
              </div>
            </ng-container>

            <!-- Underlined Link -->
            <ng-container *ngSwitchCase="'Underlined Link'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-link label="Underlined Link" color="#ff3333"
                   [underline]="true"></ava-link>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
