<!-- toggle.component.html -->
<div class="ava-toggle-container"
     [class.toggle-left]="position === 'left'"
     [class.toggle-right]="position === 'right'"
     [class.disabled]="disabled">
  
  <!-- Title - always show when it exists, let CSS handle positioning -->
  <span *ngIf="title"
        class="toggle-title"
        [class.disabled]="disabled"
        [id]="titleName">
    {{ title }}
  </span>
  
  <!-- Toggle Switch -->
  <div class="toggle-wrapper"
       [class.toggle-small]="size === 'small'"
       [class.toggle-medium]="size === 'medium'"
       [class.toggle-large]="size === 'large'"
       [class.checked]="checked"
       [class.disabled]="disabled"
       [class.animated]="animation"
       [tabindex]="disabled ? -1 : 0"
       [attr.role]="'switch'"
       [attr.aria-checked]="checked"
       [attr.aria-disabled]="disabled"
       [attr.aria-labelledby]="titleName"
       [attr.aria-label]="!title ? 'Toggle switch' : null"
       (click)="onToggle()"
       (keydown)="onKeyDown($event)">
    
    <div class="toggle-slider"></div>
    
    <!-- Screen reader support -->
    <span class="sr-only">
      {{ title || 'Toggle switch' }} {{ checked ? 'enabled' : 'disabled' }}
    </span>
  </div>
</div>