import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter } from '@angular/core';
import { ButtonComponent } from '../button/button.component';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-sidebar',
  imports: [CommonModule, ButtonComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SidebarComponent {
  @Input() width?: string;
  @Input() collapsedWidth?: string;
  @Input() showCollapseButton?: boolean;
  @Input() isCollapsed?: boolean;

  @Output() collapseToggle = new EventEmitter<boolean>();

  private _isCollapsed = false;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    this._isCollapsed = this.isCollapsed || false;
  }

  toggleCollapse(): void {
    this._isCollapsed = !this._isCollapsed;
    this.collapseToggle.emit(this._isCollapsed);
    this.cdr.markForCheck();
  }

  get sidebarWidth(): string {
    if (this._isCollapsed && this.collapsedWidth) {
      return this.collapsedWidth;
    }
    return this.width || '';
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }
}