<!-- <h1>spinner basic</h1>
<section class="demo">
  <ava-spinner type="circular" color="purple" [progressIndex]="25" size="sm"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="50" size="sm"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="75" size="sm"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="100" size="sm"></ava-spinner>
</section>
<section class="demo-section">
  <ava-spinner type="circular" color="purple" [progressIndex]="25"size="md"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="50"size="md"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="75"size="md"></ava-spinner>
  <ava-spinner type="circular" color="purple" [progressIndex]="100" size="md"></ava-spinner>
</section>

<h1>spinner sizes</h1>
<section class="demo-section">
      <ava-spinner type="circular" color="purple" size="sm"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="lg"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="xl"></ava-spinner>
</section>

<h1>spinner types</h1>
<section class="demo-section">
     <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
  <ava-spinner type="gradient"  color="purple"size="md"></ava-spinner>
  <ava-spinner type="double" color="purple" size="md"></ava-spinner>
  <ava-spinner type="dotted" color="purple" size="md"></ava-spinner>
  <ava-spinner type="partial" color="purple" size="md"></ava-spinner>
  <ava-spinner type="dashed" color="purple" size="md"></ava-spinner>
</section>

<h1>spinner animations</h1>
<section class="demo-section">
     <ava-spinner type="circular" color="purple" size="sm" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" color="purple" size="md" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" color="purple" size="md" [animation]="false"></ava-spinner>
</section>

<h1>spinner variations</h1>
<section class="demo-section">
  <ava-spinner type="circular" size="md" color="danger" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" size="md" color="warning" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" size="md" color="success" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" size="md" color="primary" [animation]="true"></ava-spinner>
  <ava-spinner type="circular" size="md" color="secondary" [animation]="true"></ava-spinner>
</section> -->


<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Spinner Component</h1>
        <p class="description">
          A versatile spinner component that supports multiple types, sizes, colors, and animations. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} SpinnerComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>Basic Usage</h2>
            <div class="description-container">
              <p>Basic examples of spinner components with different progress indices.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <section class="demo">
              <ava-spinner type="circular" color="purple" [progressIndex]="25" size="sm"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="50" size="sm"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="75" size="sm"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="100" size="sm"></ava-spinner>
              </section>
              <section class="demo-section">
              <ava-spinner type="circular" color="purple" [progressIndex]="25"size="md"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="50"size="md"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="75"size="md"></ava-spinner>
              <ava-spinner type="circular" color="purple" [progressIndex]="100" size="md"></ava-spinner>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>Spinner Sizes</h2>
            <div class="description-container">
              <p>Examples of spinner components in different sizes.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <section class="demo">
                <ava-spinner type="circular" color="purple" size="sm"></ava-spinner>
                <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
                <ava-spinner type="circular" color="purple" size="lg"></ava-spinner>
                <ava-spinner type="circular" color="purple" size="xl"></ava-spinner>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>Spinner Types</h2>
            <div class="description-container">
              <p>Examples of different spinner types.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <section class="demo">
                <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
                <ava-spinner type="gradient" color="purple" size="md"></ava-spinner>
                <ava-spinner type="double" color="purple" size="md"></ava-spinner>
                <ava-spinner type="dotted" color="purple" size="md"></ava-spinner>
                <ava-spinner type="partial" color="purple" size="md"></ava-spinner>
                <ava-spinner type="dashed" color="purple" size="md"></ava-spinner>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>Spinner Animations</h2>
            <div class="description-container">
              <p>Examples of spinner components with different animation settings.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <section class="demo">
                <ava-spinner type="circular" color="purple" size="sm" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" color="purple" size="md" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" color="purple" size="md" [animation]="false"></ava-spinner>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>Spinner Variations</h2>
            <div class="description-container">
              <p>Examples of spinner components with different color variations.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="code-example">
        <div class="example-preview">
          <div class="row g-3">
            <div class="col-12 col-sm-auto">
              <section class="demo">
                <ava-spinner type="circular" size="md" color="danger" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" size="md" color="warning" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" size="md" color="success" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" size="md" color="primary" [animation]="true"></ava-spinner>
                <ava-spinner type="circular" size="md" color="secondary" [animation]="true"></ava-spinner>
              </section>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>type</code></td>
              <td><code>string</code></td>
              <td><code>'circular'</code></td>
              <td>The type of spinner (e.g., 'circular', 'gradient', 'double', 'dotted', 'partial', 'dashed').</td>
            </tr>
            <tr>
              <td><code>color</code></td>
              <td><code>string</code></td>
              <td><code>'purple'</code></td>
              <td>The color of the spinner.</td>
            </tr>
            <tr>
              <td><code>size</code></td>
              <td><code>string</code></td>
              <td><code>'md'</code></td>
              <td>The size of the spinner (e.g., 'sm', 'md', 'lg', 'xl').</td>
            </tr>
            <tr>
              <td><code>progressIndex</code></td>
              <td><code>number</code></td>
              <td><code>0</code></td>
              <td>The progress index of the spinner.</td>
            </tr>
            <tr>
              <td><code>animation</code></td>
              <td><code>boolean</code></td>
              <td><code>true</code></td>
              <td>Whether the spinner should animate.</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>animationStart</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when the spinner animation starts.</td>
            </tr>
            <tr>
              <td><code>animationEnd</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when the spinner animation ends.</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>

