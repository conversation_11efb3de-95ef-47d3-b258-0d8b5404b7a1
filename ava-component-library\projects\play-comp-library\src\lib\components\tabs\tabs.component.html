<ng-container *ngIf="container; else noContainer">
  <div class="ava-tabs__container-wrapper" [ngStyle]="containerStyle">
    <div class="awe-tabs__container" [ngStyle]="wrapperStyle ? wrapperStyle : customStyle">
      <div class="awe-tabs__scroll-area">
        <nav #tabList class="ava-tabs__list" [ngStyle]="listStyle"
          [class.awe-tabs--highlight-active]="highlightActiveText" role="tablist" [attr.aria-label]="ariaLabel"
          (scroll)="updateScrollButtonState()">
          <ng-container *ngIf="variant === 'button'; else notButtonVariant">
            <ava-button *ngFor="let tab of tabs; let i = index" class="ava-tabs__tab" [ngClass]="{
                'ava-tabs__tab--active': tab.value === value,
                'ava-tabs__tab--disabled': !!tab.disabled,
                'ava-tabs__tab--has-dropdown': !!tab.dropdown
              }" [label]="tab.label" [iconName]="tab.icon || ''" [iconPosition]="
                tab.iconPosition === 'end'
                  ? 'right'
                  : tab.iconPosition === 'start'
                  ? 'left'
                  : 'left'
              " [disabled]="!!tab.disabled" [attr.aria-selected]="tab.value === value"
              [attr.aria-disabled]="tab.disabled" [attr.tabindex]="tab.disabled ? -1 : 0" [attr.aria-label]="tab.label"
              [attr.title]="tab.label" (userClick)="onTabClick(tab, $event)" (mouseenter)="
                tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)
              " (mouseleave)="
                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
              " (focusin)="
                tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)
              " (focusout)="
                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
              " [variant]="buttonProps.variant || 'secondary'" [size]="buttonProps.size || 'normal'" [state]="
                tab.value === value ? 'active' : buttonProps.state || 'default'
              " [visual]="buttonProps.visual || 'normal'" [pill]="buttonProps.pill ?? false"
              [width]="buttonProps.width || ''" [height]="buttonProps.height || ''"
              [gradient]="buttonProps.gradient || ''" [background]="buttonProps.background || ''"
              [color]="buttonProps.color || ''" [iconColor]="buttonProps.iconColor || ''"
              [iconSize]="buttonProps.iconSize ?? 18">
              <ng-container *ngIf="tab.dropdown">
                <span class="ava-tabs__dropdown-chevron" aria-hidden="true">▼</span>
              </ng-container>
            </ava-button>
          </ng-container>
          <ng-template #notButtonVariant>
            <button *ngFor="let tab of tabs; let i = index" #tabButton class="ava-tabs__tab" [ngClass]="{
                'icon-top': tab.iconPosition === 'top',
                'icon-bottom': tab.iconPosition === 'bottom',
                'ava-tabs__tab--has-dropdown': tab.dropdown
              }" [class.ava-tabs__tab--active]="tab.value === value" [class.ava-tabs__tab--disabled]="tab.disabled"
              [attr.aria-selected]="tab.value === value" [attr.aria-disabled]="tab.disabled"
              [attr.tabindex]="tab.disabled ? -1 : 0" [attr.aria-label]="variant === 'icon' ? tab.label : null"
              [attr.title]="variant === 'icon' ? tab.label : null" (click)="onTabClick(tab)" (mouseenter)="
                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;
                onTabHover(tab)
              " (mouseleave)="
                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
              " (focusin)="
                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;
                onTabFocus(tab)
              " (focusout)="
                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
              " type="button" role="tab">
              <ng-container *ngIf="variant === 'icon'; else normalTabContent">
                <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18" [iconColor]="'var(--tab-icon-color)'"
                  [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
              </ng-container>
              <ng-template #normalTabContent>
                <ng-container [ngSwitch]="tab.iconPosition">
                  <ng-container *ngSwitchCase="'top'">
                    <div class="ava-tabs__icon-top">
                      <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                        [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                    </div>
                    <span class="ava-tabs__label">{{ tab.label }}</span>
                  </ng-container>
                  <ng-container *ngSwitchCase="'bottom'">
                    <div class="ava-tabs__icon-bottom">
                      <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                        [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                    </div>
                    <span class="ava-tabs__label">{{ tab.label }}</span>
                  </ng-container>
                  <ng-container *ngSwitchCase="'start'">
                    <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                      [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                    <span class="ava-tabs__label">{{ tab.label }}</span>
                  </ng-container>
                  <ng-container *ngSwitchCase="'end'">
                    <span class="ava-tabs__label">{{ tab.label }}</span>
                    <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                      [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"
                      class="ava-tabs__icon"></ava-icon>
                  </ng-container>
                  <ng-container *ngSwitchDefault>
                    <span class="ava-tabs__label">{{ tab.label }}</span>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="tab.dropdown">
                  <span class="ava-tabs__dropdown-arrow" aria-hidden="true">▼</span>
                </ng-container>
              </ng-template>
            </button>
          </ng-template>
          <div *ngIf="variant !== 'button' && variant !== 'icon'" class="ava-tabs__underline"
            [style.width.px]="underline.width" [style.transform]="'translateX(' + underline.left + 'px)'"></div>
        </nav>
        <button *ngIf="showScrollButtons" class="awe-tabs__scroll-btn awe-tabs__scroll-btn--left"
          (click)="scroll('left')" [disabled]="disableScrollLeft">
          <ava-icon iconName="chevron-left" [iconSize]="18" [iconColor]="'grey'" [cursor]="false"></ava-icon>
        </button>
        <button *ngIf="showScrollButtons" class="awe-tabs__scroll-btn awe-tabs__scroll-btn--right"
          (click)="scroll('right')" [disabled]="disableScrollRight">
          <ava-icon iconName="chevron-right" [iconSize]="18" [iconColor]="'grey'" [cursor]="false"></ava-icon>
        </button>
      </div>
    </div>
    <div class="ava-tabs__content" *ngIf="activeTab?.content">
      {{ activeTab?.content }}
    </div>
  </div>
</ng-container>
<ng-template #noContainer>
  <div class="awe-tabs__container" [ngStyle]="wrapperStyle ? wrapperStyle : customStyle">
    <div class="awe-tabs__scroll-area">
      <nav #tabList class="ava-tabs__list" [ngStyle]="listStyle"
        [class.awe-tabs--highlight-active]="highlightActiveText" role="tablist" [attr.aria-label]="ariaLabel"
        (scroll)="updateScrollButtonState()">
        <ng-container *ngIf="variant === 'button'; else notButtonVariant">
          <ava-button *ngFor="let tab of tabs; let i = index" class="ava-tabs__tab" [ngClass]="{
              'ava-tabs__tab--active': tab.value === value,
              'ava-tabs__tab--disabled': !!tab.disabled,
              'ava-tabs__tab--has-dropdown': !!tab.dropdown
            }" [label]="tab.label" [iconName]="tab.icon || ''" [iconPosition]="
              tab.iconPosition === 'end'
                ? 'right'
                : tab.iconPosition === 'start'
                ? 'left'
                : 'left'
            " [disabled]="!!tab.disabled" [attr.aria-selected]="tab.value === value"
            [attr.aria-disabled]="tab.disabled" [attr.tabindex]="tab.disabled ? -1 : 0" [attr.aria-label]="tab.label"
            [attr.title]="tab.label" (userClick)="onTabClick(tab, $event)" (mouseenter)="
              tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)
            " (mouseleave)="
              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
            " (focusin)="
              tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)
            " (focusout)="
              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
            " [variant]="buttonProps.variant || 'secondary'" [size]="buttonProps.size || 'normal'" [state]="
              tab.value === value ? 'active' : buttonProps.state || 'default'
            " [visual]="buttonProps.visual || 'normal'" [pill]="buttonProps.pill ?? false"
            [width]="buttonProps.width || ''" [height]="buttonProps.height || ''"
            [gradient]="buttonProps.gradient || ''" [background]="buttonProps.background || ''"
            [color]="buttonProps.color || ''" [iconColor]="buttonProps.iconColor || ''"
            [iconSize]="buttonProps.iconSize ?? 18" [dropdown]="!!tab.dropdown">
            <ng-container *ngIf="tab.dropdown">
              <span class="ava-tabs__dropdown-chevron" aria-hidden="true">▼</span>
            </ng-container>
          </ava-button>
        </ng-container>
        <ng-template #notButtonVariant>
          <button *ngFor="let tab of tabs; let i = index" #tabButton class="ava-tabs__tab" [ngClass]="{
              'icon-top': tab.iconPosition === 'top',
              'icon-bottom': tab.iconPosition === 'bottom',
              'ava-tabs__tab--has-dropdown': tab.dropdown
            }" [class.ava-tabs__tab--active]="tab.value === value" [class.ava-tabs__tab--disabled]="tab.disabled"
            [attr.aria-selected]="tab.value === value" [attr.aria-disabled]="tab.disabled"
            [attr.tabindex]="tab.disabled ? -1 : 0" [attr.aria-label]="variant === 'icon' ? tab.label : null"
            [attr.title]="variant === 'icon' ? tab.label : null" (click)="onTabClick(tab)" (mouseenter)="
              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;
              onTabHover(tab)
            " (mouseleave)="
              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
            " (focusin)="
              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;
              onTabFocus(tab)
            " (focusout)="
              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)
            " type="button" role="tab">
            <ng-container *ngIf="variant === 'icon'; else normalTabContent">
              <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18" [iconColor]="'var(--tab-icon-color)'"
                [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
            </ng-container>
            <ng-template #normalTabContent>
              <ng-container [ngSwitch]="tab.iconPosition">
                <ng-container *ngSwitchCase="'top'">
                  <div class="ava-tabs__icon-top">
                    <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                      [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                  </div>
                  <span class="ava-tabs__label">{{ tab.label }}</span>
                </ng-container>
                <ng-container *ngSwitchCase="'bottom'">
                  <div class="ava-tabs__icon-bottom">
                    <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18"
                      [iconColor]="'var(--tab-icon-color)'" [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                  </div>
                  <span class="ava-tabs__label">{{ tab.label }}</span>
                </ng-container>
                <ng-container *ngSwitchCase="'start'">
                  <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18" [iconColor]="'var(--tab-icon-color)'"
                    [cursor]="false" [disabled]="!!tab.disabled"></ava-icon>
                  <span class="ava-tabs__label">{{ tab.label }}</span>
                </ng-container>
                <ng-container *ngSwitchCase="'end'">
                  <span class="ava-tabs__label">{{ tab.label }}</span>
                  <ava-icon *ngIf="tab.icon" [iconName]="tab.icon" [iconSize]="18" [iconColor]="'var(--tab-icon-color)'"
                    [cursor]="false" [disabled]="!!tab.disabled" class="ava-tabs__icon"></ava-icon>
                </ng-container>
                <ng-container *ngSwitchDefault>
                  <span class="ava-tabs__label">{{ tab.label }}</span>
                </ng-container>
              </ng-container>
              <ng-container *ngIf="tab.dropdown">
                <span class="ava-tabs__dropdown-arrow" aria-hidden="true">▼</span>
              </ng-container>
            </ng-template>
          </button>
        </ng-template>
        <div *ngIf="variant !== 'button' && variant !== 'icon'" class="ava-tabs__underline"
          [style.width.px]="underline.width" [style.transform]="'translateX(' + underline.left + 'px)'"></div>
      </nav>
      <button *ngIf="showScrollButtons" class="awe-tabs__scroll-btn awe-tabs__scroll-btn--left" (click)="scroll('left')"
        [disabled]="disableScrollLeft">
        <ava-icon iconName="chevron-left" [iconSize]="18" [iconColor]="'grey'" [cursor]="false"></ava-icon>
      </button>
      <button *ngIf="showScrollButtons" class="awe-tabs__scroll-btn awe-tabs__scroll-btn--right"
        (click)="scroll('right')" [disabled]="disableScrollRight">
        <ava-icon iconName="chevron-right" [iconSize]="18" [iconColor]="'grey'" [cursor]="false"></ava-icon>
      </button>
    </div>
  </div>
  <div class="ava-tabs__content" *ngIf="activeTab?.content">
    {{ activeTab?.content }}
  </div>
</ng-template>
<div *ngIf="openDropdownIndex !== null" #dropdownMenu class="ava-tabs__dropdown-menu ava-tabs__dropdown-menu--portal"
  [ngStyle]="dropdownMenuStyle" [style.left.px]="dropdownPosition?.left" [style.top.px]="dropdownPosition?.top"
  (mouseenter)="onDropdownMenuEnter(openDropdownIndex, dropdownMenu)"
  (mouseleave)="onDropdownMenuLeave(openDropdownIndex)" (focusout)="onDropdownMenuLeave(openDropdownIndex)">
  <button *ngFor="let item of tabs[openDropdownIndex]?.dropdown?.items" class="ava-tabs__dropdown-item" type="button"
    (click)="onDropdownItemClick(tabs[openDropdownIndex], item)"
    (mouseenter)="onDropdownItemHover(tabs[openDropdownIndex], item)"
    (focusin)="onDropdownItemFocus(tabs[openDropdownIndex], item)"
    (focusout)="onDropdownItemBlur(tabs[openDropdownIndex], item)">
    <ava-icon *ngIf="item.icon" [iconName]="item.icon" [iconSize]="18"
      [iconColor]="item.iconColor || 'var(--tab-dropdown-item-color)'" [cursor]="false" [disabled]="false"></ava-icon>
    <span class="ava-tabs__dropdown-label-group">
      <span class="ava-tabs__dropdown-label">{{ item.label }}</span>
      <span *ngIf="item.subtitle" class="ava-tabs__dropdown-subtitle">{{
        item.subtitle
        }}</span>
    </span>
  </button>
</div>