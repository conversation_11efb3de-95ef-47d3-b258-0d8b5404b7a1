.upload-container {
  width: 100%;
  font-family: var(--fileupload-font-family-body);
  border-radius:0.5rem;
  padding: 2rem 1.5rem;
  box-shadow: 0rem 0.0625rem 0.625rem 0rem var(--fileupload-primary-text-disabled);
  text-align: center;
  background: var(--fileupload-background-default);
  height: auto; // Changed from fixed height to auto to accommodate multiple files
  border: 1px solid var(--fileupload-border-color);

}

.file-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-title {
    width: 100%;
    text-align: center;
    font-family: var(--fileupload-font-family-heading);
    font-size: var( --fileupload-font-size-lg);
    font-weight: var(--fileupload-font-weight-regular);
    color: var(--fileupload-heading-color);
    padding-left: 2.25rem;
  }
}

.close-button {
  background: none;
  border: none;
  ava-icon {
    ::ng-deep .ava-icon-container {
      cursor: pointer;
    }
  }
}

.error-message {
  color: var(--fileupload-danger-background);
  font-size: var(--fileupload-font-size-xs);
  margin-top: 0.5rem;
  text-align: center;
}


.file-formats {
  color: var(--fileupload-text-color-primary);
  font-size: var(--fileupload-font-size-xs);
  line-height: 1.5rem;
  font-family: var(--fileupload-font-family-body);
  font-weight: var(--fileupload-font-weight-regular);
}

.upload-area {
  border-radius: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--fileupload-border-color);
  padding: 2rem 3rem;
  cursor: pointer;
  transition: border-color 0.3s ease;
  height: 12.25rem;
  margin-bottom: 1rem;

  &:hover {
    border-color: var(--fileupload-border-color);
  }

  .upload-placeholder {
    text-align: center;
    p.click-here {
      margin: 0.625rem 0;
      color: var(--fileupload-text-color-primary);
      font-size: var(--fileupload-font-size-md);
      font-weight: var(--fileupload-font-weight-regular);

      &.active {
        color: var(--fileupload-highlighted-text-pink);
      }
    }
  }
}

.file-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.files-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 0.5rem;
  color: var(--fileupload-text-color-primary);

  span {
    display: flex;
    align-items: center;
    font-size: var(--fileupload-font-size-xs);

  }

  a.viewAll {
    font-size: var(--fileupload-font-size-xs);
    align-self: center;
    cursor: pointer;
    color: var(--fileupload-text-color-link);
    text-decoration: underline;
    text-underline-offset: 0.2rem;
  }

}



@media (max-width: 768px) {
  .upload-container {
    width: 100%;
    padding: 1rem 0.75rem;
  }

  .file-upload-header-title {
    font-size: var( --fileupload-font-size-md);
  }

  .upload-area {
    padding: 1.5rem 1.8rem;
    width: 90%;
  }

  .files-list {
    gap: 0.25rem;
  }

  .upload-button,
  .select-files-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .upload-container {
    padding: 0.5rem 0.3rem;
  }

  .file-upload-header {
    flex-direction: column;
    align-items: center;

    &-title {
      font-size: var( --fileupload-font-size-sm);
    }
  }

  .upload-area {
    flex-direction: column;
    padding:1rem 1.5rem;
  }

  .upload-placeholder {
    p.click-here {
      font-size: var(--fileupload-font-size-sm);
    }
  }

 .files-list {
    gap: 0.25rem;
  }

  
}

.supported-file {
  color: var(--fileupload-text-color-primary);
  font-size: var(--fileupload-font-size-sm);
  font-family: var( --fileupload-font-family-body);
  font-weight: var(--fileupload-font-weight-regular);
}

