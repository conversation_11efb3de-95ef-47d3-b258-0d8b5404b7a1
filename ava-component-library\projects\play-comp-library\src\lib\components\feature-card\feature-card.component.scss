.ava-featured-card-container {
    .ava-feature-card {
        width: 100%;
        border-radius: 5px;
        padding: 20px;
        text-align: center;
        box-shadow: 6px 6px 12px #b8b9be, -6px -6px 12px #fff;
        transition: all 0.3s ease;
        background: #e6e7ee;

        .card-content {
            display: flex;
            flex-direction: column;
            gap: 0.5rem; // Optional: adds space between h3 and p
        }

        h3 {
            font-weight: normal;
            margin: 0;
        }

        p {
            margin: 0;
        }
    }
}