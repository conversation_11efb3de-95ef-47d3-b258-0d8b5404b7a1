import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DividersComponent } from "../../../../../play-comp-library/src/lib/components/dividers/dividers.component";
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';

interface DividerDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-dividers',
  standalone: true,
  imports: [
    CommonModule,
    DividersComponent,
    IconsComponent
  ],
  templateUrl: './app-dividers.component.html',
  styleUrls: ['./app-dividers.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppDividersComponent {
  // Documentation sections
sections: DividerDocSection[] = [
  {
    title: 'Basic Usage',
    description: 'Simple divider using default settings. Default color is applied when no other configuration is provided.',
    showCode: false,
  },
    {
    title: 'Divider Thickness',
    description: 'Examples showing different thicknesses.',
    showCode: false,
  },
  {
    title: 'Divider Colors',
    description: `Showcases various static and hover color combinations. 
If 'hoverColor' is not set, the divider remains the same color on hover.`,
    showCode: false,
  },
   {
    title: 'Divider Styles',
    description: 'Examples showing different border styles (solid, dashed, dotted).',
    showCode: false,
  },
  {
    title: 'Animated Dividers',
    description: 'Dividers with hover animations enabled using the animation property.',
    showCode: false,
  }
];


  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'defaultColor',
      type: 'string',
      default: "'#DADCE7'",
      description: 'Default color of the divider in hex format.',
    },
    {
      name: 'hoverColor',
      type: 'string',
      default: "''",
      description: 'Color of the divider on hover in hex format.',
    },
    {
      name: 'thickness',
      type: 'string',
      default: "'2px'",
      description: 'Thickness of the divider (supports px, rem, em).',
    },
    {
      name: 'dividerType',
      type: "'solid' | 'dashed' | 'dotted'",
      default: "'solid'",
      description: 'Style of the divider border.',
    },
    {
      name: 'Animation',
      type: 'boolean',
      default: 'false',
      description: 'Enable hover animation for the divider.',
    }
  ];

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage':
`// Basic divider example
import { Component } from '@angular/core';
import { DividersComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-divider',
  standalone: true,
  imports: [DividersComponent],
  template: \`
    <div class="divider-container">
      <awe-dividers></awe-dividers>
    </div>
  \`,
})
export class BasicDividerComponent {}`,

      'Divider Thickness':
`// Divider Thickness example
import { Component } from '@angular/core';
import { DividersComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-divider-thickness',
  standalone: true,
  imports: [DividersComponent],
  template: \`
    <div class="divider-variants-container">
      <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'2px'">
                  </awe-dividers>

   <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'4px'">
                  </awe-dividers>

  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'6px'">
                  </awe-dividers>
  \`,
})
export class DividerVariantsComponent {}`,

      'Divider Colors':
`// Divider Colors example
import { Component } from '@angular/core';
import { DividersComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-divider-colors',
  standalone: true,
  imports: [DividersComponent],
  template: \`
    <div class="divider-variants-container">
    <awe-dividers [defaultColor]="'#018266'" [thickness]="'5px'">
                  </awe-dividers>

  <awe-dividers [defaultColor]="'#c9372c'" [hoverColor]="'#ab2e25'" [thickness]="'5px'">
                  </awe-dividers>

   <awe-dividers [defaultColor]="'#ffcf40'" [hoverColor]="'#b88a00'" [thickness]="'5px'">
                  </awe-dividers>
  \`,
})
export class DividerVariantsComponent {}`,

      'Divider Styles':
`// Divider Styles example
import { Component } from '@angular/core';
import { DividersComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-divider-styles',
  standalone: true,
  imports: [DividersComponent],
  template: \`
    <div class="divider-variants-container">
    <awe-dividers [defaultColor]="'#018266'" [hoverColor]="'#01654f'" [thickness]="'2px'"
                    [dividerType]="'solid'">
                  </awe-dividers>

  <awe-dividers [defaultColor]="'#c9372c'" [hoverColor]="'#ab2e25'" [thickness]="'4px'"
                    [dividerType]="'dashed'"></awe-dividers>
                </div>

  <awe-dividers [defaultColor]="'#ffcf40'" [hoverColor]="'#b88a00'" [thickness]="'5px'"
                    [dividerType]="'dotted'">
                  </awe-dividers>
  \`,
})
export class DividerVariantsComponent {}`,

      'animated dividers':
`// Animated dividers example
import { Component } from '@angular/core';
import { DividersComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-animated-dividers',
  standalone: true,
  imports: [DividersComponent],
  template: \`
    <div class="animated-dividers-container">
                  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'4px'" [dividerType]="'solid'"
                    [animation]="true"></awe-dividers>
  \`,
})
export class AnimatedDividersComponent {}`,
    };

    return examples[section.toLowerCase()] || '';
  }
}
