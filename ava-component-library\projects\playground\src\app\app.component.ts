import { Component, signal, ViewEncapsulation, WritableSignal } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
// import { AvatarsComponent, BadgesComponent, HeaderComponent, IconsComponent, SidebarComponent, TabsComponent } from '../../../play-comp-library/src/public-api';

@Component({
  selector: 'awe-root',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',  
  encapsulation: ViewEncapsulation.None,
})
export class AppComponent{
  title = 'playground';
  active = true;
  activeLink = signal<string>('');
  toggle: WritableSignal<boolean> = signal(true);
  appCategory = 'consumer';

  setActiveLink(link: string): void {
    this.activeLink.set(link);
  }

  setAppCategory(category: string) {
    this.appCategory = category;
    document.documentElement.setAttribute('data-app-category', category);
  }

  dark() {
    document.documentElement.setAttribute('data-theme', 'dark');
  }

  light() {
    document.documentElement.setAttribute('data-theme', 'light');
  }

  isActive(link: string): boolean {
    return this.activeLink() === link;
  }
}
 