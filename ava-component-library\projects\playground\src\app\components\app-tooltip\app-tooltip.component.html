
<div class="documentation">

  <!-- Header -->
  <header class="doc-header">
    <h1>Tooltip Component</h1>
    <p class="description">
      The Tooltip component provides a versatile way to display contextual hints or information when users hover over or focus on elements. It supports various positions, sizes, animations, and behaviors.
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} TooltipComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <awe-tooltip [text]="'This is a tooltip '" [position]="'right'">
                This is a tooltip textPosition
              </awe-tooltip>
            </ng-container>

            <!-- Tooltip Positions -->
            <ng-container *ngSwitchCase="'Tooltip Positions'">
              <awe-tooltip [text]="'This is a tooltip is bottom'" [position]="'bottom'">This is a tooltip textPosition is bottom</awe-tooltip>
              <awe-tooltip [text]="'This is a tooltip is top'" [position]="'top'">This is a tooltip textPosition is top</awe-tooltip>
              <awe-tooltip [text]="'This is a tooltip is right'" [position]="'right'">This is a tooltip textPosition is right</awe-tooltip>
              <awe-tooltip [text]="'This is a tooltip is left'" [position]="'left'">This is a tooltip textPosition is left</awe-tooltip>
            </ng-container>

            <!-- Tooltip Sizes -->
            <ng-container *ngSwitchCase="'Tooltip Sizes'">
              <awe-tooltip [text]="'This is a small tooltip'" [position]="'right'" [size]="'small'">This is a small tooltip</awe-tooltip>
              <awe-tooltip [text]="'This is a medium tooltip'" [position]="'right'" [size]="'medium'">This is a medium tooltip</awe-tooltip>
              <awe-tooltip [text]="'This is a large tooltip'" [position]="'right'" [size]="'large'">This is a large tooltip</awe-tooltip>
            </ng-container>

            <!-- Tooltip with Animation -->
            <ng-container *ngSwitchCase="'Tooltip with Animation'">
              <awe-tooltip [text]="'Tooltip with animation'" [position]="'bottom'" [enableAnimation]="true">This tooltip has animation</awe-tooltip>
              <awe-tooltip [text]="'Tooltip without animation'" [position]="'right'" [enableAnimation]="false">This tooltip has no animation</awe-tooltip>
            </ng-container>

            <!-- Tooltip with Different Behaviors -->
            <ng-container *ngSwitchCase="'Tooltip with Different Behaviors'">
              <awe-tooltip [text]="'This is a tooltip on hover'" [position]="'right'" [behavior]="'hover'">This is a tooltip that shows on hover.</awe-tooltip>
              <awe-tooltip [text]="'This is a tooltip on focus'" [position]="'right'" [behavior]="'focus'">This is a tooltip that stays focused when clicked.</awe-tooltip>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td><code>{{ event.name }}</code></td>
          <td><code>{{ event.type }}</code></td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

</div>

