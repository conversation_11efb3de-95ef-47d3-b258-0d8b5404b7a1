import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../play-comp-library/src/lib/components/toggle/toggle.component';

interface ToggleDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-toggle-documentation',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  templateUrl: './app-toggle.component.html',
  styleUrls: ['./app-toggle.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppToggleComponent {
  sections: ToggleDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Basic usage of the toggle component with a simple on/off switch.',
      showCode: false,
    },
    {
      title: 'Toggle Sizes',
      description: 'Different available sizes for the toggle: small, medium, and large.',
      showCode: false,
    },
    {
      title: 'Disabled State',
      description: 'How to use the disabled state for the toggle.',
      showCode: false,
    },
    {
      title: 'Title positions in Toggle',
      description: 'Examples of toggle with title before and after.',
      showCode: false,
    },
    {
      title: 'Events Usage',
      description: 'Example of handling events emitted by the toggle component.',
      showCode: false,
    },
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'size',
      type: 'ToggleSize',
      default: "'medium'",
      description: 'Sets the size of the toggle component.',
    },
    {
      name: 'title',
      type: 'string',
      default: "''",
      description: 'The title displayed next to the toggle.',
    },
    {
      name: 'position',
      type: 'TogglePosition',
      default: "'left'",
      description: 'Defines the position of the title relative to the toggle switch.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disables the toggle when set to true.',
    },
    {
      name: 'checked',
      type: 'boolean',
      default: 'false',
      description: 'Defines whether the toggle is checked (true) or not (false).',
    },
    {
      name: 'animation',
      type: 'boolean',
      default: 'true',
      description: 'Enables or disables animation during state transitions.',
    },
  ];

  events = [
    {
      name: 'checkedChange',
      type: 'EventEmitter<boolean>',
      description: 'Emitted when the checked state changes.',
    },
  ];

  notificationsEnabled: boolean = false;
  darkModeEnabled: boolean = false;
  mediumToggleEnabled: boolean = true;
  animatedToggleEnabled: boolean = false;
  nonAnimatedToggleEnabled: boolean = true;
  eventToggleEnabled: boolean = false;

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onNotificationToggle(checked: boolean): void {
    this.notificationsEnabled = checked;
    console.log('Notifications:', checked ? 'enabled' : 'disabled');
  }

  onDarkModeToggle(checked: boolean): void {
    this.darkModeEnabled = checked;
    console.log('Dark mode:', checked ? 'enabled' : 'disabled');
  }

  onMediumToggle(checked: boolean): void {
    this.mediumToggleEnabled = checked;
    console.log('Medium toggle:', checked ? 'enabled' : 'disabled');
  }

  onAnimatedToggle(checked: boolean): void {
    this.animatedToggleEnabled = checked;
    console.log('Animated toggle:', checked ? 'enabled' : 'disabled');
  }

  onNonAnimatedToggle(checked: boolean): void {
    this.nonAnimatedToggleEnabled = checked;
    console.log('Non-animated toggle:', checked ? 'enabled' : 'disabled');
  }

  onEventToggle(checked: boolean): void {
    this.eventToggleEnabled = checked;
    console.log('Toggle event:', checked ? 'enabled' : 'disabled');
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
// TypeScript
import { Component } from '@angular/core';
import { ToggleComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-toggle',
  standalone: true,
  imports: [ToggleComponent],
  template: \`
    <ava-toggle
      size="medium"
      title="Enable Notifications"
      position="left"
      [animation]="true">
    </ava-toggle>
  \`
})
export class BasicToggleComponent { }
      `,
      'toggle sizes': `
// TypeScript
import { Component } from '@angular/core';
import { ToggleComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-toggle-sizes',
  standalone: true,
  imports: [ToggleComponent],
  template: \`
    <ava-toggle
      size="small"
      title="Small Toggle"
      position="left"
      [checked]="true"
      [animation]="true">
    </ava-toggle>

    <ava-toggle
      size="medium"
      title="Medium Toggle"
      position="left"
      [checked]="mediumToggleEnabled"
      [animation]="true"
      (checkedChange)="onMediumToggle($event)">
    </ava-toggle>

    <ava-toggle
      size="large"
      title="Large Toggle"
      position="left"
      [checked]="false"
      [animation]="true">
    </ava-toggle>
  \`
})
export class ToggleSizesComponent {
  mediumToggleEnabled: boolean = true;

  onMediumToggle(checked: boolean): void {
    this.mediumToggleEnabled = checked;
    console.log('Medium toggle:', checked ? 'enabled' : 'disabled');
  }
}
      `,
      'disabled state': `
// TypeScript
import { Component } from '@angular/core';
import { ToggleComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-disabled-toggle',
  standalone: true,
  imports: [ToggleComponent],
  template: \`
    <ava-toggle
      size="medium"
      title="Disabled Toggle"
      position="left"
      [checked]="false"
      [disabled]="true"
      [animation]="true">
    </ava-toggle>
  \`
})
export class DisabledToggleComponent { }
      `,
      'title positions in toggle': `
// TypeScript
import { Component } from '@angular/core';
import { ToggleComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-title-toggle',
  standalone: true,
  imports: [ToggleComponent],
  template: \`
    <ava-toggle
      size="medium"
      title="Enable Notifications"
      position="left"
      [checked]="notificationsEnabled"
      [animation]="true"
      (checkedChange)="onNotificationToggle($event)">
    </ava-toggle>

    <ava-toggle
      size="medium"
      title="Dark Mode"
      position="right"
      [checked]="darkModeEnabled"
      [animation]="true"
      (checkedChange)="onDarkModeToggle($event)">
    </ava-toggle>
  \`
})
export class TitleToggleComponent {
  notificationsEnabled: boolean = false;
  darkModeEnabled: boolean = false;

  onNotificationToggle(checked: boolean): void {
    this.notificationsEnabled = checked;
    console.log('Notifications:', checked ? 'enabled' : 'disabled');
  }

  onDarkModeToggle(checked: boolean): void {
    this.darkModeEnabled = checked;
    console.log('Dark mode:', checked ? 'enabled' : 'disabled');
  }
}
      `,
      'events usage': `
// TypeScript
import { Component } from '@angular/core';
import { ToggleComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-events-toggle',
  standalone: true,
  imports: [ToggleComponent],
  template: \`
    <ava-toggle
      size="medium"
      title="Toggle with Event"
      position="left"
      [checked]="eventToggleEnabled"
      [animation]="true"
      (checkedChange)="onEventToggle($event)">
    </ava-toggle>
  \`
})
export class EventsToggleComponent {
  eventToggleEnabled: boolean = false;

  onEventToggle(checked: boolean): void {
    this.eventToggleEnabled = checked;
    console.log('Toggle event:', checked ? 'enabled' : 'disabled');
  }
}
      `,
    };

    return examples[section] || '';
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code: ', err);
    });
  }
}
