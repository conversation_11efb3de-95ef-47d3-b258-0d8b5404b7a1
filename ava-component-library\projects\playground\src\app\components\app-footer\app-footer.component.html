<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Footer Component</h1>
        <p class="description">
          A versatile footer component that supports various configurations such as text, columns, links, and custom content. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} FooterComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Side Position'">
              <awe-footer
                [position]="'side'"
                fontColor="dark"
                bgColor="light"
                [footerText]="['Footer Text 1', 'Footer Text 2']">
                <div class="side-content">
                  <h3>This is custom content for the side position of the footer.</h3>
                  <button (click)="customAction()">Custom Side Action</button>
                </div>
              </awe-footer>
            </ng-container>

            <ng-container *ngSwitchCase="'Top Position'">
              <awe-footer
                [position]="'top'"
                [footerColumns]="footerColumns"
                [logoUrl]="'assets/ascendion_logo.svg'"
                [middleLinks]="middleLinks"
                fontColor="light"
                bgColor="dark">
                <div class="top-content">
                  <h3>This is custom content for the top position of the footer.</h3>
                  <button (click)="customAction()">Custom Top Action</button>
                </div>
                <div class="middle-content">
                  <h3>This is custom content for the middle position of the footer.</h3>
                  <button (click)="customAction()">Custom Middle Action</button>
                </div>
                <div class="bottom-content">
                  <div class="footer-bottom">
                    <div class="footer-ascendion">
                      <span>&copy; {{ currentYear }} Ascendion. All Rights Reserved.</span>
                    </div>
                  </div>
                </div>
              </awe-footer>
            </ng-container>

            <ng-container *ngSwitchCase="'Only Position'">
              <awe-footer
                [position]="'only'"
                fontColor="dark"
                bgColor="light">
                <div class="footer-only-content">
                  <img src="assets/ascendion_logo.svg" alt="Custom Image">
                  <h3>This is custom content for the "only" position of the footer.</h3>
                  <button (click)="customAction()">Custom Action</button>
                </div>
              </awe-footer>
            </ng-container>

            <ng-container *ngSwitchCase="'Animations'">
              <h2>Slide</h2>
              <awe-footer
                position="top"
                [footerColumns]="footerColumns"
                [middleLinks]="middleLinks"
                [logoUrl]="'assets/ascendion_logo.svg'"
                fontColor="light"
                bgColor="dark"
                [animation]="'slide'">
              </awe-footer>
              <h2>Fade</h2>
              <awe-footer
                position="top"
                [footerColumns]="footerColumns"
                [middleLinks]="middleLinks"
                [logoUrl]="'assets/ascendion_logo.svg'"
                fontColor="light"
                bgColor="dark"
                [animation]="'fade'">
              </awe-footer>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>

      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <section class="doc-section events-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td><code>{{ event.name }}</code></td>
          <td><code>{{ event.type }}</code></td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
