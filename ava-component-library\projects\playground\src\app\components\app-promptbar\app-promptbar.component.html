<div class="documentation">
  <!-- Hidden file input -->
  <input
    #fileInput
    type="file"
    style="display: none"
    accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"
    (change)="onFileSelected($event)"
  />

  <!-- File Error Message -->
  <div class="error-message" *ngIf="fileError">{{ fileError }}</div>
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Prompt Bar Component</h1>
        <p class="description">
          A versatile prompt bar component that supports various configurations
          such as text input, icons, animations, and custom content. Built with
          accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} promptbarComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
      <div class="row">
        <div class="col-12">
          <div
            class="section-header"
            tabindex="0"
            role="button"
            (click)="toggleSection(i)"
            (keydown.enter)="toggleSection(i)"
            (keydown.space)="toggleSection(i)"
          >
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <awe-prompt-bar
                [theme]="'light'"
                [(textValue)]="promptText"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
              <awe-prompt-bar
                [theme]="'dark'"
                [(textValue)]="promptText"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
            </ng-container>

            <ng-container *ngSwitchCase="'Animations'">
              <awe-prompt-bar
                [theme]="'light'"
                [(textValue)]="promptText"
                [staticText]="'Start creating with AI:'"
                [animatedTexts]="animatedTexts"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
              <awe-prompt-bar
                [theme]="'dark'"
                [(textValue)]="promptText"
                [staticText]="'Start creating with AI:'"
                [animatedTexts]="animatedTexts"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
            </ng-container>

            <ng-container *ngSwitchCase="'Custom Content'">
              <awe-prompt-bar
                [theme]="'light'"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                (enterPressed)="handleEnterPressed()"
                [(textValue)]="promptText"
              >
                <div class="custom-content">
                  <!-- Selected Files Display -->
                  <div class="selected-files" *ngIf="selectedFiles.length > 0">
                    <div class="file-item" *ngFor="let file of selectedFiles">
                      <div class="file-preview" (click)="showFilePreview(file)">
                        <img [src]="file.url" [alt]="file.name">
                        <span class="file-name">{{ truncateFileName(file.name) }}</span>
                      </div>
                      <awe-icons
                        iconName="awe_close"
                        (click)="removeFile(file.id)"
                        role="button"
                        tabindex="0"
                        iconColor="blue"
                        [attr.aria-label]="'Remove ' + file.name"
                      ></awe-icons>
                    </div>
                  </div>

                  <div class="tools-container">
                    <div class="pills-container">
                      <awe-file-attach-pill
                      [currentTheme]="'light'"
                        [options]="fileOptions"
                        (optionSelected)="onFileOptionSelected($event)"
                        [class.disabled]="isFileAttachDisabled"
                      ></awe-file-attach-pill>
                      <awe-icon-pill
                        [options]="techOptions"
                        [selectedOption]="selectedTech"
                        (selectionChange)="onTechSelected($event)"
                      ></awe-icon-pill>
                      <awe-icon-pill
                        [options]="designOptions"
                        [selectedOption]="selectedDesign"
                        (selectionChange)="onDesignSelected($event)"
                      ></awe-icon-pill>
                    </div>
                    <div class="enhance-icons">
                      <awe-icons
                        iconName="awe_enhance"
                        (click)="handleEnhanceText()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhance'"
                      ></awe-icons>
                      <awe-icons
                        iconName="awe_enhanced_send"
                        (click)="handleEnhancedSend()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhanced Send'"
                      ></awe-icons>
                    </div>
                  </div>
                </div>
              </awe-prompt-bar>
              <awe-prompt-bar
              [theme]="'dark'"
              defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
              (enterPressed)="handleEnterPressed()"
              [(textValue)]="promptText"
            >
              <div class="custom-content">
                <!-- Selected Files Display -->
                <div class="selected-files" *ngIf="selectedFiles.length > 0">
                  <div class="file-item" *ngFor="let file of selectedFiles">
                    <div class="file-preview" (click)="showFilePreview(file)">
                      <img [src]="file.url" [alt]="file.name">
                      <span class="file-name">{{ truncateFileName(file.name) }}</span>
                    </div>
                    <awe-icons
                      iconName="awe_close"
                      (click)="removeFile(file.id)"
                      role="button"
                      tabindex="0"
                      iconColor="blue"
                      [attr.aria-label]="'Remove ' + file.name"
                    ></awe-icons>
                  </div>
                </div>

                <div class="tools-container">
                  <div class="pills-container">
                    <awe-file-attach-pill
                    [currentTheme]="'light'"
                      [options]="fileOptions"
                      (optionSelected)="onFileOptionSelected($event)"
                      [class.disabled]="isFileAttachDisabled"
                    ></awe-file-attach-pill>
                    <awe-icon-pill
                      [options]="techOptions"
                      [selectedOption]="selectedTech"
                      (selectionChange)="onTechSelected($event)"
                    ></awe-icon-pill>
                    <awe-icon-pill
                      [options]="designOptions"
                      [selectedOption]="selectedDesign"
                      (selectionChange)="onDesignSelected($event)"
                    ></awe-icon-pill>
                  </div>
                  <div class="enhance-icons">
                    <awe-icons
                      iconName="awe_enhance"
                      (click)="handleEnhanceText()"
                      role="button"
                      tabindex="0"
                      [attr.aria-label]="'Enhance'"
                    ></awe-icons>
                    <awe-icons
                      iconName="awe_enhanced_send"
                      (click)="handleEnhancedSend()"
                      role="button"
                      tabindex="0"
                      [attr.aria-label]="'Enhanced Send'"
                    ></awe-icons>
                  </div>
                </div>
              </div>
            </awe-prompt-bar>
            </ng-container>

            <ng-container *ngSwitchCase="'Chat Bot Variant'">
              <awe-prompt-bar
                [theme]="'light'"
                [variant]="'chat-bot'"
                [(textValue)]="promptText"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
              <awe-prompt-bar
                [theme]="'dark'"
                [variant]="'chat-bot'"
                [(textValue)]="promptText"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                [leftIcons]="leftIcons"
                [rightIcons]="rightIcons"
                (iconClicked)="handleIconClick($event)"
                (textValueChange)="onTextValueChange($event)"
                (enterPressed)="handleEnterPressed()"
                (filesSelected)="onFilesSelected($event)"
                (fileRemoved)="onFileRemoved($event)"
                (filePreviewClosed)="onFilePreviewClosed()">
              </awe-prompt-bar>
            </ng-container>

            <ng-container *ngSwitchCase="'Prompt with design System'">
              <awe-prompt-bar
                [theme]="'light'"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                (enterPressed)="handleEnterPressed()"
                [(textValue)]="promptText"
              >
                <div class="custom-content">
                  <!-- Selected Files Display -->
                  <div class="selected-files" *ngIf="selectedFiles.length > 0">
                    <div class="file-item" *ngFor="let file of selectedFiles">
                      <div class="file-preview" (click)="showFilePreview(file)">
                        <img [src]="file.url" [alt]="file.name">
                        <span class="file-name">{{ truncateFileName(file.name) }}</span>
                      </div>
                      <awe-icons
                        iconName="awe_close"
                        (click)="removeFile(file.id)"
                        role="button"
                        tabindex="0"
                        iconColor="blue"
                        [attr.aria-label]="'Remove ' + file.name"
                      ></awe-icons>
                    </div>
                  </div>    
                
                  <!-- Design System Selection Container - Show only when triggered -->
                  <div class="design-system-container" *ngIf="showDesignSystemContainer">
                    <div class="design-system-option" (click)="handleDesignSystemSelection('our')">
                      <awe-icons
                        iconName="awe_design_system"
                        iconSize="48"
                        [attr.aria-label]="'Our Design System'"
                      ></awe-icons>
                      <span class="option-title">Our Design System</span>
                    </div>
                    <div class="design-system-option" (click)="handleDesignSystemSelection('create')">
                      <awe-icons
                        iconName="awe_add"
                        iconSize="48"
                        [attr.aria-label]="'Create Design System'"
                      ></awe-icons>
                      <span class="option-title">Create Design System</span>
                    </div>
                  </div>
              
                  <div class="tools-container">
                    <div class="pills-container">
                      <awe-file-attach-pill
                        [currentTheme]="'light'"
                        [options]="fileOptions"
                        (optionSelected)="onFileOptionSelected($event)"
                        [class.disabled]="isFileAttachDisabled"
                      ></awe-file-attach-pill>
                      <awe-icon-pill
                        [options]="designOptions"
                        [selectedOption]="selectedDesign"
                        (selectionChange)="onDesignSelected($event)"
                      ></awe-icon-pill>
              
                      <!-- New container for device icons -->
                      <div class="device-icons-container">
                        <awe-icons
                          iconName="awe_laptop"
                          [color]="activeDevice === 'laptop' ? '#3D415C' : '#A3A7C2'"
                          (click)="setActiveDevice('laptop')"
                          role="button"
                          tabindex="0"
                          [attr.aria-label]="'Laptop view'"
                        ></awe-icons>
                        <awe-icons
                          iconName="awe_mobile"
                          [color]="activeDevice === 'mobile' ? '#3D415C' : '#A3A7C2'"
                          (click)="setActiveDevice('mobile')"
                          role="button"
                          tabindex="0"
                          [attr.aria-label]="'Mobile view'"
                        ></awe-icons>
                      </div>
                    </div>
                  
              
                    <div class="enhance-icons">
                      <!-- Undo button - only shows when text is enhanced -->
                      <awe-icons
                        *ngIf="isTextEnhanced"
                        iconName="awe_undo"
                        (click)="handleUndo()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Undo Enhancement'"
                      ></awe-icons>
                      <awe-icons
                        iconName="awe_enhance"
                        (click)="handleEnhanceText()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhance'"
                      ></awe-icons>
                      <awe-icons
                        iconName="awe_enhanced_send"
                        (click)="handleEnhancedSend()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhanced Send'"
                      ></awe-icons>
                    </div>
                  </div>
                </div>
              </awe-prompt-bar>
            </ng-container>

            <ng-container *ngSwitchCase="'Prompt without design System'">
              <awe-prompt-bar
                [theme]="'light'"
                defaultText="'Start creating with AI: &quot;Generate Design&quot;'"
                (enterPressed)="handleEnterPressed()"
                [(textValue)]="promptText"
              >
                <div class="custom-content">
                  <!-- Selected Files Display -->
                  <div class="selected-files" *ngIf="selectedFiles.length > 0">
                    <div class="file-item" *ngFor="let file of selectedFiles">
                      <div class="file-preview" (click)="showFilePreview(file)">
                        <img [src]="file.url" [alt]="file.name">
                        <span class="file-name">{{ truncateFileName(file.name) }}</span>
                      </div>
                      <awe-icons
                        iconName="awe_close"
                        (click)="removeFile(file.id)"
                        role="button"
                        tabindex="0"
                        iconColor="blue"
                        [attr.aria-label]="'Remove ' + file.name"
                      ></awe-icons>
                    </div>
                  </div>    
              
                  <div class="tools-container">
                    <div class="pills-container">
                      <awe-file-attach-pill
                        [currentTheme]="'light'"
                        [options]="fileOptions"
                        (optionSelected)="onFileOptionSelected($event)"
                        [class.disabled]="isFileAttachDisabled"
                      ></awe-file-attach-pill>
                      <awe-icon-pill
                        [options]="techOptions"
                        [selectedOption]="selectedTech"
                        (selectionChange)="onTechSelected($event)">
                      </awe-icon-pill>
                      <awe-icon-pill
                        [options]="designOptions"
                        [selectedOption]="selectedDesign"
                        (selectionChange)="onDesignSelected($event)"
                      ></awe-icon-pill>
              
                      <!-- New container for device icons -->
                      <div class="device-icons-container">
                        <awe-icons
                          iconName="awe_laptop"
                          [color]="activeDevice === 'laptop' ? '#3D415C' : '#A3A7C2'"
                          (click)="setActiveDevice('laptop')"
                          role="button"
                          tabindex="0"
                          [attr.aria-label]="'Laptop view'"
                        ></awe-icons>
                        <awe-icons
                          iconName="awe_mobile"
                          [color]="activeDevice === 'mobile' ? '#3D415C' : '#A3A7C2'"
                          (click)="setActiveDevice('mobile')"
                          role="button"
                          tabindex="0"
                          [attr.aria-label]="'Mobile view'"
                        ></awe-icons>
                      </div>
                    </div>
                  
                  
                    <div class="enhance-icons">
                      <awe-icons
                        *ngIf="isTextEnhanced"
                        iconName="awe_undo"
                        (click)="handleUndo()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Undo Enhancement'"
                      ></awe-icons>
                      <awe-icons
                        iconName="awe_enhance"
                        (click)="handleEnhanceText()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhance'"
                      ></awe-icons>
                      <awe-icons
                        iconName="awe_enhanced_send"
                        (click)="handleEnhancedSend()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhanced Send'"
                      ></awe-icons>
                    </div>

                  </div>
                </div>
              </awe-prompt-bar>
            </ng-container>

          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td>
            <code>{{ prop.name }}</code>
          </td>
          <td>
            <code>{{ prop.type }}</code>
          </td>
          <td>
            <code>{{ prop.default }}</code>
          </td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <section class="doc-section events-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td>
            <code>{{ event.name }}</code>
          </td>
          <td>
            <code>{{ event.type }}</code>
          </td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>

<!-- Image Preview Overlay -->
<div class="preview-overlay" *ngIf="showPreview" (click)="closePreview()">
  <div class="preview-content" (click)="$event.stopPropagation()">
    <div class="preview-header">
      <span class="preview-title">{{ previewFile?.name }}</span>
      <awe-icons
        iconName="awe_cross"
        (click)="closePreview()"
        role="button"
        tabindex="0"
        aria-label="Close preview"
      ></awe-icons>
    </div>
    <div class="preview-body">
      <img [src]="previewFile?.url" [alt]="previewFile?.name">
    </div>
  </div>
</div>
