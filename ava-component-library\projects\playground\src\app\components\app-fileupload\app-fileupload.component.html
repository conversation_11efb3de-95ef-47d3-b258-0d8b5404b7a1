

<div class="documentation">

  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>File Upload Component</h1>
        <p class="description">
          A versatile file upload component that allows users to select and upload files with different theme options. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>


  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} FileUploadComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
    <div class="row">
      <div class="col-12">
        <div class="section-header" tabindex="0" role="button">
          <h2>{{ section.title }}</h2>
          <div class="description-container">
            <p>{{ section.description }}</p>
            <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
              <span *ngIf="!section.showCode">View Code</span>
              <span *ngIf="section.showCode">Hide Code</span>
              <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
            </div>
          </div>
        </div>
      </div>
    </div>
    

    
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'light-uploader')" 
                    [theme]="'light'" 
                    uploaderId="light-uploader">
                  </awe-file-upload>
                </div>
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'dark-uploader')" 
                    [theme]="'dark'" 
                    uploaderId="dark-uploader">
                  </awe-file-upload>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Animated Upload'">
              <div class="row g-3">
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'light-animated-uploader')" 
                    [theme]="'light'" 
                    [enableAnimation]="true" 
                    uploaderId="light-animated-uploader">
                  </awe-file-upload>
                </div>
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'dark-animated-uploader')" 
                    [theme]="'dark'" 
                    [enableAnimation]="true" 
                    uploaderId="dark-animated-uploader">
                  </awe-file-upload>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Single File Upload'">
              <div class="row g-3">
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'light-single-uploader')" 
                    (filesListChanged)="onFilesChanged($event, 'light-single-uploader')"
                    [theme]="'light'" 
                    [singleFileMode]="true"
                    uploaderId="light-single-uploader">
                  </awe-file-upload>
                </div>
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'dark-single-uploader')" 
                    (filesListChanged)="onFilesChanged($event, 'dark-single-uploader')"
                    [theme]="'dark'" 
                    [singleFileMode]="true"
                    uploaderId="dark-single-uploader">
                  </awe-file-upload>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Limited File Upload'">
              <div class="row g-3">
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'light-limited-uploader')" 
                    (filesListChanged)="onFilesChanged($event, 'light-limited-uploader')"
                    [theme]="'light'" 
                    [maxFiles]="5"
                    uploaderId="light-limited-uploader">
                  </awe-file-upload>
                </div>
                <div class="col-12 col-md-6">
                  <awe-file-upload 
                    (fileUploaded)="onFileUpload($event, 'dark-limited-uploader')" 
                    (filesListChanged)="onFilesChanged($event, 'dark-limited-uploader')"
                    [theme]="'dark'" 
                    [maxFiles]="5"
                    uploaderId="dark-limited-uploader">
                  </awe-file-upload>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getFileUploadCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
  
 
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let event of eventProps">
              <td><code>{{ event.name }}</code></td>
              <td><code>{{ event.type }}</code></td>
              <td>{{ event.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>