/* toggle.component.scss - COMPLETE FIXED VERSION */
$toggle-track-checked-background : var(--toggle-track-checked-background);
$toggle-size-sm-width : var(--toggle-size-sm-width);
$toggle-size-sm-height :var( --toggle-size-sm-height);
$toggle-size-md-width : var(--toggle-size-md-width);
$toggle-size-md-height: var(--toggle-size-md-height);
$toggle-size-lg-width : var(--toggle-size-lg-width);
$toggle-size-lg-height: var(--toggle-size-lg-height);
$toggle-track-background:var(--toggle-track-background);
$toggle-thumb-background:var(--toggle-thumb-background);
$toggle-label-text:var( --toggle-label-text);
$toggle-label-disabled-text: var(--toggle-label-disabled-text);
$toggle-label-font:var(--toggle-label-font);
$glass-shadow:0px 2px 4px 0px rgba(39, 39, 39, 0.10);
$toggle-gap:12px;
$toggle-slider-sm-width:16px;
$toggle-slider-sm-height:16px;
$toggle-slider-md-width:20px;
$toggle-slider-md-height:20px;
$toggle-slider-lg-width:24px;
$toggle-slider-lg-height:24px;

$toggle-border-radius:var(--toggle-border-radius);

.ava-toggle-container {
  display: flex;
  align-items: center;
  gap: $toggle-gap;
  user-select: none;

  &.toggle-left {
    flex-direction: row;
  }

  &.toggle-right {
    flex-direction: row-reverse;
  }

  &.disabled {
    opacity: 0.6;
  }
}

.toggle-title {
  font-size: $toggle-label-font;
  color: $toggle-label-text;
  white-space: nowrap;

  &.disabled {
    color: $toggle-label-disabled-text;
  }
}

.toggle-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: $toggle-border-radius;
  background: $toggle-track-background;
  cursor: pointer;
  flex-shrink: 0;
  outline: none;
  padding: 2px;
  
  &.disabled {
    cursor: not-allowed;
  }

  &.checked {
    background: $toggle-track-checked-background;
  }

  /* Size variants */
  &.toggle-small {
    width: $toggle-size-sm-width;
    height: $toggle-size-sm-height;

    .toggle-slider {
      width: $toggle-slider-sm-width;
      height: $toggle-slider-sm-height;
      transform: translateX(1px);
    }

    &.checked .toggle-slider {
      transform: translateX(19px);
    }

    &:not(.disabled):active:not(.checked) .toggle-slider {
      transform: translateX(1px) scale(0.95);
    }

    &:not(.disabled):active.checked .toggle-slider {
      transform: translateX(15px) scale(0.95);
    }
  }

  &.toggle-medium {
    width: $toggle-size-md-width;
    height: $toggle-size-md-height;

    .toggle-slider {
      width: $toggle-slider-md-width;
      height: $toggle-slider-md-height;
      transform: translateX(1px);
    }

    &.checked .toggle-slider {
      transform: translateX(23px);
    }

    &:not(.disabled):active:not(.checked) .toggle-slider {
      transform: translateX(1px) scale(0.95);
    }

    &:not(.disabled):active.checked .toggle-slider {
      transform: translateX(19px) scale(0.95);
    }
  }

  &.toggle-large {
    width: $toggle-size-lg-width;
    height: $toggle-size-lg-height;

    .toggle-slider {
      width: $toggle-slider-lg-width;
      height: $toggle-slider-lg-height;
      transform: translateX(1px);
    }

    &.checked .toggle-slider {
      transform: translateX(27px);
    }

    &:not(.disabled):active:not(.checked) .toggle-slider {
      transform: translateX(1px) scale(0.95);
    }

    &:not(.disabled):active.checked .toggle-slider {
      transform: translateX(19px) scale(0.95);
    }
  }

  /* Focus styles - keyboard navigation only */
  &:focus-visible {
    outline: 2px solid $toggle-track-checked-background;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(156, 39, 176, 0.1);
  }

  /* Remove focus styles for mouse clicks */
  &:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }

  /* Hover effects */
  &:not(.disabled):hover .toggle-slider {
    box-shadow: $glass-shadow;
  }

  /* Active/pressed state */
  &:not(.disabled):active .toggle-slider {
    box-shadow: none;
  }

  &.animated {
    transition: background-color 0.3s ease;
    
    .toggle-slider {
      transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55), box-shadow 0.2s ease;
    }
  }

  &:not(.animated) {
    .toggle-slider {
      transition: box-shadow 0.2s ease; 
    }
  }
}

.toggle-slider {
  position: relative;
  border-radius: $toggle-border-radius;
  background: $toggle-thumb-background;
  box-shadow: $glass-shadow;
  flex-shrink: 0;
}

/* Screen reader support - visually hidden but accessible text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .toggle-wrapper {
    border: 1px solid transparent;

    &:focus-visible {
      border-color: currentColor;
      outline: 2px solid;
    }

    &:focus:not(:focus-visible) {
      border: none;
      outline: none;
    }
  }

  .toggle-slider {
    border: 1px solid rgba(0, 0, 0, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .toggle-wrapper.animated {
    transition: background-color 0.15s ease !important;
    
    .toggle-slider {
      transition: transform 0.15s ease, box-shadow 0.15s ease !important;
    }
  }
}