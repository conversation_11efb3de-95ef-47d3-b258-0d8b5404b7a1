<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Sidebar Component</h1>
        <p class="description">
          A versatile sidebar component that supports multiple items, icons, and
          routes. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} SidebarComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Sidebar without Icons -->
            <ng-container *ngSwitchCase="'Basic Sidebar'">
              <div class="multi-sidebar-container">
                <!-- Basic Sidebar -->
                <div class="basic-sidebar-wrapper">
                  <h3>Basic Sidebar</h3>
                  <ava-sidebar class="basic-sidebar-theme" width="280px" collapsedWidth="65px"  height="500px"
                    [class.basic-collapsed]="isBasicCollapsed" (collapseToggle)="onBasicCollapseToggle($event)" >
                    <!-- Basic Header Content -->
                    <div slot="header" class="basic-demo-header">
                      <div class="basic-logo">
                        <ava-icon iconName="home"></ava-icon>
                        <span class="basic-logo-text">Simple App</span>
                      </div>
                    </div>

                    <!-- Basic Main Content -->
                    <div slot="content">
                      <div class="basic-nav-section">
                        <div class="basic-nav-section-title">Navigation</div>
                        <div *ngFor="let item of basicSidebarItems" class="basic-nav-item"
                          [class.basic-active]="item.active" (click)="onBasicItemClick(item)">
                          <ava-icon [iconName]="item.icon" class="basic-nav-icon"></ava-icon>
                          <span class="basic-nav-text">{{ item.text }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- Basic Footer Content -->
                    <div slot="footer" class="basic-demo-footer">
                      <div class="basic-user-profile">
                        <ava-icon iconName="home" class="basic-profile-avatar"></ava-icon>
                        <div class="basic-profile-info">
                          <span class="basic-profile-name">Jane Smith</span>
                          <span class="basic-profile-role">User</span>
                        </div>
                      </div>
                    </div>
                  </ava-sidebar>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Collapsible Sidebar with Button Inside the Sidebar'">
              <div class="sidebar-demo">
                <h3>Collapsible Sidebar</h3>

                <ava-sidebar width="300px" collapsedWidth="60px" [showCollapseButton]="true"  height="500px"
                  [class.collapsed]="isCollapsed" (collapseToggle)="onCollapseToggle($event)">
                  <!-- Header Content -->
                  <div slot="header" class="demo-header">
                    <div class="logo">
                      <ava-icon iconName="home"></ava-icon>
                      <span class="logo-text">Dashboard</span>
                    </div>
                  </div>

                  <!-- Main Content -->
                  <div slot="content">
                    <div class="nav-section">
                      <div class="nav-section-title">Main</div>
                      <div *ngFor="let item of sidebarItems" class="nav-item" [class.active]="item.active"
                        (click)="onItemClick(item)">
                        <ava-icon [iconName]="item.icon" class="nav-icon"></ava-icon>
                        <span class="nav-text">{{ item.text }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Footer Content -->
                  <div slot="footer" class="demo-footer">
                    <div class="user-profile">
                      <ava-icon iconName="home" class="profile-avatar"></ava-icon>
                      <div class="profile-info">
                        <span class="profile-name">John Doe</span>
                        <span class="profile-role">Administrator</span>
                      </div>
                    </div>
                  </div>
                </ava-sidebar>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Collapsible Sidebar with Button Outside the Sidebar'">
              <div class="sidebar-demo">
                <h3>Collapsible Sidebar</h3>

                <ava-sidebar width="300px" collapsedWidth="60px" [showCollapseButton]="true"  height="500px" hoverAreaWidth="45px" buttonVariant="outside"
                  [class.collapsed]="isCollapsed" (collapseToggle)="onCollapseToggle($event)">
                  <!-- Header Content -->
                  <div slot="header" class="demo-header">
                    <div class="logo">
                      <ava-icon iconName="home"></ava-icon>
                      <span class="logo-text">Dashboard</span>
                    </div>
                  </div>

                  <!-- Main Content -->
                  <div slot="content">
                    <div class="nav-section">
                      <div class="nav-section-title">Main</div>
                      <div *ngFor="let item of sidebarItems" class="nav-item" [class.active]="item.active"
                        (click)="onItemClick(item)">
                        <ava-icon [iconName]="item.icon" class="nav-icon"></ava-icon>
                        <span class="nav-text">{{ item.text }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Footer Content -->
                  <div slot="footer" class="demo-footer">
                    <div class="user-profile">
                      <ava-icon iconName="home" class="profile-avatar"></ava-icon>
                      <div class="profile-info">
                        <span class="profile-name">John Doe</span>
                        <span class="profile-role">Administrator</span>
                      </div>
                    </div>
                  </div>
                </ava-sidebar>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Console Sidebar'">
              <ava-custom-sidebar [jsonFilePath]="'assets/data/sidebar.json'"></ava-custom-sidebar>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
          </button>
        </div>
      </div>
    </section>
  </div>
</div>
