.action-link {
  cursor: pointer;
  text-decoration: none; /* Ensures no underline by default */
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  transition: color 0.3s ease; /* Transition for smooth color change */

  /* Size classes */
  &.small {
    font-size: var(--link-size-sm-font);
  }

  &.medium {
    font-size: var(--link-size-md-font);
  }

  &.large {
    font-size: var(--link-size-lg-font);
  }

  /* Icon and arrow styles */
  .icon-left {
    margin-right: 6px;
  }

  /* Color variants */
  &.primary {
    color: var(--link-primary-text);
  }

  &.danger {
    color: var(--link-danger-text);
  }

  &.success {
    color: var(--link-success-text);
  }

  &.warning {
    color: var(--link-warning-text);
  }

  &.info {
    color: var(--link-info-text);
  }

  /* Hover effect for all labels */
  &:hover {
    color: var(--color-text-interactive-hover) !important; /* Changes the color to pink on hover */
  }

  /* Underline class */
  &.underline {
    text-decoration: var(--link-active-text-decoration);
  }

  /* Hover state for underlined links */
  &.underline:hover {
    text-decoration: underline; /* Ensure underline remains on hover */
  }

  /* Ensure hover color is applied to underlined links */
  &.underline:hover {
    color: var(--color-text-interactive-hover);
  }
}




