/**
 * Component: Timepicker
 * Purpose: Timepicker component tokens for time selection
 */

:root {
  /* Timepicker Base */
  --timepicker-background: var(--color-background-primary);
  --timepicker-border: 1px solid var(--color-border-default);
  --timepicker-border-radius: var(--global-radius-md);
  --timepicker-font-family: var(--font-family-body);
  --timepicker-shadow: var(--global-elevation-02);

  /* Timepicker Display */
  --timepicker-display-background: var(--color-background-secondary);
  --timepicker-display-text: var(--color-text-primary);
  --timepicker-display-font: var(--font-body-1);
  --timepicker-display-padding: var(--global-spacing-3);
  --timepicker-display-border-radius: var(--global-radius-sm);

  /* Timepicker Input */
  --timepicker-input-background: var(--color-background-primary);
  --timepicker-input-text: var(--color-text-primary);
  --timepicker-input-font: var(--font-body-1);
  --timepicker-input-padding: var(--global-spacing-3);
  --timepicker-input-border: 1px solid var(--color-border-default);
  --timepicker-input-border-radius: var(--global-radius-md);
  --timepicker-input-font-size: var(--global-font-size-md);
  --timepicker-input-small-font-size: calc(var(--global-font-size-md) - (var(--global-font-size-md) * 0.25));

  --timepicker-input-focus-border: 1px solid var(--color-border-focus);
  --timepicker-input-focus-shadow: 0 0 0 2px var(--accessibility-focus-ring-color);

  --timepicker-input-disabled-background: var(--color-background-disabled);
  --timepicker-input-disabled-text: var(--color-text-disabled);
  --timepicker-input-disabled-border: 1px solid var(--color-border-disabled);

  /* Timepicker Icon */
  --timepicker-icon-background: var(--color-surface-subtle);
  --timepicker-icon-color: var(--color-text-secondary);
  --timepicker-icon-size: var(--global-icon-size-md);
  --timepicker-icon-border-radius: var(--global-radius-sm);
  --timepicker-icon-padding: var(--global-spacing-2);

  --timepicker-icon-hover-background: var(--color-surface-subtle-hover);
  --timepicker-icon-hover-color: var(--color-text-primary);

  /* Timepicker Popup */
  --timepicker-popup-background: var(--color-background-primary);
  --timepicker-popup-border: 1px solid var(--color-border-default);
  --timepicker-popup-border-radius: var(--global-radius-lg);
  --timepicker-popup-shadow: var(--global-elevation-03);
  --timepicker-popup-padding: var(--global-spacing-4);
  --timepicker-popup-z-index: 1000;

  /* Timepicker Scroll Areas */
  --timepicker-scroll-background: var(--color-background-primary);
  --timepicker-scroll-border: 1px solid var(--color-border-subtle);
  --timepicker-scroll-border-radius: var(--global-radius-sm);
  --timepicker-scroll-max-height: 12rem;

  /* Timepicker Scrollbar */
  --timepicker-scrollbar-track-background: var(--color-surface-subtle);
  --timepicker-scrollbar-thumb-background: var(--color-surface-subtle);
  --timepicker-scrollbar-thumb-hover-background: var(--color-surface-subtle-hover);
  --timepicker-scrollbar-width: 0.5rem;

  /* Timepicker Time Items */
  --timepicker-time-item-background: transparent;
  --timepicker-time-item-text: var(--color-text-primary);
  --timepicker-time-item-font: var(--font-body-2);
  --timepicker-time-item-padding: var(--global-spacing-2);
  --timepicker-time-item-border-radius: var(--global-radius-sm);
  --timepicker-time-item-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  --timepicker-time-item-hover-background: var(--color-surface-subtle-hover);
  --timepicker-time-item-hover-text: var(--color-text-primary);

  --timepicker-time-item-selected-background: var(--color-surface-interactive-default);
  --timepicker-time-item-selected-text: var(--color-text-on-brand);

  --timepicker-time-item-disabled-background: var(--color-background-disabled);
  --timepicker-time-item-disabled-text: var(--color-text-disabled);

  /* Timepicker Sections */
  --timepicker-hour-section-background: var(--color-background-primary);
  --timepicker-minute-section-background: var(--color-background-primary);
  --timepicker-ampm-section-background: var(--color-background-primary);

  --timepicker-section-border: 1px solid var(--color-border-subtle);
  --timepicker-section-border-radius: var(--global-radius-sm);
  --timepicker-section-padding: var(--global-spacing-2);

  /* Timepicker Labels */
  --timepicker-label-text: var(--color-text-secondary);
  --timepicker-label-font: var(--font-label);
  --timepicker-label-margin: var(--global-spacing-2);

  /* Timepicker Error */
  --timepicker-error-text: var(--color-text-error);
  --timepicker-error-font: var(--font-body-2);
  --timepicker-error-margin: var(--global-spacing-2);

  /* Timepicker Sizes */
  --timepicker-size-sm-input-padding: var(--global-spacing-2);
  --timepicker-size-sm-font: var(--global-font-size-sm);
  --timepicker-size-sm-icon-size: var(--global-icon-size-sm);

  --timepicker-size-md-input-padding: var(--global-spacing-3);
  --timepicker-size-md-font: var(--global-font-size-md);
  --timepicker-size-md-icon-size: var(--global-icon-size-md);

  --timepicker-size-lg-input-padding: var(--global-spacing-4);
  --timepicker-size-lg-font: var(--global-font-size-lg);
  --timepicker-size-lg-icon-size: var(--global-icon-size-lg);

  /* Timepicker Variants */
  --timepicker-variant-default-border: 1px solid var(--color-border-default);
  --timepicker-variant-default-background: var(--color-background-primary);

  --timepicker-variant-outlined-border: 1px solid var(--color-border-default);
  --timepicker-variant-outlined-background: transparent;

  --timepicker-variant-filled-border: none;
  --timepicker-variant-filled-background: var(--color-surface-subtle);

  /* Timepicker Format */
  --timepicker-format-12-hour: true;
  --timepicker-format-24-hour: false;
  --timepicker-format-separator: ":";
  --timepicker-format-ampm-margin: var(--global-spacing-2);
} 