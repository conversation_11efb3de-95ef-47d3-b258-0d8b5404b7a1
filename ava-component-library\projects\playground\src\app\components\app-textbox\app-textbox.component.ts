import { Component, ViewEncapsulation, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { 
  AvaTextboxComponent, 
  TextboxVariant,
  TextboxSize
} from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-app-textbox',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AvaTextboxComponent,
    ButtonComponent,
    IconComponent
  ],
  templateUrl: './app-textbox.component.html',
  styleUrls: ['./app-textbox.component.scss'],
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppTextboxComponent {
  // Demo form
  demoForm: FormGroup;

  // Demo values
  basicValue = '';
  emailValue = '';
  searchValue = '';
  passwordValue = '';
  showPassword = false;

  // Available options for demo
  variants: TextboxVariant[] = ['default', 'primary', 'success', 'error', 'warning', 'info'];
  sizes: TextboxSize[] = ['sm', 'md', 'lg'];

  constructor(private fb: FormBuilder) {
    this.demoForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      message: ['']
    });
  }

  // Form helpers
  getFieldError(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    return '';
  }

  onSubmit(): void {
    if (this.demoForm.valid) {
      console.log('Form submitted:', this.demoForm.value);
      alert('Form submitted successfully!');
    } else {
      console.log('Form is invalid');
      this.demoForm.markAllAsTouched();
    }
  }

  onSearchClick(): void {
    console.log('Search clicked for:', this.searchValue);
  }

  onClearClick(): void {
    this.searchValue = '';
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
