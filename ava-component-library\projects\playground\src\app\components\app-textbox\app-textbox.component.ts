import { Component, ViewEncapsulation,CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AvaTextboxComponent } from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-app-textbox',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AvaTextboxComponent,
    ButtonComponent,
    IconComponent
  ],
  templateUrl: './app-textbox.component.html',
  styleUrls: ['./app-textbox.component.scss'],
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppTextboxComponent {
  // Demo form
  demoForm: FormGroup;

  // Demo values
  basicValue = '';
  emailValue = '';
  passwordValue = '';
  textareaValue = '';
  showPassword = false;

  constructor(private fb: FormBuilder) {
    this.demoForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      description: [''],
      website: ['']
    });
  }

  // Event handlers for demo
  onTextboxInput(event: Event): void {
    console.log('Textbox input:', event);
  }

  onTextboxFocus(event: Event): void {
    console.log('Textbox focus:', event);
  }

  onTextboxBlur(event: Event): void {
    console.log('Textbox blur:', event);
  }

  onTextboxChange(event: Event): void {
    console.log('Textbox change:', event);
  }

  // Form helpers
  getFieldError(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    return '';
  }

  hasFieldError(fieldName: string): boolean {
    const field = this.demoForm.get(fieldName);
    return !!(field?.errors && field.touched);
  }

  onSubmit(): void {
    if (this.demoForm.valid) {
      console.log('Form submitted:', this.demoForm.value);
    } else {
      console.log('Form is invalid');
      this.demoForm.markAllAsTouched();
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onEditClick() {
    alert('Edit icon clicked!');
  }

  onSendClick() {
    alert('Send icon clicked!');
  }

  onClearClick() {
    alert('Clear icon clicked!');
  }
}
