import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  Output,
  EventEmitter,
} from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
import { IconComponent } from '../icon/icon.component';


export type ButtonVariant = 'primary' | 'secondary';
export type ButtonSize = 'small' | 'medium' | 'large' | 'normal';
export type ButtonState =
  | 'default'
  | 'active'
  | 'disabled'
  | 'danger'
  | 'warning';

@Component({
  selector: 'ava-button',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, IconComponent],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss',],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ButtonComponent {
  @Input() label = '';
  @Input() variant: ButtonVariant = 'primary';
  @Input() size: ButtonSize = 'normal';
  @Input() state: ButtonState = 'default';
  @Input() visual: 'normal' | 'glass' | 'neo' = 'normal';
  @Input() pill = false;
  @Input() disabled = false;
  @Input() width?: string;
  @Input() height?: string;
  @Input() gradient?: string;
  @Input() background?: string;
  @Input() color?: string;
  @Input() dropdown = false;


  @Input() iconName = '';
  @Input() iconColor = '';
  @Input() iconSize = 20;
  @Input() iconPosition: 'left' | 'right' | 'only' = 'left';


  @Output() userClick = new EventEmitter<Event>();


  isActive = false;
  timeoutRef: any;
  basicOrAdvanced = 'basic';
  ngOnInit() {
    if (this.visual === 'neo') {
      this.basicOrAdvanced = 'ava-button-advanced';
    } else {
      this.basicOrAdvanced = 'ava-button-basic';
    }
    this.isActive = this.state === 'active' ? true : false;
  }
  handleClick(event: Event): void {
    if (this.disabled) {
      event.preventDefault();
      return;
    }
    this.setActiveState();
    this.userClick.emit(event);
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (!this.disabled) {
        this.setActiveState();
        this.userClick.emit(event);
      }
    }
  }
  setActiveState(): void {
    this.isActive = true;
    this.timeoutRef = setTimeout(() => {
      this.isActive = false;
    }, 200);
  }

  get hasIcon(): boolean {
    return !!this.iconName;
  }

  get computedIconColor(): string {
    if (this.disabled)
      return 'var(--button-icon-color-disabled)';
    if (this.iconColor && this.isValidColor(this.iconColor))
      return this.iconColor;
    if (this.variant === 'primary')
      return 'var(--button-primary-text)';
    return 'var(--button-secondary-text)';
  }

  isValidColor(value: string): boolean {
    const s = new Option().style;
    s.color = value;
    return s.color !== '';
  }


  ngOnDestroy(): void {
    if (this.timeoutRef) {
      clearTimeout(this.timeoutRef);
    }
  }
}
