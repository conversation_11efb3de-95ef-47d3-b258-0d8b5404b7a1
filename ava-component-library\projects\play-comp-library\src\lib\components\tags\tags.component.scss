// Tag base styles
.ava-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.12em;
  font: var(--tags-font);
  border-radius: 0.7em;
  padding: 0.38em 0.28em;
  background: var(--tags-filled-background);
  color: var(--tags-filled-text);
  border: var(--tags-filled-border);
  transition: box-shadow 0.18s, outline 0.18s, background 0.18s, color 0.18s, border 0.18s;
  cursor: default;
  outline: none;
  min-width: 0;
  max-width: 100%;
  white-space: nowrap;
  user-select: none;
  position: relative;
  box-shadow: none;
  line-height: 1.8;
  font-size: 1.05rem;

  &.ava-tag--pill {
    border-radius: var(--tags-pill-border-radius) !important;
  }
  &.ava-tag--disabled {
    background: var(--tags-disabled-background) !important;
    color: var(--tags-disabled-text) !important;
    border: var(--tags-disabled-border) !important;
    cursor: var(--tags-disabled-cursor);
    opacity: 0.6;
    pointer-events: none;
  }
  &.ava-tag--clickable:not(.ava-tag--disabled) {
    cursor: pointer;
    &:hover, &:focus-visible {
      box-shadow: 0 0 0 2px var(--tags-hover-border);
      outline: 2px solid var(--tags-hover-border);
      outline-offset: 1px;
      // background: var(--tags-hover-background);
      // color: var(--tags-hover-text);
      border: var(--tags-hover-border);
    }
  }
  &.ava-tag--sm {
    font-size: var(--tags-size-sm-font);
    padding: var(--tags-size-sm-padding);
    border-radius: var(--tags-size-sm-border-radius);
    --tags-icon-size: var(--tags-icon-size);
  }
  &.ava-tag--md {
    font-size: var(--tags-size-md-font);
    padding: var(--tags-size-md-padding);
    border-radius: var(--tags-size-md-border-radius);
    --tags-icon-size: var(--tags-icon-size);
  }
  &.ava-tag--lg {
    font-size: var(--tags-size-lg-font);
    padding: var(--tags-size-lg-padding);
    border-radius: var(--tags-size-lg-border-radius);
    --tags-icon-size: var(--tags-icon-size);
  }
  &.ava-tag--filled {
    background: var(--tags-filled-background);
    color: var(--tags-filled-text);
    border: var(--tags-filled-border);
  }
  &.ava-tag--outlined {
    background: var(--tags-outlined-background) !important;
    color: var(--tags-outlined-text);
    border: var(--tags-outlined-border);
    &.ava-tag--primary {
      color: var(--tags-outlined-primary-text);
      border: var(--tags-outlined-primary-border);
    }
    &.ava-tag--success {
      color: var(--tags-outlined-success-text);
      border: var(--tags-outlined-success-border);
    }
    &.ava-tag--warning {
      color: var(--tags-outlined-warning-text);
      border: var(--tags-outlined-warning-border);
    }
    &.ava-tag--error {
      color: var(--tags-outlined-error-text);
      border: var(--tags-outlined-error-border);
    }
    &.ava-tag--info {
      color: var(--tags-outlined-info-text);
      border: var(--tags-outlined-info-border);
    }
    &.ava-tag--custom {
      background: var(--tag-custom-bg) !important;
      color: var(--tag-custom-color);
      border: var(--tag-custom-border);
    }
  }
  &.ava-tag--primary {
    background: var(--tags-filled-primary-background);
    color: var(--tags-filled-primary-text);
    border: var(--tags-filled-primary-border);
  }
  &.ava-tag--success {
    background: var(--tags-filled-success-background);
    color: var(--tags-filled-success-text);
    border: var(--tags-filled-success-border);
  }
  &.ava-tag--warning {
    background: var(--tags-filled-warning-background);
    color: var(--tags-filled-warning-text);
    border: var(--tags-filled-warning-border);
  }
  &.ava-tag--error {
    background: var(--tags-filled-error-background);
    color: var(--tags-filled-error-text);
    border: var(--tags-filled-error-border);
  }
  &.ava-tag--info {
    background: var(--tags-filled-info-background);
    color: var(--tags-filled-info-text);
    border: var(--tags-filled-info-border);
  }
  &.ava-tag--custom {
    background: var(--tag-custom-bg);
    color: var(--tag-custom-color);
    border: var(--tag-custom-border);
  }
}

.ava-tag__icon {
  font-size: 0.9em;
  display: inline-flex;
  align-items: center;
  color: var(--tags-icon-color, currentColor);
  margin-right: 0.12em;
  margin-left: 0;
  transition: color 0.18s;
}
.ava-tag__icon--start {
  margin-right: 0.6em;
  margin-left: -0.4em;
}
.ava-tag__icon--end {
  margin-left: 0.8em;
  margin-right: 0;
}
.ava-tag__avatar {
  width: 1.3em;
  height: 1.3em;
  margin-right: 0.3em;
  margin-left: -0.2em;
  font-size: 1em;
  border-radius: 50%;
  background: #e5e7eb;
  color: #222;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
}
.ava-tag__avatar--initials {
  background: #d1d5db;
  color: #222;
}
.ava-tag__label {
  display: inline-block;
  vertical-align: middle;
  max-width: 12em;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  letter-spacing: 0.01em;
}
.ava-tag__remove-btn {
  background: none;
  border: none;
  padding: 0.1em 0.1em;
  margin-left: 0.2em;
  margin-right: -0.4em;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 50%;
  transition: none;
  opacity: 0.7;
  &:hover:not(:disabled), &:active:not(:disabled) {
    background: none;
    color: inherit;
    opacity: 1;
  }
  &:focus-visible {
    outline: 2px solid var(--tags-focus-outline, #2563eb);
    outline-offset: 2px;
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}
.ava-tag__remove {
  font-size: var(--tags-removable-button-size, 1em);
  color: var(--tags-removable-button-text, #888);
  background: transparent;
  pointer-events: none;
} 