import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';

@Component({
  selector: 'ava-pagination-controls',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './pagination-controls.component.html',
  styleUrl: './pagination-controls.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaginationControlsComponent {
  @Input() currentPage = 1;
  @Input() totalPages = 10;
  @Input() type:
    | 'basic'
    | 'extended'
    | 'standard'
    | 'pageinfo'
    | 'simplepageinfo' = 'basic';

  @Output() pageChange = new EventEmitter<number>();

  goToPage(page: number | string): void {
    if (typeof page === 'number' && page !== this.currentPage) {
      this.pageChange.emit(page);
    }
  }

  // Return extended or basic pages depending on type
  get pages(): (number | string)[] {
    switch (this.type) {
      case 'basic':
        return this.getBasicPages();
      case 'extended':
        return this.getExtendedPages();
      case 'standard':
        return this.getStandardPages();
      default:
        return [];
    }
  }

  private getBasicPages(): (number | string)[] {
    const pages: (number | string)[] = [];
    const { currentPage, totalPages } = this;

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      pages.push(1);
      if (currentPage > 3) pages.push('...');
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) pages.push(i);
      if (currentPage < totalPages - 2) pages.push('...');
      pages.push(totalPages);
    }

    return pages;
  }

  private getExtendedPages(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  private getStandardPages(): (number | string)[] {
    const { currentPage, totalPages } = this;
    const pages: (number | string)[] = [];

    const firstPages = [1, 2, 3];
    const lastPages = [totalPages - 2, totalPages - 1, totalPages];

    // Add first 3 always
    firstPages.forEach((p) => {
      if (p <= totalPages) pages.push(p);
    });

    // Case: currentPage <= 3 → show only beginning
    if (currentPage <= 3) {
      pages.push('...');
      lastPages.forEach((p) => {
        if (!pages.includes(p) && p > 3) pages.push(p);
      });
      return pages;
    }

    // Case: currentPage == 4 → slight expansion
    if (currentPage === 4) {
      pages.push(4);
      pages.push('...');
      lastPages.forEach((p) => {
        if (!pages.includes(p)) pages.push(p);
      });
      return pages;
    }

    // Case: currentPage in middle
    if (currentPage > 4 && currentPage < totalPages - 3) {
      pages.push('...');
      pages.push(currentPage);
      pages.push('...');
      lastPages.forEach((p) => {
        if (!pages.includes(p)) pages.push(p);
      });
      return pages;
    }

    // Case: currentPage is near the end (≥ totalPages - 3)
    if (currentPage >= totalPages - 3) {
      pages.push('...');
      for (let i = totalPages - 3; i <= totalPages; i++) {
        if (!pages.includes(i) && i > 3) pages.push(i);
      }
      return pages;
    }

    return pages;
  }

  shouldShow(page: number | string): boolean {
    if (typeof page !== 'number') {
      return false;
    }

    const { currentPage, totalPages } = this;
    const firstPages = [1, 2, 3];
    const lastPages = [totalPages - 1, totalPages];

    return (
      firstPages.includes(page) ||
      lastPages.includes(page) ||
      Math.abs(currentPage - page) <= 1
    );
  }

  shouldInsertDots(index: number): boolean {
    const pages = this.pages;
    const curr = pages[index];
    const next = pages[index + 1];

    return (
      typeof curr === 'number' &&
      typeof next === 'number' &&
      this.shouldShow(next) &&
      !this.shouldShow(curr)
    );
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.pageChange.emit(this.currentPage + 1);
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.pageChange.emit(this.currentPage - 1);
    }
  }
}
