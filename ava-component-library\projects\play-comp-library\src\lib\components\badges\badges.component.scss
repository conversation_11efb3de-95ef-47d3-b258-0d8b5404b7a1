$badge-success-background:var(--badge-success-background);
$badge-warning-background:var(--badge-warning-background);
$badge-error-background:var(--badge-error-background);
$badge-info-background:var(--badge-info-background);
$badge-default-background:var(--badge-default-background);
$badge-align-items:var(--badge-align-items);
$badge-justify-content:var(--badge-justify-content);
$badge-weight:var(--badge-weight);
$badge-success-text:var(--badge-success-text);
$badge-warning-text:var(--badge-warning-text);
$badge-error-text:var(--badge-error-text);
$badge-info-text:var(--badge-info-text);
$badge-default-text:var(--badge-default-text);
$badge-size-lg-min-width: var(--badge-size-lg-min-width);
$badge-size-lg-height: var(--badge-size-lg-height);
$badge-size-md-min-width: var(--badge-size-md-min-width);
$badge-size-md-height: var(--badge-size-md-height);
$badge-size-sm-min-width: var(--badge-size-sm-min-width);
$badge-size-sm-height: var(--badge-size-sm-height);
$badge-size-lg-font: var(--badge-size-lg-font);
$badge-size-md-font: var(--badge-size-md-font);
$badge-size-sm-font: 14px;
$badge-icon-size:var(--badge-icon-size);

:host {
    display: inline-block;
  }
  
  .badge {
    display: flex;
    flex-direction: column;
    justify-content: $badge-justify-content;
    align-items: $badge-align-items;
    gap: 10px;
    flex-shrink: 0;
    border-radius: var(--Round, 1000px);
    font-weight: $badge-weight;
    font-family: system-ui, -apple-system, sans-serif;
    box-sizing: border-box;
    
    // Size variants - default circular dimensions
    &--large {
      width: $badge-size-lg-min-width;
      height: $badge-size-lg-height;
      padding: 12px;
      font-size: $badge-size-lg-font;
      line-height: 1;
    }
    
    &--medium {
      width: $badge-size-md-min-width;
      height: $badge-size-md-height;
      padding: 8px;
      font-size: $badge-size-md-font;
      line-height: 1;
    }
    
    &--small {
      width: $badge-size-sm-min-width;
      height: $badge-size-sm-height;
      padding: 4px;
      font-size: $badge-size-sm-font;
      line-height: 1;
    }
    
    // State variants
    &--high-priority {
      background: $badge-error-background;
      color: $badge-error-text;
    }
    
    &--medium-priority {
      background: $badge-warning-background;
      color: $badge-warning-text;
    }
    
    &--low-priority {
      background: $badge-success-background;
      color: $badge-success-text;
    }
    
    &--neutral {
      background: $badge-default-background;
      color: $badge-default-text;
    }
    
    &--information {
      background: $badge-info-background;
      color: $badge-info-text;
    }
  }
  
  .badge__count {
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
  }
  
  // Icon styling for perfect centering within badge
  .badge__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }
 
  
  .badge--expanded {
    width: auto !important;
    
    &.badge--large {
      min-width: $badge-size-lg-min-width;
      padding-left: 4px;
      padding-right: 4px;
    }
    
    &.badge--medium {
      min-width: $badge-size-md-min-width;
      padding-left: 4px !important;
      padding-right: 4px !important;
    }
    
    &.badge--small {
      min-width: $badge-size-sm-min-width;
      padding-left: 4px;
      padding-right: 4px;
    }
  }

  
