// @use '../../play-comp-library/src/lib/styles/global/index' as theme;
// @use '../../play-comp-library/src/lib/styles/theme/config' as config;
@use "../../play-comp-library/src/lib/styles/main.scss";
// // Include the button theme mixin globally (optional)
//////////////////play ground style
/// 
/* Header */
.doc-header { 
  h1 {
     text-align: left;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}
 
/* Sections */
.doc-sections {
  margin-top: 4rem;
  .example-preview{    
    padding: 50px 25px;
  }
}
 
.doc-section {
  margin-bottom: 1rem;
 
  h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }
 
  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}
 
/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);
 
  h2 {
    margin-bottom: 0.5rem;
  }
 
  .description-container {
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
 
  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);
 
    &:hover {
      text-decoration: underline;
    }
 
    span {
      margin-right: 0.5rem;
    }
 
    
  }
   h1 {
    font-size: 1.5rem !important;
    font-weight: 600;
    color: #212529 !important;; 
    margin-bottom: 1rem;
}

 h2 {
    font-size: 1rem !important;
    font-weight: 600;
    color: #212529 ;
    margin-bottom: 1rem;
}
p{
    font-size: .75rem !important;;
    color: #212529;

}
 .example-preview {
    border-radius: 5px !important;
    border: 1px solid #cccccc !important; ;
 }
}




