<div class="documentation">
  <!-- Header -->
  <header class="doc-header">
    <h1>Pagination Controls Component</h1>
    <p class="description">
      The Pagination Controls component provides a user-friendly interface for
      navigating through paginated content. It supports customizable page sizes
      and displays navigation controls for moving between pages.
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} PaginationControlsComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div
                class="code-toggle"
                (click)="toggleCodeVisibility(i, $event)"
              >
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->

            <ng-container *ngSwitchCase="'Basic Usage'">
              <ava-pagination-controls
                [type]="'basic'"
                [currentPage]="basic_Page"
                [totalPages]="10"
                (pageChange)="basic_Page = $event"
              ></ava-pagination-controls>
            </ng-container>

            <!-- Custom Size Pagination -->
            <ng-container *ngSwitchCase="'Custom Size Pagination'">
              <ava-pagination-controls
                [type]="'extended'"
                [currentPage]="extended_Page"
                [totalPages]="20"
                (pageChange)="extended_Page = $event"
              ></ava-pagination-controls>
            </ng-container>

            <!-- Console Pagination -->
            <ng-container *ngSwitchCase="'Console Pagination'">
              <ava-pagination-controls
                [type]="'standard'"
                [currentPage]="standard_Page"
                [totalPages]="10"
                (pageChange)="standard_Page = $event"
              ></ava-pagination-controls>
            </ng-container>

            <ng-container *ngSwitchCase="'Card Page Info'">
              <ava-pagination-controls
                [type]="'pageinfo'"
                [currentPage]="pageinfo_Page"
                [totalPages]="10"
                (pageChange)="pageinfo_Page = $event"
              ></ava-pagination-controls>
            </ng-container>

            <ng-container *ngSwitchCase="'Page Info'">
              <ava-pagination-controls
                [type]="'simplepageinfo'"
                [currentPage]="simplepageinfo_Page"
                [totalPages]="10"
                (pageChange)="simplepageinfo_Page = $event"
              ></ava-pagination-controls>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getPaginationCode(section.title)"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title)">
            <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td>
            <code>{{ prop.name }}</code>
          </td>
          <td>
            <code>{{ prop.type }}</code>
          </td>
          <td>
            <code>{{ prop.default }}</code>
          </td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td>
            <code>{{ event.name }}</code>
          </td>
          <td>
            <code>{{ event.type }}</code>
          </td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
