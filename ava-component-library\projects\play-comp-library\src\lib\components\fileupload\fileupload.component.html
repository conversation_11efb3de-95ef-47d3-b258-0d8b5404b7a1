<div class="upload-container" role="dialog" [ngClass]="[theme, enableAnimation ? 'animated' : '']"
    [attr.aria-labelledby]="'upload-title-' + uniqueId" [attr.aria-describedby]="'upload-desc-' + uniqueId"
    (dragover)="onDragOver($event)" (drop)="onDrop($event)">
    <div class="file-upload-header">
        <div class="file-upload-header-title" [id]="'upload-title-' + uniqueId">{{componentTitle}}</div>
        <button class="close-button" (click)="closeUpload()" [attr.aria-label]="'Close upload dialog'">
            <ava-icon iconName="X" [iconSize]="24" iconColor="#a1a1a1"></ava-icon>
        </button>
    </div>

    <p class="supported-file" [id]="'upload-desc-' + uniqueId">{{supportedFormatLabel}}</p>
    <p class="file-formats">{{allowedFormatsList.join(', ') | uppercase}}</p>

    <label class="upload-area" [attr.for]="'fileInput-' + uniqueId" [attr.aria-label]="'Click here to upload a file'">
        <input [id]="'fileInput-' + uniqueId" type="file" (change)="onFileSelected($event)"
            [attr.accept]="allowedFormatsList.join(',')" [attr.aria-describedby]="'file-input-desc-' + uniqueId"
            hidden />
        <div class="upload-placeholder">
            <ava-icon iconName="image" aria-hidden="true" [iconSize]="24" [iconColor]="'#a1a1a1'"></ava-icon>
            <div class="" *ngIf="uploadedFiles.length > 0; else dragToUpload">
                <p class="click-here" [id]="'file-input-desc-' + uniqueId">
                    Your file was added successfully
                </p>
                <p class="click-here active" [id]="'file-input-desc-' + uniqueId">
                    {{singleFileMode && uploadedFiles.length > 0 ? 'Click to replace file' :
                  'Click here to add more' }}
                </p>
            </div>
            <ng-template #dragToUpload>
                <p class="click-here " [id]="'file-input-desc-' + uniqueId">
                    Drag and Drop your file here
                </p>

                <p class="click-here" [id]="'file-input-desc-' + uniqueId">
                    {{singleFileMode && uploadedFiles.length > 0 ? 'Click to replace file' :
                    'Click here to upload'}}
                </p>
            </ng-template>
        </div>
    </label>

    @if (fileFormatError) {
    <div class="error-message" role="alert">
        Invalid file type. Allowed formats:{{allowedFormatsList.join(', ') | uppercase}}.
    </div>
    }

    @if (fileSizeError) {
    <div class="error-message" role="alert">
        File is too large. Maximum size allowed is {{ sizeFormat(maxFileSize)}}
    </div>
    }

    @if (maxFilesError) {
    <div class="error-message" role="alert">
        Maximum of {{maxFiles}} files allowed
    </div>
    }



    <div class="file-actions">

        <div class="files-list" *ngIf="uploadedFiles.length > 0">
            <ng-container *ngIf="!viewAll; else viewAllFiles">
                <div *ngFor="let file of uploadedFiles.slice(0,2); let i = index">
                    <ava-tag [label]="file.name | lowercase" [color]="'success'"
                        [iconColor]="theme=='dark'?'#a1a1aa':'#059669'" [variant]="'outlined'" [size]="'sm'"
                        [removable]="true" (removed)="removeFile(i)"></ava-tag>
                </div>
                <span *ngIf="uploadedFiles.length > 2">
                    ...+{{ uploadedFiles.length - 2 }} more
                </span>

            </ng-container>
            <ng-template #viewAllFiles>
                <div *ngFor="let file of uploadedFiles; let i = index">
                    <ava-tag [label]="file.name | lowercase" [color]="'success'"
                        [iconColor]="theme=='dark'?'#a1a1aa':'#059669'" [variant]="'outlined'" [size]="'sm'"
                        [removable]="true" (removed)="removeFile(i)"></ava-tag>
                </div>
            </ng-template>
            <a class="viewAll" *ngIf="uploadedFiles.length >2" (click)="toggleViewAll()">{{viewAll?
                'View Less' : 'View All'}}</a>
        </div>
        <!-- <button class="select-files-button" (click)="openFileSelector()"
            *ngIf="uploadedFiles.length > 0 && !singleFileMode">
            Add More Files
        </button> -->
        <ava-button [label]="'Upload'" variant="primary"
            size="medium" [state]="uploadedFiles.length > 0 ?'default':'disabled'"
            [disabled]="uploadedFiles.length > 0 ? false:true" (click)="uploadFile()"> </ava-button>

        <!-- <button class="upload-button" (click)="uploadFile()">
            {{uploadedFiles.length > 0 ? appConstants.UPLOAD_BUTTON : 'Upload'}}
        </button> -->
    </div>
</div>