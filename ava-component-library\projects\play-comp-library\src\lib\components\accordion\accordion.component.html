<div class="accordion-container" [ngClass]="accordionClasses">
  <div
    class="accordion-header"
    (click)="toggleExpand()"
    tabindex="0"
    role="button"
    [attr.aria-expanded]="expanded"
    (keydown.enter)="toggleExpand()"
    (keydown.space)="toggleExpand()"
  >
    <div class="header-row">
      <!-- TYPE: titleIcon -->
      <ng-container *ngIf="type === 'titleIcon'">
        <!-- LEFT: Static Title Icon -->
        <span class="icon">
          <lucide-icon
            [name]="titleIcon"
            class="accordion-title-icon"
            [attr.aria-hidden]="true"
          ></lucide-icon>
        </span>
      </ng-container>

      <!-- DEFAULT ICON LEFT -->
      <ng-container *ngIf="type !== 'titleIcon' && iconPosition === 'left'">
        <span class="icon">
          <lucide-icon
            [name]="expanded ? iconOpen : iconClosed"
            class="accordion-icon"
            [attr.aria-hidden]="true"
          ></lucide-icon>
        </span>
      </ng-container>

      <!-- TITLE: Always Rendered -->
      <span class="accordion-title">
        <span class="accordion-title-highlight">
          <ng-content select="[header]"></ng-content>
        </span>
      </span>

      <!-- ICON RIGHT -->
      <ng-container
        *ngIf="
          (type !== 'titleIcon' && iconPosition === 'right') ||
          type === 'titleIcon'
        "
      >
        <span class="icon right-aligned-icon">
          <lucide-icon
            [name]="expanded ? iconOpen : iconClosed"
            class="accordion-icon"
            [attr.aria-hidden]="true"
          ></lucide-icon>
        </span>
      </ng-container>
    </div>
  </div>

  <div *ngIf="expanded">
    <div
      class="accordion-body"
      [ngClass]="{ 'animated-content': animation, show: expanded }"
    >
      <ng-content select="[content]"></ng-content>
    </div>
  </div>
</div>
