<div class="ava-console-approval-card-container" [style.background-color]="cardContainerBackground">
    <ava-card>
        <div header *ngIf="cardData?.header">
            <h2 *ngIf="cardData?.header?.title">{{cardData?.header?.title}}</h2>
        </div>
        <div [style.height.px]="height?height:null" class="content-wrapper" content *ngIf="cardData?.contents">
            <div class="contents" *ngFor="let each of cardData?.contents; let i= index"
                [style.background-color]="contentsBackground">
                <ng-content select="div[contentFooter]"></ng-content>

                <div class="box c-header-wrapper" style="display: flex; justify-content: space-between; width: 100%;">
                    <div class="head-left">
                        <p *ngIf="each?.session1?.title">{{each?.session1?.title}} </p>
                    </div>
                    <div class="head-right">
                        <ava-tag *ngFor="let eachLabel of each?.session1?.labels" [label]="eachLabel.name"
                            [color]="eachLabel.color" size="sm"></ava-tag>
                        <ava-icon [iconColor]="'green'" iconSize="20" iconName="ellipsis-vertical"></ava-icon>
                    </div>

                </div>
                <div class="box label-wrapper">
                    <ava-tag *ngFor="let eachLabel of each?.session2" [label]="eachLabel.name" [color]="eachLabel.color"
                        size="sm"></ava-tag>
                </div>

                <div class="box info-wrapper">
                    <div [ngClass]="{'f': i==0}" *ngFor="let eachLabel of each?.session3; let i=index">
                        <ava-icon iconSize="20" [iconName]="eachLabel.iconName"></ava-icon>
                        <span>{{eachLabel.label}}</span>
                    </div>
                </div>
                <div class="box footer-wrapper" *ngIf=" each?.session4">
                    <ng-container *ngTemplateOutlet="contentTemplate;context: { index: i , label:each.session4 }">
                    </ng-container>
                </div>


            </div>
        </div>

    </ava-card>
</div>