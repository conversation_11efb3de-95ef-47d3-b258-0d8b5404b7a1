$avatar-size-sm:var(--avatar-size-sm);
$avatar-size-md:var(--avatar-size-md);
$avatar-size-lg:var(--avatar-size-lg);
$avatar-background:var( --avatar-background);
$avatar-text-color:var(--avatar-text-color);
$avatar-text-font:var(--avatar-text-font);
$avatar-text-size-sm:var(--avatar-text-size-sm);
$avatar-text-size-md:var(--avatar-text-size-md);
$avatar-status-border-color:var(--avatar-status-border-color);
$avatar-border-radius:var(--avatar-border-radius);
.avatar-container {
    position: relative;
    display: inline-block;
  
  .avatar-wrapper {
    display: inline-flex;
    padding: 8px;
    align-items: center;
    gap: 8px;
    background: $avatar-status-border-color;
  }
  
  .avatar {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 10px;
    flex-shrink: 0;
    aspect-ratio: 1/1;
    background-color:$avatar-background ;
    background-size: 100% 149.982%;
    background-repeat: no-repeat;
    position: relative;
  
    // Large size variations
    &--large {
      width:$avatar-size-lg;
      height: $avatar-size-lg;
  
      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }
  
      &.avatar--square {
        border-radius: 12px;
      }
    }
  
    // Medium size variations
    &--medium {
      width: $avatar-size-md;
      height: $avatar-size-md;
  
      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }
  
      &.avatar--square {
        border-radius: 8px;
      }
    }
  
    // Small size variations
    &--small {
      width: $avatar-size-sm;
      height: $avatar-size-sm;
  
      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }
  
      &.avatar--square {
        border-radius: 4px;
      }
    }
  }
}
  
  // Badge positioning
  .avatar-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    z-index: 1;
  }
  .avatar-text-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
  }
  
  .avatar-text {
    color: $avatar-text-color;
    text-align: center;
    font-weight: $avatar-text-font;
  
    &--status {
      font-size: $avatar-text-size-sm;
    }
  
    &--profile {
      font-size: $avatar-text-size-md;
    }
  }