// import { Component } from '@angular/core';
// import { SpinnerComponent, SpinnerType, SpinnerSize } from "../../../../../play-comp-library/src/lib/components/spinner/spinner.component";
// import { CommonModule } from '@angular/common';

// @Component({
//   selector: 'ava-app-spinners',
//   standalone: true,
//   imports: [ SpinnerComponent,CommonModule],
//   templateUrl: './app-spinners.component.html',
//   styleUrls: ['./app-spinners.component.scss']
// })
// export class AppSpinnersComponent {
//   isLoading = false;
//   spinnerTypes: SpinnerType[] = ['circular', 'dotted', 'partial','gradient', 'dashed', 'double'];
//   spinnerSizes: SpinnerSize[] = [ 'sm', 'md', 'lg', 'xl'];

//   toggleLoading() {
//     this.isLoading = !this.isLoading;
//   }
// }

import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SpinnerComponent, SpinnerType, SpinnerSize } from '../../../../../play-comp-library/src/lib/components/spinner/spinner.component';

interface SpinnerDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'ava-app-spinners',
  standalone: true,
  imports: [CommonModule, SpinnerComponent],
  templateUrl: './app-spinners.component.html',
  styleUrls: [
    './app-spinners.component.scss',
    // Include any additional styles if necessary
  ],
  encapsulation: ViewEncapsulation.None,
})
export class AppSpinnersComponent {
  // Documentation sections
  sections: SpinnerDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple spinner examples with different progress indices.',
      showCode: false,
    },
    {
      title: 'Spinner Sizes',
      description: 'Examples of spinner components in different sizes.',
      showCode: false,
    },
    {
      title: 'Spinner Types',
      description: 'Examples of different spinner types.',
      showCode: false,
    },
    {
      title: 'Spinner Animations',
      description: 'Examples of spinner components with different animation settings.',
      showCode: false,
    },
    {
      title: 'Spinner Variations',
      description: 'Examples of spinner components with different color variations.',
      showCode: false,
    },
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'type',
      type: "'circular' | 'dotted' | 'partial' | 'gradient' | 'dashed' | 'double'",
      default: "'circular'",
      description: 'The type of spinner.',
    },
    {
      name: 'color',
      type: 'string',
      default: "'purple'",
      description: 'The color of the spinner.',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg' | 'xl'",
      default: "'md'",
      description: 'The size of the spinner.',
    },
    {
      name: 'progressIndex',
      type: 'number',
      default: '0',
      description: 'The progress index of the spinner.',
    },
    {
      name: 'animation',
      type: 'boolean',
      default: 'true',
      description: 'Whether the spinner should animate.',
    },
  ];

  // Track loading states for examples
  isLoading = false;

  // Example spinner types and sizes
  spinnerTypes: SpinnerType[] = ['circular', 'dotted', 'partial', 'gradient', 'dashed', 'double'];
  spinnerSizes: SpinnerSize[] = ['sm', 'md', 'lg', 'xl'];

  toggleLoading() {
    this.isLoading = !this.isLoading;
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  // Example code snippets
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
import { Component } from '@angular/core';
import { SpinnerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-spinners',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <section class="demo">
      <ava-spinner type="circular" color="purple" [progressIndex]="25" size="sm"></ava-spinner>
      <ava-spinner type="circular" color="purple" [progressIndex]="50" size="sm"></ava-spinner>
      <ava-spinner type="circular" color="purple" [progressIndex]="75" size="sm"></ava-spinner>
      <ava-spinner type="circular" color="purple" [progressIndex]="100" size="sm"></ava-spinner>
    </section>
  \`
})
export class BasicSpinnersComponent { }`,
      'spinner sizes': `
import { Component } from '@angular/core';
import { SpinnerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-spinner-sizes',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <section class="demo-section">
      <ava-spinner type="circular" color="purple" size="sm"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="lg"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="xl"></ava-spinner>
    </section>
  \`
})
export class SpinnerSizesComponent { }`,
      'spinner types': `
import { Component } from '@angular/core';
import { SpinnerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-spinner-types',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <section class="demo-section">
      <ava-spinner type="circular" color="purple" size="md"></ava-spinner>
      <ava-spinner type="gradient" color="purple" size="md"></ava-spinner>
      <ava-spinner type="double" color="purple" size="md"></ava-spinner>
      <ava-spinner type="dotted" color="purple" size="md"></ava-spinner>
      <ava-spinner type="partial" color="purple" size="md"></ava-spinner>
      <ava-spinner type="dashed" color="purple" size="md"></ava-spinner>
    </section>
  \`
})
export class SpinnerTypesComponent { }`,
      'spinner animations': `
import { Component } from '@angular/core';
import { SpinnerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-spinner-animations',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <section class="demo-section">
      <ava-spinner type="circular" color="purple" size="sm" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="md" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" color="purple" size="md" [animation]="false"></ava-spinner>
    </section>
  \`
})
export class SpinnerAnimationsComponent { }`,
      'spinner variations': `
import { Component } from '@angular/core';
import { SpinnerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-spinner-variations',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <section class="demo-section">
      <ava-spinner type="circular" size="md" color="danger" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" size="md" color="warning" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" size="md" color="success" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" size="md" color="primary" [animation]="true"></ava-spinner>
      <ava-spinner type="circular" size="md" color="secondary" [animation]="true"></ava-spinner>
    </section>
  \`
})
export class SpinnerVariationsComponent { }`,
    };

    return examples[section.toLowerCase()] || '';
  }
}
