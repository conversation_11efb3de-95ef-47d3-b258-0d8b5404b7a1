<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Weekday Format Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .format-example {
            display: inline-block;
            margin: 10px 20px 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .format-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #495057;
        }
        .weekday-display {
            font-family: monospace;
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            display: inline-block;
        }
        .usage-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            color: #68d391;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Calendar Component - Weekday Format Feature</h1>
        <p>This demo shows how the new <code>weekdayFormat</code> input property allows you to customize the weekday display in the calendar component.</p>

        <div class="demo-section">
            <h3>Available Formats</h3>
            
            <div class="format-example">
                <div class="format-title">Format 3 (Default) - Three Letters</div>
                <div class="weekday-display">Mon Tue Wed Thu Fri Sat Sun</div>
            </div>

            <div class="format-example">
                <div class="format-title">Format 2 - Two Letters</div>
                <div class="weekday-display">Mo Tu We Th Fr Sa Su</div>
            </div>

            <div class="format-example">
                <div class="format-title">Format 1 - Single Letter</div>
                <div class="weekday-display">M T W T F S S</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>How to Use</h3>
            <p>Simply add the <code>weekdayFormat</code> input to your calendar component:</p>

            <div class="usage-example">
                <h4>Three Letters (Default)</h4>
                <div class="code">
&lt;ava-calendar <span class="highlight">[weekdayFormat]="3"</span>&gt;&lt;/ava-calendar&gt;
                </div>
            </div>

            <div class="usage-example">
                <h4>Two Letters</h4>
                <div class="code">
&lt;ava-calendar <span class="highlight">[weekdayFormat]="2"</span>&gt;&lt;/ava-calendar&gt;
                </div>
            </div>

            <div class="usage-example">
                <h4>Single Letter</h4>
                <div class="code">
&lt;ava-calendar <span class="highlight">[weekdayFormat]="1"</span>&gt;&lt;/ava-calendar&gt;
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>TypeScript Implementation</h3>
            <div class="code">
import { Component } from '@angular/core';
import { CalendarComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-calendar-demo',
  standalone: true,
  imports: [CalendarComponent],
  template: `
    &lt;!-- Different weekday formats --&gt;
    &lt;ava-calendar <span class="highlight">[weekdayFormat]="3"</span> (dateSelected)="onDateSelected($event)"&gt;&lt;/ava-calendar&gt;
    &lt;ava-calendar <span class="highlight">[weekdayFormat]="2"</span> (dateSelected)="onDateSelected($event)"&gt;&lt;/ava-calendar&gt;
    &lt;ava-calendar <span class="highlight">[weekdayFormat]="1"</span> (dateSelected)="onDateSelected($event)"&gt;&lt;/ava-calendar&gt;
  `
})
export class CalendarDemoComponent {
  onDateSelected(date: Date) {
    console.log('Selected date:', date);
  }
}
            </div>
        </div>

        <div class="demo-section">
            <h3>Dynamic Format Switching</h3>
            <p>You can also dynamically change the format based on user input or component state:</p>
            <div class="code">
export class DynamicCalendarComponent {
  <span class="highlight">weekdayFormat: 1 | 2 | 3 = 3;</span>

  changeFormat(format: 1 | 2 | 3) {
    this.weekdayFormat = format;
  }
}

// Template:
&lt;button (click)="changeFormat(1)"&gt;Single Letter&lt;/button&gt;
&lt;button (click)="changeFormat(2)"&gt;Two Letters&lt;/button&gt;
&lt;button (click)="changeFormat(3)"&gt;Three Letters&lt;/button&gt;

&lt;ava-calendar <span class="highlight">[weekdayFormat]="weekdayFormat"</span>&gt;&lt;/ava-calendar&gt;
            </div>
        </div>

        <div class="demo-section">
            <h3>API Reference</h3>
            <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">Property</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">Type</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">Default</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #dee2e6;"><code>weekdayFormat</code></td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;"><code>1 | 2 | 3</code></td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;"><code>3</code></td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">Format for weekday display: 1 = single letter (M), 2 = two letters (Mo), 3 = three letters (Mon)</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
