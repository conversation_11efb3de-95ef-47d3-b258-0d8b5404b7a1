<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>ava-textarea Component</h1>
        <p class="description">
          A flexible textarea component supporting variants, sizes, states,
          icons, prefix/suffix, and accessibility.
        </p>
      </header>
    </div>
  </div>

  <!-- Basic Example -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Basic Example</h2>
    <ava-textarea
      label="Basic Textarea"
      placeholder="Type here..."
      [(ngModel)]="basicValue"
      (textareaInput)="onTextareaInput()"
      (textareaFocus)="onTextareaFocus()"
      (textareaBlur)="onTextareaBlur()"
      (textareaChange)="onTextareaChange()"
    ></ava-textarea>
  </div>

  <!-- Sizes -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Sizes</h2>
    <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
      <ava-textarea
        label="Small"
        size="sm"
        placeholder="Small textarea"
      ></ava-textarea>
      <ava-textarea
        label="Medium (Default)"
        size="md"
        placeholder="Medium textarea"
      ></ava-textarea>
      <ava-textarea
        label="Large"
        size="lg"
        placeholder="Large textarea"
      ></ava-textarea>
    </div>
  </div>

  <!-- Variants -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Variants</h2>
    <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
      <ava-textarea
        label="Default"
        variant="default"
        placeholder="Default variant"
      ></ava-textarea>
      <ava-textarea
        label="Primary"
        variant="primary"
        placeholder="Primary variant"
      ></ava-textarea>
      <ava-textarea
        label="Success"
        variant="success"
        placeholder="Success variant"
        helper="Looks good!"
      ></ava-textarea>
      <ava-textarea
        label="Warning"
        variant="warning"
        placeholder="Warning variant"
        helper="Please double-check."
      ></ava-textarea>
      <ava-textarea
        label="Error"
        variant="error"
        placeholder="Error variant"
        error="This field has an error"
      ></ava-textarea>
    </div>
  </div>

  <!-- States -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>States</h2>
    <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
      <ava-textarea
        label="Disabled"
        [disabled]="true"
        [rows]="rows"
        [(ngModel)]="disabledValue"
      ></ava-textarea>
      <ava-textarea
        label="Read-only"
        [readonly]="true"
        [rows]="rows"
        [(ngModel)]="readonlyValue"
      ></ava-textarea>
      <ava-textarea
        label="Required"
        [required]="true"
        [rows]="rows"
      ></ava-textarea>
    </div>
  </div>

  <!-- With Icons -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>With Icons</h2>
    <ava-textarea
      label="Message"
      placeholder="Type your message..."
      [(ngModel)]="iconValue"
      [resizable]="false"
      [rows]="rows"
    >
      <ava-icon
        slot="icon-start"
        iconName="paperclip"
        [iconColor]="'#7c3aed'"
        (click)="onEditClick()"
        [iconSize]="16"
      ></ava-icon>
      <ava-icon
        slot="icon-end"
        iconName="edit"
        [iconColor]="'#7c3aed'"
        (click)="onEditClick()"
        [iconSize]="16"
      ></ava-icon>
      <ava-icon
        slot="icon-end"
        iconName="send"
        [iconColor]="'#2563eb'"
        (click)="onSendClick()"
        [iconSize]="16"
      ></ava-icon>
      <ava-icon
        slot="icon-end"
        iconName="x"
        [iconColor]="'#e91e63'"
        (click)="onClearClick()"
        [iconSize]="16"
      ></ava-icon>
    </ava-textarea>
  </div>

  <!-- Helper & Error -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Helper & Error</h2>
    <ava-textarea
      label="With Helper"
      placeholder="Helper text example"
      helper="This is some helpful info."
      [(ngModel)]="helperValue"
    ></ava-textarea>
    <ava-textarea
      label="With Error"
      placeholder="Error text example"
      error="This field is required."
      [(ngModel)]="errorValue"
    ></ava-textarea>
  </div>

  <!-- Live Value Display -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Live Value</h2>
    <ava-textarea
      label="Live Value"
      placeholder="Type to see value below"
      [(ngModel)]="basicValue"
    ></ava-textarea>
    <div style="margin-top: 1rem">
      <strong>Value:</strong>
      <pre>{{ basicValue }}</pre>
    </div>
  </div>
</div>
