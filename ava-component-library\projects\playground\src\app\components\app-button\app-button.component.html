
<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Button Component</h1>
        <p class="description">
          A versatile button component that supports multiple variants, states,
          sizes, icons, and loading states. Built with accessibility and user
          experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ButtonComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                          <ava-button
                          label="Primary"
                          variant="primary"
                          width="160px"
                          (userClick)="onButtonClick($event)"
                          ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                      <ava-button
                        label="Seondary"
                        variant="secondary"
                        width="160px"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
              </div>
            </ng-container>

            <!-- Button States -->
            <ng-container *ngSwitchCase="'Button States'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-button
                        label="Default"
                        variant="primary"
                        size="medium"
                        state="default"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                 <ava-button
                        label="Active"
                        variant="primary"
                        state="active"
                        size="medium"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button
                        label="Disabled"
                        variant="primary"
                        state="disabled"
                        [disabled]="true"
                        size="medium"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button
                        label="Danger"
                        variant="primary"
                        state = "danger"
                        size="medium"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button
                        label="Warning"
                        variant="primary"
                        state="warning"
                        size="medium"
                        (userClick)="onButtonClick($event)"
                  ></ava-button>
                </div>
              </div>
            </ng-container>

            <!-- Button Sizes -->
            <ng-container *ngSwitchCase="'Button Sizes'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-button label="Small" size="small" ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button label="Medium" size="medium"></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button label="Large" size="large" ></ava-button>
                </div>
              </div>
            </ng-container>

            <!-- Icons -->
            <ng-container *ngSwitchCase="'Icons'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                 <ava-button
                          label="Icon Left"
                          variant="primary"
                          size ='large'
                          iconName="home"
                          iconPosition="left"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
                 <div class="col-12 col-sm-auto">
                 <ava-button
                          label="Icon Right"
                          variant="primary"
                          size ='large'
                          iconName="home"
                          iconPosition="right"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
                <div class="col-12 col-sm-auto" style="margin-top: 7px;">
                 <ava-button
                          label="Primary"
                          variant="primary"
                          iconName="home"
                          iconPosition="only"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
              
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Pill'">
               <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-button
                          label="Primary"
                          variant="primary"
                          size ='large'
                          [pill]="pill"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                   <ava-button
                          label="Warning"
                          variant="primary"
                          size ='large'
                          state="warning"
                            [pill]="pill"
                          (userClick)="onButtonClick($event)"
                          ></ava-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-button
                          label="Danger"
                          variant="primary"
                          size ='large'
                          state="danger"
                          [pill]="pill"
                          (userClick)="onButtonClick($event)"
                          ></ava-button>
                </div>
              </div>
            </ng-container>

                <ng-container *ngSwitchCase="'Gradient'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                 <ava-button
                          label="Icon Left"
                          variant="primary"
                          size ='large'
                          iconName="home"
                          iconPosition="left"
                          gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
                 <div class="col-12 col-sm-auto">
                 <ava-button
                          label="Icon Right"
                          variant="primary"
                          size ='large'
                          iconName="home"
                          iconPosition="right"
                          gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
                <div class="col-12 col-sm-auto" style="margin-top: 7px;">
                 <ava-button
                          label="Primary"
                          variant="primary"
                          iconName="home"
                          iconPosition="only"
                          gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
                          (userClick)="onButtonClick($event)"
                    ></ava-button>
                </div>
              
              </div>
            </ng-container>


            <!-- Animations -->
            <!-- <ng-container *ngSwitchCase="'Animations'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <awe-button
                    label="Ripple Effect"
                    animation="ripple"
                    width="150px"
                    height="50px"
                    (userClick)="onButtonClick($event)"
                  ></awe-button>
                </div>
                <div class="col-12 col-sm-auto">
                  <awe-button
                    label="Pulse Effect"
                    animation="pulse"
                    variant="secondary"
                    width="150px"
                    height="50px"
                    (userClick)="onButtonClick($event)"
                  ></awe-button>
                </div>
              </div>
            </ng-container>         -->

              <!-- Animations -->

              <ng-container *ngSwitchCase="'Glassmorphic'">
              <div class="row g-3 glass">               
                      <div class="glass-panel-container">
                            <div class="glass-panel">
                                <p>Glassmorphism Buttons</p>
                                  <ava-button
                                        label="Default"
                                        variant="primary"
                                        size="medium"
                                        state="default"
                                        visual = 'glass'
                                        (userClick)="onButtonClick($event)"
                                  ></ava-button>

                                  
                            </div>
                        </div>    
              </div>
            </ng-container> 
           <ng-container *ngSwitchCase="'Neomorphic'">
              <div class="bg-b">
                   <div class="row g-3">    
                  <div class="col-12 col-sm-auto ">    
                      <ava-button
                            label="Neo Type 1"
                            variant="primary"
                            height = "60"
                            state="default"
                            visual = 'neo'
                            background="#0d32a1"
                            iconName="home"
                            iconPosition="left"
                            (userClick)="onButtonClick($event)"
                      ></ava-button>
                  </div>
                   <div class="col-12 col-sm-auto">   
                        <ava-button
                            label="Neo Type 1"
                            variant="primary"
                            height = "60"
                            state="active"
                            visual = 'neo'
                            background="#0d32a1"
                            (userClick)="onButtonClick($event)"
                      ></ava-button>
                  </div>
                

              </div>
              </div>
             

            </ng-container>  
          </ng-container>
        </div>

   
      </div>
    </section>
  </div>



</div>


