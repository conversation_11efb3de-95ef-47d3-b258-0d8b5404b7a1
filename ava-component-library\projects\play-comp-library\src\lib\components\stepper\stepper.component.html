<div class="ava-stepper" [ngClass]="[orientation, size]">
  <div class="step-wrapper" *ngFor="let label of steps; let i = index">
    <div class="step-circle-wrapper">
      <div
        class="step-circle"
        [ngClass]="{
          active: i === currentStep,
          completed: i < currentStep
        }"
        (click)="goToStep(i)">

        <ava-icon
          *ngIf="i < currentStep || (i === currentStep && currentStep === steps.length - 1)"
          iconName="check"
          [iconColor]="iconColor"
          [iconSize]="iconSize"
        ></ava-icon>
        <!-- Show step number if not completed and not on last step -->
        <span *ngIf="i >= currentStep && !(i === currentStep && currentStep === steps.length - 1)">{{ i + 1 }}</span>
      </div>
      <!-- Connector line -->
      <div *ngIf="i < steps.length - 1" class="step-line"></div>
    </div>
    <div class="step-label" [ngClass]="{ active: i === currentStep }">{{ label }}</div>
  </div>
</div>
