import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation, OnInit, HostBinding, ViewChild, ElementRef, AfterViewInit, QueryList, ViewChildren, OnDestroy, ChangeDetectorRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LucideAngularModule } from 'lucide-angular';
import { IconComponent } from '../icon/icon.component';
import { ButtonComponent } from '../button/button.component';

export interface AvaTabDropdownItem {
  label: string;
  value: string | number;
  icon?: string;
  iconColor?: string;
  subtitle?: string;
}

export interface AvaTab {
  label: string;
  value: string | number;
  icon?: string; // Lucide icon name
  iconPosition?: 'top' | 'bottom' | 'start' | 'end';
  disabled?: boolean;
  content?: string | null;
  id?: string;
  dropdown?: {
    items: AvaTabDropdownItem[];
  };
  iconColor?: string;
}

@Component({
  selector: 'ava-tabs',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, IconComponent, ButtonComponent],
  templateUrl: './tabs.component.html',
  styleUrls: ['./tabs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class TabsComponent implements OnInit, AfterViewInit, AfterViewChecked, OnDestroy {
  @Input() tabs: AvaTab[] = [];
  @Input() value: string | number | null = null;
  @Output() valueChange = new EventEmitter<string | number>();
  @Input() highlightActiveText = true;
  @Input() maxWidth?: string;
  @Input() showChevrons = false;
  @Input() ariaLabel?: string;
  @Input() variant: 'default' | 'button' | 'icon' = 'default';
  @Input() style?: Record<string, string>;
  @Input() iconColor?: string;
  @Input() container = false;
  @Input() containerStyle?: Record<string, string>;
  @Output() dropdownSelect = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();
  @Output() tabClick = new EventEmitter<AvaTab>();
  @Output() tabHover = new EventEmitter<AvaTab>();
  @Output() tabFocus = new EventEmitter<AvaTab>();
  @Output() tabBlur = new EventEmitter<AvaTab>();
  @Output() dropdownItemClick = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();
  @Output() dropdownItemHover = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();
  @Output() dropdownItemFocus = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();
  @Output() dropdownItemBlur = new EventEmitter<{ parent: AvaTab; item: { label: string; value: string | number; icon?: string } }>();
  /**
   * Props to pass to ava-button when using the 'button' variant. All ava-button @Inputs are supported.
   * Defaults to { variant: 'secondary' } for design system consistency.
   */
  @Input() buttonProps: Partial<ButtonComponent> = { variant: 'secondary' };
  /**
   * Style object for the tab list (nav.ava-tabs__list). Useful for glassmorphic, neomorphic, or custom backgrounds.
   */
  @Input() listStyle?: Record<string, string>;
  /**
   * Style object for the wrapper (div.awe-tabs__container). Useful for custom backgrounds or effects when container is false.
   */
  @Input() wrapperStyle?: Record<string, string>;
  /**
   * Optional style object for the dropdown menu (ava-tabs__dropdown-menu). Allows full customization.
   */
  @Input() dropdownMenuStyle?: Record<string, string>;

  @HostBinding('style.--tabs-count')
  get tabsCount() {
    return this.tabs.length;
  }

  @ViewChild('tabList') tabList!: ElementRef<HTMLElement>;
  @ViewChildren('tabButton') tabButtons!: QueryList<ElementRef<HTMLButtonElement>>;

  underline = { width: 0, left: 0 };
  showScrollButtons = false;
  disableScrollLeft = true;
  disableScrollRight = false;
  openDropdownIndex: number | null = null;
  isTabHovered: number | null = null;
  isDropdownHovered: number | null = null;
  dropdownPosition: { left: number; top: number } | null = null;

  private resizeObserver?: ResizeObserver;
  private lastTabsSnapshot = '';
  private lastValue: string | number | null = null;
  private initialized = false;
  private dropdownCloseTimeout: ReturnType<typeof setTimeout> | null = null;
  private tabButtonRefs: HTMLElement[] = [];
  private dropdownMenuRef: HTMLElement | null = null;
  private justOpenedDropdown = false;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    if (this.tabs.length && (this.value === null || !this.tabs.some(tab => tab.value === this.value))) {
      this.value = this.tabs[0]?.value;
    }
  }

  ngAfterViewInit() {
    // Initial microtask deferral for DOM paint
    Promise.resolve().then(() => {
      this.initializeComponent();
      this.initialized = true;
    });
    // Track tab button refs for outside click detection
    setTimeout(() => {
      this.tabButtonRefs = this.tabButtons.map(ref => ref.nativeElement);
    });
  }

  ngAfterViewChecked() {
    // Only recalculate if tabs or value changed
    const tabsSnapshot = JSON.stringify(this.tabs.map(t => ({ label: t.label, value: t.value, icon: t.icon, iconPosition: t.iconPosition, disabled: t.disabled })));
    if (tabsSnapshot !== this.lastTabsSnapshot || this.value !== this.lastValue) {
      this.lastTabsSnapshot = tabsSnapshot;
      this.lastValue = this.value;
      Promise.resolve().then(() => {
        this.initializeComponent();
      });
    }
  }

  ngOnDestroy() {
    this.resizeObserver?.disconnect();
    this.removeDocumentClickListener();
  }

  private initializeComponent() {
    this.checkForOverflow();
    this.updateUnderlinePosition();
    this.resizeObserver = new ResizeObserver(() => {
      this.checkForOverflow();
      this.updateUnderlinePosition();
    });
    this.resizeObserver.observe(this.tabList.nativeElement);
  }

  public onTabClick(tab: AvaTab, event?: Event) {
    const idx = this.tabs.findIndex(t => t.value === tab.value);
    if (tab.dropdown) {
      if (this.openDropdownIndex === idx) {
        this.openDropdownIndex = null;
        this.dropdownPosition = null;
        this.isTabHovered = null;
        this.isDropdownHovered = null;
        this.removeDocumentClickListener();
        return;
      } else {
        this.openDropdownIndex = idx;
        if (event && event.currentTarget) {
          const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
          const scrollY = window.scrollY || window.pageYOffset;
          this.dropdownPosition = {
            left: rect.left + rect.width / 2,
            top: rect.bottom + scrollY
          };
        } else {
          this.dropdownPosition = { left: 0, top: 48 };
        }
        this.justOpenedDropdown = true;
        this.addDocumentClickListener();
        return;
      }
    }
    if (!tab.disabled && tab.value !== this.value) {
      this.value = tab.value;
      this.updateUnderlinePosition();
      this.valueChange.emit(tab.value);
    }
    this.tabClick.emit(tab);
  }

  public onTabHover(tab: AvaTab) {
    this.tabHover.emit(tab);
  }

  public onTabFocus(tab: AvaTab) {
    this.tabFocus.emit(tab);
  }

  public onTabBlur(tab: AvaTab) {
    this.tabBlur.emit(tab);
  }

  public scroll(direction: 'left' | 'right'): void {
    const scrollAmount = direction === 'left' ? -200 : 200;
    this.tabList.nativeElement.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    setTimeout(() => {
      this.updateScrollButtonState();
    }, 300);
  }

  private checkForOverflow() {
    if (!this.tabList) return;
    const el = this.tabList.nativeElement;
    const hasOverflow = el.scrollWidth > el.clientWidth;
    this.showScrollButtons = this.showChevrons && hasOverflow;
    this.updateScrollButtonState();
    this.cdr.detectChanges();
  }

  public updateScrollButtonState(): void {
    if (!this.tabList) return;
    const el = this.tabList.nativeElement;
    this.disableScrollLeft = el.scrollLeft === 0;
    this.disableScrollRight = el.scrollLeft + el.clientWidth >= el.scrollWidth - 1;
    this.cdr.detectChanges();
  }

  private updateUnderlinePosition() {
    if (!this.tabButtons || this.tabButtons.length === 0 || !this.tabList) return;
    const idx = this.tabs.findIndex(tab => tab.value === this.value);
    if (idx === -1) return;
    const tabElement = this.tabButtons.get(idx)?.nativeElement;
    if (tabElement) {
      const navRect = this.tabList.nativeElement.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();
      this.underline.width = tabRect.width;
      this.underline.left = tabRect.left - navRect.left + this.tabList.nativeElement.scrollLeft;
      this.cdr.detectChanges();
    }
  }

  get activeTab(): AvaTab | undefined {
    return this.tabs.find(tab => tab.value === this.value);
  }

  public onDropdownItemClick(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {
    this.value = tab.value;
    this.valueChange.emit(tab.value);
    this.dropdownSelect.emit({ parent: tab, item });
    this.dropdownItemClick.emit({ parent: tab, item });
    this.openDropdownIndex = null;
  }

  public onDropdownItemHover(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {
    this.dropdownItemHover.emit({ parent: tab, item });
  }

  public onDropdownItemFocus(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {
    this.dropdownItemFocus.emit({ parent: tab, item });
  }

  public onDropdownItemBlur(tab: AvaTab, item: { label: string; value: string | number; icon?: string }) {
    this.dropdownItemBlur.emit({ parent: tab, item });
  }

  public onTabDropdownEnter(i: number, tabButton?: HTMLElement) {
    this.isTabHovered = i;
    this.openDropdownIndex = i;
    if (this.dropdownCloseTimeout) {
      clearTimeout(this.dropdownCloseTimeout);
      this.dropdownCloseTimeout = null;
    }
    if (tabButton) {
      const rect = tabButton.getBoundingClientRect();
      const scrollY = window.scrollY || window.pageYOffset;
      this.dropdownPosition = {
        left: rect.left + rect.width / 2,
        top: rect.bottom + scrollY
      };
    }
    this.addDocumentClickListener();
  }

  public onTabDropdownLeave(i: number) {
    this.isTabHovered = null;
    if (this.dropdownCloseTimeout) {
      clearTimeout(this.dropdownCloseTimeout);
      this.dropdownCloseTimeout = null;
    }
    this.dropdownCloseTimeout = setTimeout(() => {
      if (this.isTabHovered !== i && this.isDropdownHovered !== i) {
        this.openDropdownIndex = null;
        this.dropdownPosition = null;
      }
    }, 180);
  }

  public onDropdownMenuEnter(i: number, ref?: HTMLElement) {
    this.isDropdownHovered = i;
    this.openDropdownIndex = i;
    if (this.dropdownCloseTimeout) {
      clearTimeout(this.dropdownCloseTimeout);
      this.dropdownCloseTimeout = null;
    }
    if (ref) {
      this.dropdownMenuRef = ref;
    }
  }

  public onDropdownMenuLeave(i: number) {
    this.isDropdownHovered = null;
    if (this.dropdownCloseTimeout) {
      clearTimeout(this.dropdownCloseTimeout);
      this.dropdownCloseTimeout = null;
    }
    this.dropdownCloseTimeout = setTimeout(() => {
      if (this.isTabHovered !== i && this.isDropdownHovered !== i) {
        this.openDropdownIndex = null;
        this.dropdownPosition = null;
      }
    }, 180);
  }

  private addDocumentClickListener() {
    document.addEventListener('mousedown', this.handleDocumentClick, true);
  }

  private removeDocumentClickListener() {
    document.removeEventListener('mousedown', this.handleDocumentClick, true);
  }

  private handleDocumentClick = (event: MouseEvent) => {
    if (this.justOpenedDropdown) {
      this.justOpenedDropdown = false;
      return;
    }
    if (!this.dropdownMenuRef && this.openDropdownIndex !== null) return;
    const dropdownMenu = this.dropdownMenuRef;
    const tabButton = this.tabButtonRefs[this.openDropdownIndex!];
    if (
      dropdownMenu &&
      !dropdownMenu.contains(event.target as Node) &&
      tabButton &&
      !tabButton.contains(event.target as Node)
    ) {
      this.openDropdownIndex = null;
      this.dropdownPosition = null;
      this.isTabHovered = null;
      this.isDropdownHovered = null;
      this.removeDocumentClickListener();
    }
  };

  get customStyle() {
    return {
      ...(this.style || {}),
      ...(this.maxWidth ? { 'max-width': this.maxWidth, width: '100%' } : {})
    };
  }
} 