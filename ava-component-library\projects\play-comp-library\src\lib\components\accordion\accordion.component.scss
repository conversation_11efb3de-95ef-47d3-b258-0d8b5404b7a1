// Optional: define variables at the top
$accordion-gap: 0.5rem;
$accordion-width: 40rem; // 640px
$accordion-margin-bottom: 1.25rem; // 20px
$accordion-border-radius: 0.5rem; //8px
$accordion-background: var(--accordion-light-background);
$accordion-padding: 1.5rem;
$accordion-font-size: var(--accordion-content-font); // 1rem
$accordion-font-md-weight: 500;
$accordion-font-family: var(--accordion-font-family); //inter
$accordion-color-gray: var(--accordion-dark-header-background);
$accordion-dark-bg-color: var(--accordion-dark-content-text);

.accordion-container {
  width: 100%;
  max-width: $accordion-width;
  border-radius: $accordion-border-radius; // 8px
  overflow: hidden;
  margin-bottom: $accordion-margin-bottom;
  background: $accordion-background;
  box-shadow: 0rem 0.125rem 0.25rem 0.0625rem rgba(0, 0, 0, 0.12);

  .accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: $accordion-width; // 640px
    min-height: 4.5rem; // 72px
    padding: $accordion-padding $accordion-margin-bottom; // 24px 20px
    cursor: pointer;
    box-sizing: border-box;
    color: $accordion-color-gray;
    font-family: $accordion-font-family;
    font-size: $accordion-font-size; // 16px
    font-style: normal;
    font-weight: $accordion-font-md-weight;
    line-height: $accordion-font-size; // 16px
  }

  .accordion-body {
    display: flex;
    width: $accordion-width;
    padding: 0rem 1.25rem 1.5rem 1.5rem;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: $accordion-padding; // 24px
    border-radius: $accordion-border-radius; // 8px
    background: $accordion-background;
    color: var(--accordion-dark-content-background);
    font-family: $accordion-font-family;
    font-size: $accordion-font-size;
    font-style: normal;
    font-weight: var(--accordion-font-weight);
    line-height: $accordion-padding; // 24px
  }

  .accordion-title-highlight {
    color: $accordion-color-gray;
    font-family: $accordion-font-family;
    font-size: $accordion-font-size;
    font-style: normal;
    font-weight: $accordion-font-md-weight;
    line-height: $accordion-font-size;
  }

  .header-row {
    display: flex;
    align-items: center;
    gap: $accordion-gap;
    width: 100%;

    .accordion-title {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .right-aligned-icon {
      margin-left: auto;
    }
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: $accordion-padding; // 24px
    height: $accordion-padding;
  }

  .accordion-content.show {
    animation: slideDown 0.5s forwards;
  }

  .accordion-content:not(.show) {
    animation: slideUp 0.5s forwards;
  }
}

.accordion-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.accordion-container.accordion-dark {
  background: $accordion-dark-bg-color;
  .accordion-title {
    background: $accordion-dark-bg-color;
  }
  .accordion-title-highlight {
    background: $accordion-dark-bg-color;
    color: var(--accordion-background);
  }

  .accordion-body {
    background: $accordion-dark-bg-color;
    color: var(--accordion-background); //rgb(240, 241, 242);
  }
}
.header-row {
  display: flex;
  align-items: center;
  width: 100%;

  .accordion-title {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-aligned-icon {
    margin-left: auto;
  }
}
