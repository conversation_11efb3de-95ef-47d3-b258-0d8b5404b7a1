import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TabsComponent, AvaTab } from './tabs.component';
import { LucideAngularModule, User, Settings, ChevronLeft, ChevronRight, XCircle } from 'lucide-angular';
import { By } from '@angular/platform-browser';

describe('TabsComponent', () => {
  let component: TabsComponent;
  let fixture: ComponentFixture<TabsComponent>;
  const tabs: AvaTab[] = [
    { label: 'Tab 1', icon: 'user', content: 'Content 1', value: 'tab1' },
    { label: 'Tab 2', icon: 'settings', content: 'Content 2', disabled: true, value: 'tab2' },
    { label: 'Tab 3', content: 'Content 3', value: 'tab3' },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TabsComponent, 
        LucideAngularModule.pick({ <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight, XCircle })
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(TabsComponent);
    component = fixture.componentInstance;
    component.tabs = tabs;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render all tabs', () => {
    const tabButtons = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
    expect(tabButtons.length).toBe(3);
  });

  it('should render icons for tabs with icon property', () => {
    const icons = fixture.debugElement.queryAll(By.css('lucide-icon'));
    expect(icons.length).toBe(2);
  });

  it('should switch active tab on click', () => {
    const tabButtons = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
    tabButtons[2].nativeElement.click();
    fixture.detectChanges();
    expect(component.activeTabIndex).toBe(2);
  });

  it('should not switch to a disabled tab', () => {
    const tabButtons = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
    tabButtons[1].nativeElement.click();
    fixture.detectChanges();
    expect(component.activeTabIndex).toBe(0);
  });

  it('should emit tabChange event on tab click', () => {
    spyOn(component.tabChange, 'emit');
    const tabButtons = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
    tabButtons[2].nativeElement.click();
    fixture.detectChanges();
    expect(component.tabChange.emit).toHaveBeenCalledWith(2);
  });

  it('should display content of the active tab', () => {
    component.activeTabIndex = 2;
    fixture.detectChanges();
    const content = fixture.debugElement.query(By.css('.ava-tabs__content'));
    expect(content.nativeElement.textContent).toContain('Content 3');
  });
}); 