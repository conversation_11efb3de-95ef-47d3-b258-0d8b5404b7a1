<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Icon Component</h1>
        <p class="description">
          A versatile icon component that supports various icon sets, sizes, colors, and states. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} IconsComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div
            class="section-header"
            tabindex="0"
            role="button"
            (click)="toggleSection(i)"
            (keydown.enter)="toggleSection(i)"
            (keydown.space)="toggleSection(i)"
          >
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode && section.title!=='Available Icons'">View Code</span>
                <span *ngIf="section.showCode && section.title!=='Available Icons'">Hide Code</span>                
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action" *ngIf="section.title!=='Available Icons'"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="icons-grid">
                <ava-icon  [iconColor] = "'green'" [iconSize] = "20" [iconName] = "'square-pen'"></ava-icon>
                <ava-icon  [iconColor] = "'red'" [iconSize] = "30" [iconName] = "'circle-check'"></ava-icon>
                 <ava-icon [cursor] = "true" (userClick) = "userClick($event)" [iconColor] = "'blue'" [iconSize] = "30" [iconName] = "'wifi'"></ava-icon>
                
                <!-- <awe-icons iconName="awe_edit"></awe-icons>
                <awe-icons iconName="awe_save" iconColor="success"></awe-icons>
                <awe-icons iconName="awe_trash" iconColor="danger"></awe-icons> -->
              </div>
            </ng-container>
            <!-- <ng-container *ngSwitchCase="'Icon Variants'">
              <div class="icons-grid">
                <awe-icons iconName="awe_edit" iconColor="action"></awe-icons>
              </div>
            </ng-container> -->
            <!-- <ng-container *ngSwitchCase="'Custom Colors'">
              <div class="icons-grid">
              </div>
            </ng-container> -->
            <ng-container *ngSwitchCase="'Disabled State'">
              <div class="icons-grid">
                <ava-icon  [disabled] ="true" [iconColor] = "'green'" [iconSize] = "20" [iconName] = "'square-pen'"></ava-icon>
                <ava-icon  [disabled] ="true" [iconColor] = "'red'" [iconSize] = "30" [iconName] = "'circle-check'"></ava-icon>
                <ava-icon [disabled] ="true"   [iconColor] = "'blue'" [iconSize] = "30" [iconName] = "'wifi'"></ava-icon>

              </div>
            </ng-container>
           
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>
