import { Component, ViewChild, ElementRef, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InputComponent } from "../../../../../play-comp-library/src/lib/components/input/input.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";
import { ButtonComponent } from '../../../../../play-comp-library/src/public-api';

interface InputDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-input',
  standalone: true,
  imports: [CommonModule, InputComponent, IconsComponent, ButtonComponent],
  templateUrl: './app-input.component.html',
  styleUrls: ['./app-input.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppInputComponent {
  // Documentation sections
  sections: InputDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Basic implementation of the input component with icons.',
      showCode: false
    },
    {
      title: 'States and Colors',
      description: 'Input fields with different border colors and states.',
      showCode: false
    },
    {
      title: 'Validation',
      description: 'Input fields with different validation rules and error messages.',
      showCode: false
    },
    {
      title: 'Variants',
      description: 'Different width variants of the input component.',
      showCode: false
    },
    {
      title: 'Loading States',
      description: 'Input fields with different loading state presentations.',
      showCode: false
    },
    {
      title: 'Input with Single Icons',
      description: 'Input fields with single icon functionality like search and password visibility.',
      showCode: false
    },
    {
      title: 'Expanded Input',
      description: 'Input fields that can expand to full width.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    { 
      name: 'label',
      type: 'string',
      default: '""',
      description: 'Label text for the input field.'
    },
    {
      name: 'placeholder',
      type: 'string',
      default: '""',
      description: 'Placeholder text for the input field.'
    },
    {
      name: 'icons',
      type: 'string[]',
      default: '[]',
      description: 'Array of icon names to display in the input field.'
    },
    {
      name: 'iconColor',
      type: 'string',
      default: '"default"',
      description: 'Color of the icons in the input field.'
    },
    {
      name: 'borderColor',
      type: 'boolean',
      default: 'true',
      description: 'Whether to use state-based border colors.'
    },
    {
      name: 'status',
      type: 'string',
      default: '""',
      description: 'Status color of the input (blue, green, red, white).'
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description: 'Whether the input field is required.'
    },
    {
      name: 'pattern',
      type: 'string',
      default: '""',
      description: 'Regular expression pattern for input validation.'
    },
    {
      name: 'errorMessage',
      type: 'string',
      default: '""',
      description: 'Error message to display when validation fails.'
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Whether the input field is disabled.'
    },
    {
      name: 'variant',
      type: '"fixed" | "fluid" | "compact"',
      default: '"fixed"',
      description: 'Width variant of the input field.'
    },
    {
      name: 'loading',
      type: 'boolean',
      default: 'false',
      description: 'Whether the input is in loading state.'
    },
    {
      name: 'loadingType',
      type: '"spinner" | "skeleton"',
      default: '"spinner"',
      description: 'Type of loading animation to display.'
    },
    {
      name: 'clearable',
      type: 'boolean',
      default: 'false',
      description: 'Whether the input field can be cleared with a button.'
    },
    {
      name: 'expand',
      type: 'boolean',
      default: 'false',
      description: 'Whether the input field should expand to full width.'
    }
  ];

  // Events documentation
  events = [
    {
      name: 'valueChange',
      type: 'EventEmitter<string>',
      description: 'Emitted when the input value changes.'
    },
    {
      name: 'iconClickEvent',
      type: 'EventEmitter<number>',
      description: 'Emitted when an icon is clicked, with the icon index.'
    },
    {
      name: 'clear',
      type: 'EventEmitter<void>',
      description: 'Emitted when the clear button is clicked.'
    }
  ];

  // Example state management
  isInputDisabled = false;
  inputValue = '';
  isLoading = false;
  hasError = false;
  showPassword = false;
  loadingTimer: number | undefined;

  // Section toggle
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Event handlers
  onInputChange(value: string): void {
    console.log('Input value changed:', value);
    this.inputValue = value;
  }
  onIconClick(index: number): void {
    if (index === 0 && this.sections.find((s: InputDocSection) => s.title === 'Special Features')?.showCode) {
      this.showPassword = !this.showPassword;
    }
    console.log('Icon clicked at index:', index);
  }

  onClear(): void {
    this.inputValue = '';
    console.log('Input cleared');
  }

  simulateLoading(): void {
    if (this.isLoading) return;

    this.isLoading = true;
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
    }

    this.loadingTimer = window.setTimeout(() => {
      this.isLoading = false;
      this.loadingTimer = undefined;
    }, 2000);
  }

  // Code example handling
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');    }).catch((err: Error) => {
      console.error('Failed to copy code:', err);
    });
  }  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-basic-input',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input 
        label="Basic Input"
        placeholder="Enter your text"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action"
        (valueChange)="onValueChange($event)"
        (iconClickEvent)="onIconClick($event)">
      </awe-input>
    \`
  })
  export class BasicInputComponent {
    onValueChange(value: string) {
      console.log('Input value changed:', value);
    }
  
    onIconClick(index: number) {
      console.log('Icon clicked at index:', index);
    }
  }`,

      'states and colors': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-input-states',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input 
        label="Border Color Blue" 
        placeholder="Enter your text" 
        [borderColor]="false"
        status="blue"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action">
      </awe-input>

      <awe-input 
        label="Border Color Green" 
        placeholder="Enter your text" 
        [borderColor]="false"
        status="green"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action">
      </awe-input>

      <awe-input 
        label="Border Color Red" 
        placeholder="Enter your text" 
        [borderColor]="false"
        status="red"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action">
      </awe-input>

      <awe-input 
        label="Border Color White" 
        placeholder="Enter your text" 
        [borderColor]="false"
        status="white"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action">
      </awe-input>
    \`
  })
  export class InputStatesComponent {
    onValueChange(value: string) {
      console.log('Input value changed:', value);
    }
  }`,

      'validation': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-input-validation',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input
        label="Required Field"
        placeholder="This field is required"
        [required]="true"
        errorMessage="This field is required"
        [icons]="['awe_send']"
        iconColor="action">
      </awe-input>

      <awe-input 
        label="Email Validation" 
        placeholder="Enter your Gmail address"
        [required]="true" 
        pattern="[a-z0-9._%+-]+@gmail.com$"
        errorMessage="Please enter a valid Gmail address"
        [icons]="['awe_send']"
        iconColor="action">
      </awe-input>
    \`
  })
  export class InputValidationComponent {
    onValueChange(value: string) {
      console.log('Input value changed:', value);
    }
  }`,

      'variants': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-input-variants',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input
        label="Fixed Width"
        placeholder="Fixed width input"
        variant="fixed"
        [icons]="['awe_send']"
        iconColor="action">
      </awe-input>

      <awe-input
        label="Fluid Width"
        placeholder="Fluid width input"
        variant="fluid"
        [icons]="['awe_send']"
        iconColor="action">
      </awe-input>

      <awe-input
        label="Compact Width"
        placeholder="Compact width input"
        variant="compact"
        [icons]="['awe_send']"
        iconColor="action">
      </awe-input>
    \`
  })
  export class InputVariantsComponent {
    onValueChange(value: string) {
      console.log('Input value changed:', value);
    }
  }`,

      'loading states': `
  import { Component } from '@angular/core';
  import { InputComponent, ButtonComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-input-loading',
    standalone: true,
    imports: [InputComponent, ButtonComponent],
    template: \`
      <awe-input
        label="Spinner Loading"
        placeholder="Loading with spinner..."
        [loading]="isLoading"
        loadingType="spinner"
        [icons]="['awe_search']">
      </awe-input>

      <awe-input
        label="Skeleton Loading"
        placeholder="Loading with skeleton..."
        [loading]="isLoading"
        loadingType="skeleton"
        skeletonWidth="240px"
        [icons]="['awe_search']">
      </awe-input>

      <awe-button
        (click)="simulateLoading()"
        [disabled]="isLoading">
        {{ isLoading ? 'Loading...' : 'Simulate Loading' }}
      </awe-button>
      
      <span class="loading-hint" *ngIf="isLoading">
        Loading will complete in 2 seconds
      </span>
    \`
  })
  export class InputLoadingComponent {
    isLoading = false;

    simulateLoading() {
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
      }, 2000);
    }
  }`,

      'input with single icons': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-input-with-icons',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input
        label="Search"
        placeholder="Search items..."
        [icons]="['awe_search']"
        [clearable]="true"
        (iconClickEvent)="onIconClick($event)"
        (clear)="onClear()">
      </awe-input>

      <awe-input
        label="Password"
        placeholder="Enter password"
        type="password"
        [icons]="['awe_visibility']"
        (iconClickEvent)="onIconClick($event)">
      </awe-input>
    \`
  })
  export class InputWithIconsComponent {
    onIconClick(index: number) {
      console.log('Icon clicked at index:', index);
    }

    onClear() {
      console.log('Input cleared');
    }
  }`,

      'expanded input': `
  import { Component } from '@angular/core';
  import { InputComponent } from '@awe/play-comp-library';
  
  @Component({
    selector: 'app-expanded-input',
    standalone: true,
    imports: [InputComponent],
    template: \`
      <awe-input
        label="Expanded Input"
        placeholder="This is an expanded input field"
        [expand]="true"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action">
      </awe-input>

      <awe-input
        label="Expanded Input without state color"
        placeholder="This is an expanded input field"
        [expand]="true"
        [icons]="['awe_send', 'awe_mic', 'awe_close']"
        iconColor="action"
        [borderColor]="false">
      </awe-input>
    \`
  })
  export class ExpandedInputComponent {
    onValueChange(value: string) {
      console.log('Input value changed:', value);
    }
  }`
    };

    return examples[section.toLowerCase()] || '';
  }
}

