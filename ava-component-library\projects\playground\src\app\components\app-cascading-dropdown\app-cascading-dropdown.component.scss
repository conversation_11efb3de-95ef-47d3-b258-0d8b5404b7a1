.cascading-dropdown-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    margin-bottom: 2rem;
    text-align: center;

    h2 {
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--text-secondary);
      font-size: 1.1rem;
    }
  }

  .dropdown-container {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.5rem;
    }

    .dropdown-section {
      flex: 1;
      min-width: 250px;

      h4 {
        margin-bottom: 1rem;
        color: var(--text-primary);
        font-weight: 600;
      }
    }
  }

  .selections-display {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .selection-item {
      margin-bottom: 0.5rem;
      padding: 0.5rem;
      background: var(--background-primary);
      border-radius: 4px;
      border-left: 4px solid var(--primary-color);

      strong {
        color: var(--primary-color);
      }
    }
  }

  .data-display {
    background: var(--success-background, #f0f9ff);
    border: 1px solid var(--success-border, #0ea5e9);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: var(--success-color, #0ea5e9);
    }

    h5 {
      margin-bottom: 0.5rem;
      color: var(--text-primary);
      font-size: 1rem;
    }

    .data-section {
      margin-bottom: 1.5rem;

      pre {
        background: var(--code-background, #f8f9fa);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
        white-space: pre-wrap;
      }

      ul {
        margin: 0;
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;

          code {
            background: var(--code-background, #f8f9fa);
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: var(--code-color, #e11d48);
          }
        }
      }
    }
  }

  .actions {
    text-align: center;
    margin-bottom: 2rem;

    .reset-btn {
      background: var(--danger-color, #dc2626);
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        background: var(--danger-hover, #b91c1c);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.3);
      }
    }
  }

  .instructions {
    background: var(--info-background);
    border: 1px solid var(--info-border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;

    h4 {
      margin-bottom: 1rem;
      color: var(--info-color);
    }

    ol {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
        line-height: 1.5;
      }
    }
  }

  // 5-Level Cascading Dropdown Styles
  .five-level-example {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-color);

    h2 {
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--text-secondary);
      font-size: 1.1rem;
      margin-bottom: 2rem;
    }

    .five-level-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .dropdown-section {
        h4 {
          margin-bottom: 1rem;
          color: var(--text-primary);
          font-weight: 600;
          font-size: 0.9rem;
        }
      }
    }

    .location-display {
      background: var(--background-secondary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;

      h4 {
        margin-bottom: 1rem;
        color: var(--text-primary);
      }

      .location-item {
        margin-bottom: 0.5rem;
        padding: 0.5rem;
        background: var(--background-primary);
        border-radius: 4px;
        border-left: 4px solid var(--success-color, #10b981);

        strong {
          color: var(--success-color, #10b981);
        }
      }
    }

    .location-data-display {
      background: var(--success-background, #f0fdf4);
      border: 1px solid var(--success-border, #10b981);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;

      h4 {
        margin-bottom: 1rem;
        color: var(--success-color, #10b981);
      }

      h5 {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
        font-size: 1rem;
      }

      .location-data-section {
        pre {
          background: var(--code-background, #f8f9fa);
          border: 1px solid var(--border-color);
          border-radius: 4px;
          padding: 1rem;
          font-family: 'Courier New', monospace;
          font-size: 0.9rem;
          overflow-x: auto;
          white-space: pre-wrap;
        }
      }
    }
  }

  .code-section {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;

    h4 {
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .code-block {
      background: var(--code-background, #f8f9fa);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 1rem;
      overflow-x: auto;

      pre {
        margin: 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        line-height: 1.4;

        code {
          color: var(--code-color, #333);
          white-space: pre-wrap;
        }
      }
    }
  }
}
