import { Component, signal, ViewEncapsulation } from '@angular/core';
import { RadioButtonComponent } from '../../../../../play-comp-library/src/public-api';
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';

interface RadioButtonDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-radiobutton',
  standalone: true,
  imports: [RadioButtonComponent, CommonModule,IconsComponent],
  templateUrl: './app-radiobutton.component.html',
  styleUrl: './app-radiobutton.component.scss',
    encapsulation: ViewEncapsulation.None,
})
export class AppRadiobuttonComponent {
  // Documentation sections
  sections: RadioButtonDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple radio button group with default configuration.',
      showCode: false,
    },
    {
      title: 'Radio Button Variants',
      description: 'Different radio button configurations including disabled and animated options.',
      showCode: false,
    },
    {
      title: 'Orientation',
      description: 'Radio buttons in horizontal and vertical layouts.',
      showCode: false,
    },
    {
      title: 'Customization',
      description: 'Customize radio buttons with various themes and styles.',
      showCode: false,
    },
    {
      title: 'Accessibility',
      description: 'Fully accessible radio button component with enhanced features.',
      showCode: false,
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'options',
      type: 'RadioButtonOption[]',
      default: '[]',
      description: 'Array of radio button options.',
    },
    {
      name: 'name',
      type: 'string',
      default: '"radioGroup"',
      description: 'Name attribute for the radio button group.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disable entire radio button group.',
    },
    {
      name: 'enableAnimation',
      type: 'boolean',
      default: 'false',
      description: 'Enable animation for radio button selection.',
    },
    {
      name: 'orientation',
      type: "'horizontal' | 'vertical'",
      default: 'horizontal',
      description: 'Layout orientation of radio buttons.',
    },
    {
      name: 'customClass',
      type: 'string',
      default: '""',
      description: 'Apply custom CSS class to radio button group.',
    }
  ];

  // Example state management
  basicOptions = signal([
    { value: 'option1', label: 'Option 1', checked: true },
    { value: 'option2', label: 'Option 2', checked: false },
    { value: 'option3', label: 'Option 3', checked: false }
  ]);

  disabledOptions = signal([
    { value: 'option4', label: 'Option 4', disabled: true },
    { value: 'option5', label: 'Option 5', disabled: false, checked: true },
    { value: 'option6', label: 'Option 6', disabled: true }
  ]);

  animatedOptions = signal([
    { value: 'option7', label: 'Option 7', enableAnimation: true, checked: true },
    { value: 'option8', label: 'Option 8', enableAnimation: true },
    { value: 'option9', label: 'Option 9', enableAnimation: true }
  ]);

  verticalOptions = signal([
    { value: 'option10', label: 'Option 10' },
    { value: 'option11', label: 'Option 11', checked: true },
    { value: 'option12', label: 'Option 12' }
  ]);
    horizontalOptions = signal([
    { value: 'option13', label: 'Option 13' },
    { value: 'option14', label: 'Option 14'},
    { value: 'option15', label: 'Option 15' }
  ]);

  // Customization Options
  customizationOptions = signal([
    { 
      value: 'theme1', 
      label: 'Light Theme', 
      checked: true,
      customClass: 'custom-light-theme'
    },
    { 
      value: 'theme2', 
      label: 'Dark Theme', 
      customClass: 'custom-dark-theme'
    },
    { 
      value: 'theme3', 
      label: 'Colorful Theme', 
      customClass: 'custom-colorful-theme'
    }
  ]);

  // Accessibility Options
  accessibilityOptions = signal([
    { 
      value: 'screen-reader', 
      label: 'Screen Reader Friendly', 
      ariaLabel: 'Enable screen reader support',
      checked: true
    },
    { 
      value: 'high-contrast', 
      label: 'High Contrast Mode', 
      ariaLabel: 'Enable high contrast accessibility',
      customClass: 'high-contrast-option'
    },
    { 
      value: 'keyboard-nav', 
      label: 'Keyboard Navigation', 
      ariaLabel: 'Enable enhanced keyboard navigation'
    }
  ]);

  // Function to handle radio button selection properly
  onSelectionChange(event: any) {
    const value = event?.detail || event?.target?.value;
    console.log('Selected value:', value);

    // Update the state so only one is checked at a time
    this.basicOptions.update(options => {
      return options.map(option => ({
        ...option,
        checked: option.value === value
      }));
    });
  }

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': 
`// Basic radio button example
import { Component, signal } from '@angular/core';
import { RadioButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-radio-button',
  standalone: true,
  imports: [RadioButtonComponent],
  template: \`
    <awe-radio-button
      [options]="options"
      (selectionChange)="onSelectionChange($event)"
    ></awe-radio-button>
  \`
})
export class BasicRadioButtonComponent {
  options = signal([
    { value: 'option1', label: 'Option 1', checked: true },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ]);

  onSelectionChange(value: string): void {
    console.log('Selected value:', value);
  }
}`,

      'radio button variants': 
`// Radio button variants example
import { Component, signal } from '@angular/core';
import { RadioButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-radio-button-variants',
  standalone: true,
  imports: [RadioButtonComponent],
  template: \`
    <div class="radio-variants-container">
      <h3>Disabled Options</h3>
      <awe-radio-button [options]="disabledOptions"></awe-radio-button>

      <h3>Animated Options</h3>
      <awe-radio-button [options]="animatedOptions"></awe-radio-button>
    </div>
  \`,
})
export class RadioButtonVariantsComponent {
  disabledOptions = signal([
    { value: 'option1', label: 'Disabled Option', disabled: true },
    { value: 'option2', label: 'Enabled Option', checked: true }
  ]);

  animatedOptions = signal([
    { value: 'option3', label: 'Animated Option', enableAnimation: true },
    { value: 'option4', label: 'Static Option', enableAnimation: false }
  ]);
}`,

      'orientation': 
`// Radio button orientation example
import { Component, signal } from '@angular/core';
import { RadioButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-radio-button-orientation',
  standalone: true,
  imports: [RadioButtonComponent],
  template: \`
    <div class="orientation-container">
     <h3>Vertical Layout</h3>
      <awe-radio-button 
        [options]="verticalOptions"
        orientation="vertical"
      ></awe-radio-button>
      <h3>Horizontal Layout</h3>
          <awe-radio-button
                    [options]="horizontalOptions()"
                    orientation="horizontal"
                  ></awe-radio-button>
    </div>
  \`,
})
export class RadioButtonOrientationComponent {
  verticalOptions = signal([
    { value: 'option4', label: 'Option 4' },
    { value: 'option5', label: 'Option 5', checked: true },
    { value: 'option6', label: 'Option 6' }
  ]);
      horizontalOptions = signal([
    { value: 'option13', label: 'Option 13' },
    { value: 'option14', label: 'Option 14'},
    { value: 'option15', label: 'Option 15' }
  ]);
}`,

      'customization': 
`// Radio button customization example
import { Component, signal } from '@angular/core';
import { RadioButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-radio-button-customization',
  standalone: true,
  imports: [RadioButtonComponent],
  template: \`
    <div class="customization-container">
      <h3>Theme Customization</h3>
      <awe-radio-button 
        [options]="customizationOptions"
        name="theme-selector"
      ></awe-radio-button>
    </div>
  \`,
})
export class RadioButtonCustomizationComponent {
  customizationOptions = signal([
    { 
      value: 'theme1', 
      label: 'Light Theme', 
      checked: true,
      customClass: 'custom-light-theme'
    },
    { 
      value: 'theme2', 
      label: 'Dark Theme', 
      customClass: 'custom-dark-theme'
    },
    { 
      value: 'theme3', 
      label: 'Colorful Theme', 
      customClass: 'custom-colorful-theme'
    }
  ]);
}`,

      'accessibility': 
`// Radio button accessibility example
import { Component, signal } from '@angular/core';
import { RadioButtonComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-radio-button-accessibility',
  standalone: true,
  imports: [RadioButtonComponent],
  template: \`
    <div class="accessibility-container">
      <h3>Accessibility Options</h3>
      <awe-radio-button 
        [options]="accessibilityOptions"
        name="accessibility-settings"
      ></awe-radio-button>
    </div>
  \`,
  styles: [\`
    /* High contrast option styling */
    :host ::ng-deep .high-contrast-option {
      border: 2px solid #000;
      background-color: #fff;
      color: #000;
    }
  \`]
})
export class RadioButtonAccessibilityComponent {
  accessibilityOptions = signal([
    { 
      value: 'screen-reader', 
      label: 'Screen Reader Friendly', 
      ariaLabel: 'Enable screen reader support',
      checked: true
    },
    { 
      value: 'high-contrast', 
      label: 'High Contrast Mode', 
      ariaLabel: 'Enable high contrast accessibility',
      customClass: 'high-contrast-option'
    },
    { 
      value: 'keyboard-nav', 
      label: 'Keyboard Navigation', 
      ariaLabel: 'Enable enhanced keyboard navigation'
    }
  ]);
}`
    };

    return examples[section.toLowerCase()] || '';
  }

  // Method to copy code to clipboard
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}