/* Grid container for icons */
.icons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* Responsive grid */
    gap: 1.5rem;
    padding: 1rem;
    justify-items: center;
    align-items: center;
    text-align: center;
  }
  
  /* Style for individual icon containers */
  .icons {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 160px; /* Height adjusted for better alignment */
    padding: 1.5rem; /* Slightly increased padding */
    background-color: #f5f5f5; /* Soft gray background */
    border-radius: 12px; /* Rounded corners */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Slightly stronger shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    cursor: pointer;
  
    &:hover {
      transform: scale(1.05); /* Zoom effect on hover */
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15); /* More prominent shadow */
      background-color: #e7e7e7; /* Subtle background change on hover */
    }
  
    /* Styling the icon inside */
    awe-icons {
      font-size: 2.5rem; /* Larger icon size */
      margin-bottom: 0.75rem; /* Increased space between icon and text */
      color: #333; /* Default color for icons */
      transition: color 0.3s ease;
  
      &:hover {
        color: #4caf50; /* Icon color change on hover */
      }
    }
  
    /* Styling the text under each icon */
    span {
      font-size: 0.85rem; /* Slightly larger text */
      color: #333; /* Dark color for better visibility */
      font-weight: 600; /* Bold font weight for better readability */
      padding: 0 5px;
      word-break: break-word;
      transition: color 0.3s ease;
    }
  }
  
  /* "Copied!" label style */
  .copied-label {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #4caf50; /* Green background */
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    opacity: 0; /* Initially hidden */
    animation: fadeInOut 2s ease forwards;
  }
  
  /* FadeInOut animation for "Copied!" label */
  @keyframes fadeInOut {
    0% {
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  
  /* Hover effect for individual icons to indicate interaction */
  .icons:hover .copied-label {
    animation: none; /* Stop animation on hover */
    opacity: 1; /* Keep label visible while hovering */
  }
  :host {
    display: block;
    width: 100%;
  }
   
  /* Prevent horizontal scroll */
  html,
  body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    width: 100%;
  }
   
  * {
    box-sizing: border-box;
  }
   
  /* Main layout */
  .documentation {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    overflow-x: hidden;
   
    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
   
  /* Header */
  .doc-header {
    h1 {
      font-size: 2.5rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }
   
    .description {
      font-size: 1.1rem;
      line-height: 1.6;
    }
  }
   
  /* Sections */
  .doc-sections {
    margin-top: 4rem;
  }
   
  .doc-section {
    margin-bottom: 1rem;
   
    h2 {
      font-size: 1.8rem;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 1.5rem;
    }
   
    p {
      font-size: 0.95rem;
      line-height: 1.5;
    }
  }
   
  /* Section header with toggle */
  .section-header {
    display: flex;
    flex-direction: column;
    position: relative;
    cursor: pointer;
    padding: 1rem;
    background-color: var(--surface);
    border-radius: var(--border-radius);
   
    h2 {
      margin-bottom: 0.5rem;
    }
   
    .description-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.5rem;
    }
   
    .code-toggle {
      font-size: 0.75rem;
      color: var(--icons-action);
      cursor: pointer;
      display: flex;
      align-items: center;
      font-weight: var(--font-font-weight-medium);
      font-family: var(--font-font-family-heading);
   
      &:hover {
        text-decoration: underline;
      }
   
      span {
        margin-right: 0.5rem;
      }
   
      awe-icons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        line-height: 0;
        padding: 0;
        margin: 0;
        vertical-align: middle;
        flex-shrink: 0;
   
        svg {
          width: 60%;
          height: 80%;
          display: block;
        }
      }
    }
  }
   
  /* Code example styles */
  .code-example {
    margin-top: 1.5rem;
   
    .example-preview {
      padding: 1.5rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
      border: 1px solid var(--surface-border);
    }
   
    .code-block {
      position: relative;
      border-radius: 0.5rem;
      margin-top: 1rem;
      border: 1px solid var(--surface-border);
      background-color: var(--surface-ground);
   
      pre {
        margin: 0;
        padding: 1rem;
        border-radius: 0.25rem;
        overflow-x: auto;
      }
   
      .copy-button {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.5rem;
        background: transparent;
        border: none;
        cursor: pointer;
        color: var(--text-color-secondary);
   
        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
   
  /* API table styles */
  .api-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
   
    th,
    td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--surface-border);
    }
   
    th {
      background-color: var(--surface);
      font-weight: 600;
      color: var(--text-color-primary);
    }
   
    td {
      code {
        background-color: var(--surface);
        padding: 0.2rem 0.4rem;
        border-radius: var(--border-radius-sm);
        font-family: monospace;
      }
    }
  }
   
  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .documentation {
      padding: 1rem;
    }
  }