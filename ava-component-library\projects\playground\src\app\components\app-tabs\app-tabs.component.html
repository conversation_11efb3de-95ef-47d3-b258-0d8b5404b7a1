<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Tabs Component</h1>
        <p class="description">
          A demo of the Tabs component with Lucide icons, all icon positions,
          value-based selection, and optional chevrons.
        </p>
      </header>
    </div>
  </div>

  <ava-tabs
    [variant]="'default'"
    [tabs]="demoTabs"
    [value]="selectedTab"
    (valueChange)="onTabChange($event)"
    (tabClick)="onTabClick($event)"
    (tabHover)="onTabHover($event)"
    (tabFocus)="onTabFocus($event)"
    (tabBlur)="onTabBlur($event)"
    (dropdownItemClick)="onDropdownItemClick($event)"
    (dropdownItemHover)="onDropdownItemHover($event)"
    (dropdownItemFocus)="onDropdownItemFocus($event)"
    (dropdownItemBlur)="onDropdownItemBlur($event)"
    [showChevrons]="true"
    ariaLabel="Tabs with events"
  ></ava-tabs>
  <div class="row g-3">
    <!-- <div class="col-12 col-md-8 offset-md-2"> -->
    <!--
      NOTE: The parent container must have a constrained width (fixed or max-width)
      for the Tabs component to enable scrolling and show scroll buttons.
    -->
    <div
      style="
        max-width: 1000px;
        width: 100%;
        margin: 40px auto;
        /* overflow: hidden; */
      "
    ></div>
    <div style="margin-top: 2rem">
      <strong>Selected Tab Value:</strong> {{ selectedTab }}
    </div>

    <div style="max-width: 1000px; width: 100%; margin: 40px auto">
      <h2>Pill Variant Tabs with Dropdown (Libraries)</h2>
      <p>
        This demo shows a pill-shaped tabs bar with a "Libraries" tab that opens
        a dropdown menu. The dropdown is styled as a two-column card via
        <code>[dropdownMenuStyle]</code> prop. Each dropdown item has a primary
        icon, bold label, and subtitle below.
      </p>
      <ava-tabs
        [tabs]="demoTabsPillDropdown"
        [value]="selectedTabPillDropdown"
        (valueChange)="selectedTabPillDropdown = $event"
        [variant]="'button'"
        [buttonProps]="{ pill: true, variant: 'primary' }"
        [dropdownMenuStyle]="dropdownMenuStyle"
        ariaLabel="Pill variant tabs with Libraries dropdown"
      ></ava-tabs>
    </div>

    <div
      style="
        max-width: 1000px;
        width: 100%;
        margin: 40px auto;
        /* overflow: hidden; */
      "
    >
      <h2>Button Variant</h2>

      <ava-tabs
        [tabs]="demoTabs"
        [value]="selectedTabButton"
        (valueChange)="onTabChange1($event)"
        [showChevrons]="false"
        [variant]="'button'"
        ariaLabel="icon position tabs example"
        [buttonProps]="{
          visual: 'normal',
          variant: 'primary',
          size: 'medium',
          state: 'default',
        }"
      ></ava-tabs>
    </div>
    <div style="margin-top: 2rem">
      <strong>Selected Tab Value:</strong> {{ selectedTabButton }}
    </div>

    <div style="margin-top: 2rem">
      <strong>Selected Button Variant Tab Value:</strong>
      {{ selectedTabButton }}
    </div>

    <div
      style="
        max-width: 1000px;
        width: 100%;
        margin: 40px auto;
        /* overflow: hidden; */
      "
    >
      <ava-tabs
        [tabs]="demoTabs"
        [value]="selectedTabIcon"
        (valueChange)="selectedTabIcon = $event"
        variant="icon"
        [showChevrons]="true"
        ariaLabel="icon only tabs example"
      ></ava-tabs>
    </div>
    <div style="margin-top: 2rem">
      <strong>Selected Icon Variant Tab Value:</strong> {{ selectedTabIcon }}
    </div>

    <div style="margin-top: 2rem">
      <strong>Selected Container Variant Tab Value:</strong>
      {{ selectedTabContainer }}
    </div>

    <div style="max-width: 1000px; width: 100%; margin: 40px auto">
      <h2>Tabs with Pages (Per-Tab Content)</h2>
      <p>
        This demo shows the standard tabs pattern where each tab displays its
        own content panel below. Switching tabs updates the content area.
      </p>
      <ava-tabs
        [tabs]="demoTabsWithContent"
        [value]="selectedTabWithContent"
        (valueChange)="selectedTabWithContent = $event"
        [container]="true"
        [containerStyle]="{
          background: '#fff',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '24px 16px',
          margin: '32px 0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
        }"
        ariaLabel="Tabs with per-tab content"
      ></ava-tabs>
      <!-- <div style="padding: 24px 0; min-height: 80px">
        <div [innerHTML]="selectedTabWithContentObj?.content"></div>
      </div> -->
    </div>

    <div style="max-width: 1000px; width: 100%; margin: 40px auto">
      <h2>Tabs with Pages – Button Variant</h2>
      <p>
        This demo shows the <code>[variant]="'button'"</code> style with per-tab
        content, different icon placements, and a dropdown tab.
      </p>
      <ava-tabs
        [tabs]="demoTabsWithContentButton"
        [value]="selectedTabWithContentButton"
        (valueChange)="selectedTabWithContentButton = $event"
        [container]="true"
        [containerStyle]="{
          background: '#fff',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '24px 16px',
          margin: '32px 0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
        }"
        [variant]="'button'"
        ariaLabel="Tabs with per-tab content, button variant"
        [buttonProps]="{ visual: 'normal', variant: 'primary' }"
      ></ava-tabs>
      <div style="padding: 24px 0; min-height: 80px">
        <div [innerHTML]="selectedTabWithContentButtonObj?.content"></div>
      </div>
    </div>

    <div style="max-width: 1000px; width: 100%; margin: 40px auto">
      <h2>Tabs with Pages – Icon-Only Variant</h2>
      <p>
        This demo shows the <code>[variant]="'icon'"</code> style with per-tab
        content. Only icons are shown in the tab navigation.
      </p>
      <ava-tabs
        [tabs]="demoTabsWithContentIcon"
        [value]="selectedTabWithContentIcon"
        (valueChange)="selectedTabWithContentIcon = $event"
        [container]="true"
        [containerStyle]="{
          background: '#fff',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '24px 16px',
          margin: '32px 0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)'
        }"
        [variant]="'icon'"
        ariaLabel="Tabs with per-tab content, icon variant"
      ></ava-tabs>
      <div style="padding: 24px 0; min-height: 80px">
        <div [innerHTML]="selectedTabWithContentIconObj?.content"></div>
      </div>
    </div>

    <div style="max-width: 1000px; width: 100%; margin: 40px auto">
      <h2>Button Variant – Default</h2>
      <p>
        This demo uses <code>buttonProps</code> with
        <code>visual: 'normal'</code> (ava-button default).
      </p>
      <ava-tabs
        [tabs]="demoTabsButtonDefault"
        [value]="selectedTabButtonDefault"
        (valueChange)="selectedTabButtonDefault = $event"
        [variant]="'button'"
        [buttonProps]="{ visual: 'normal', variant: 'primary', pill: true }"
        ariaLabel="Button variant default"
      ></ava-tabs>
      <div style="margin-top: 1rem">
        <strong>Selected:</strong> {{ selectedTabButtonDefault }}
      </div>
    </div>
    <div
      style="
        background: url('/assets/glass-image.jpg') center/cover no-repeat;
        padding: 2rem;
        border-radius: 18px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        margin-bottom: 2rem;
      "
    >
      <!-- <p style="color: #fff; font-size: 1.1rem; margin-bottom: 1rem"></p> -->
      <ava-tabs
        [tabs]="demoTabsButtonGlass"
        [value]="selectedTabButtonGlass"
        (valueChange)="selectedTabButtonGlass = $event"
        [variant]="'button'"
        [buttonProps]="{
          visual: 'glass',
          variant: 'primary',
          size: 'medium',
          state: 'default'
        }"
        [wrapperStyle]="{
          background: 'rgba(255,255,255,0.18)',
          borderRadius: '14px',
          boxShadow: '0 4px 24px rgba(0,0,0,0.10)',
          padding: '2rem 1rem',
          backdropFilter: 'blur(12px)',
        }"
        [listStyle]="{
          background: 'transparent',
          borderRadius: '14px'
        }"
        ariaLabel="Button variant glassmorphic"
      ></ava-tabs>
    </div>

    <!-- </div> -->
  </div>
</div>
