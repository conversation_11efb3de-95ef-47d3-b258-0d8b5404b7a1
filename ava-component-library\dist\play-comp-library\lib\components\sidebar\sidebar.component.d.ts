import { ChangeDetectorRef, EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class SidebarComponent {
    private cdr;
    width?: string;
    collapsedWidth?: string;
    showCollapseButton?: boolean;
    isCollapsed?: boolean;
    collapseToggle: EventEmitter<boolean>;
    private _isCollapsed;
    constructor(cdr: ChangeDetectorRef);
    ngOnInit(): void;
    toggleCollapse(): void;
    get sidebarWidth(): string;
    get collapsed(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<SidebarComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<SidebarComponent, "ava-sidebar", never, { "width": { "alias": "width"; "required": false; }; "collapsedWidth": { "alias": "collapsedWidth"; "required": false; }; "showCollapseButton": { "alias": "showCollapseButton"; "required": false; }; "isCollapsed": { "alias": "isCollapsed"; "required": false; }; }, { "collapseToggle": "collapseToggle"; }, never, ["[slot=header]", "[slot=content]", "[slot=footer]"], true, never>;
}
