/**
 * =========================================================================
 * Play+ Design System: Token Index
 *
 * This file imports all design tokens in the correct order to establish
 * the token hierarchy as defined by the design token manifesto.
 *
 * Order of imports:
 * 1. Base tokens (global + semantic)
 * 2. Component tokens (semantic)
 * 3. Theme tokens (overrides)
 * =========================================================================
 */

/* ==========================================================================
   1. BASE TOKENS (Foundation Layer)
   ========================================================================== */
@use "./_base.css";

/* ==========================================================================
   2. COMPONENT TOKENS (Semantic Layer)
   ========================================================================== */
@use "./components/index";

/* ==========================================================================
   3. THEME TOKENS (Override Layer)
   ========================================================================== */

   
@use "./themes/light";
@use "./themes/dark";
@use "./themes/legacy";
/* Add more theme files as needed */
