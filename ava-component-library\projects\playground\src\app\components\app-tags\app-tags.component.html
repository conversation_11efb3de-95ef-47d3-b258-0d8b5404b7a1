<div class="ava-tags-demo">
  <h1>Tag Component Demo</h1>
  <section>
    <h2>Filled Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of filledTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [customStyle]="tag.customStyle" [icon]="tag.icon"
        [iconColor]="tag.iconColor" [iconPosition]="tag.iconPosition ?? 'start'" [avatar]="tag.avatar"
        [removable]="tag.removable ?? false" [disabled]="tag.disabled ?? false" [pill]="tag.pill ?? false"
        [size]="tag.size ?? 'md'"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Outlined Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of outlinedTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'outlined'" [customStyle]="tag.customStyle"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Pill Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of pillTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [pill]="tag.pill ?? false"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Removable Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of removableTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [removable]="tag.removable ?? false" [icon]="tag.icon"
        [iconPosition]="tag.iconPosition ?? 'start'" [avatar]="tag.avatar" (removed)="onRemove(tag.label)"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Disabled Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of disabledTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [removable]="tag.removable ?? false"
        [disabled]="tag.disabled ?? false"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Clickable Tags</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of clickableTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [icon]="tag.icon" [iconPosition]="tag.iconPosition ?? 'start'"
        (clicked)="onClick(tag.label)"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Size Variants</h2>
    <div class="tag-demo-row">
      <ava-tag *ngFor="let tag of sizeTags" [label]="tag.label" [color]="tag.color ?? 'default'"
        [variant]="tag.variant ?? 'filled'" [size]="tag.size ?? 'md'"></ava-tag>
    </div>
  </section>
  <section>
    <h2>Custom Color Tag</h2>
    <div class="tag-demo-row">
      <ava-tag label="Custom Purple" color="custom" variant="filled" [customStyle]="{
          '--tag-custom-bg': '#ede9fe',
          '--tag-custom-color': '#7c3aed',
          '--tag-custom-border': '1px solid #7c3aed'
        }"></ava-tag>
      <ava-tag label="Custom Outlined" color="custom" variant="outlined" [customStyle]="{
          '--tag-custom-bg': '#fff',
          '--tag-custom-color': '#7c3aed',
          '--tag-custom-border': '1px solid #7c3aed'
        }"></ava-tag>
    </div>
  </section>
</div>

<style>
  .ava-tags-demo {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .ava-tags-demo h1 {
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
  }

  .ava-tags-demo section {
    margin-bottom: 2.5rem;
  }

  .ava-tags-demo h2 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #444;
  }

  .tag-demo-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
</style>