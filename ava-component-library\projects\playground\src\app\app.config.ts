import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import {
  LucideAngularModule, User, Settings, Info, ChevronLeft, ChevronRight, XCircle, CircleCheck, CheckCircle2, TrendingUp,
  AlignVerticalDistributeStart, CircleCheckBig, Play, MoveLeft, CalendarDays, EllipsisVertical, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Lock, Edit, Trash, Plus, Minus, Eye, Home, Layout, ChevronDown, ChevronUp, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip,ArrowLeft,ArrowRight,PanelRightOpen,PanelRightClose


} from 'lucide-angular';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    importProvidersFrom(LucideAngularModule.pick({
      User, <PERSON>tings, Info, ChevronLeft, ChevronRight,ArrowRight,
      XCircle, CircleCheck, AlignVerticalDistributeStart, CircleCheckBig, MoveLeft, Play, CalendarDays, EllipsisVertical, CheckCircle2, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Lock, Edit, Trash, Plus,ArrowLeft, Minus, ChevronDown, ChevronUp, Eye, Home, Layout, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip,PanelRightClose,PanelRightOpen, TrendingUp

    })),
  ],
};
