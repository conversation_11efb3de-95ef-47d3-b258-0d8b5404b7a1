/**
 * =========================================================================
 * Play+ Design System: Light Theme
 *
 * Light theme overrides for semantic tokens.
 * This theme maintains the same token structure but changes
 * the visual appearance to a light mode.
 * =========================================================================
 */

[data-theme="light"] {
  /* --- Light Theme Color Overrides --- */
  /* PRIMARY (Vibrant Pink) */
  --color-brand-primary: var(--global-color-pink-500);
  --color-brand-primary-hover: var(--global-color-pink-700);
  --color-brand-primary-active: var(--global-color-purple-500);
  --color-surface-interactive-primary: var(--global-color-pink-500);
  --color-surface-interactive-primary-hover: var(--global-color-pink-700);
  --color-surface-interactive-primary-active: var(--global-color-purple-500);
  --color-border-primary: var(--global-color-pink-500);
  --color-border-primary-hover: var(--global-color-pink-700);
  --color-border-primary-active: var(--global-color-purple-500);
  --color-text-primary: var(--global-color-gray-700);
  --color-text-on-primary: var(--global-color-white);

  /* SECONDARY (Light Blue) */
  --color-brand-secondary: var(--global-color-blue-info-500);
  --color-brand-secondary-hover: var(--global-color-royal-blue-500);
  --color-brand-secondary-active: var(--global-color-royal-blue-700);
  --color-surface-interactive-secondary: var(--global-color-blue-100);
  --color-surface-interactive-secondary-hover: var(--global-color-blue-info-500);
  --color-surface-interactive-secondary-active: var(--global-color-royal-blue-500);
  --color-border-secondary: var(--global-color-blue-info-500);
  --color-border-secondary-hover: var(--global-color-royal-blue-500);
  --color-border-secondary-active: var(--global-color-royal-blue-700);
  --color-text-secondary: var(--global-color-royal-blue-500);
  --color-text-on-secondary: var(--global-color-white);
  --color-background-secondary: var(--global-color-blue-100);

  /* BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states */
  --color-text-placeholder: var(--global-color-gray-400);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-pink-500);
  --color-text-interactive-hover: var(--global-color-pink-700);
  --color-text-success: var(--global-color-green-500);
  --color-text-error: var(--global-color-red-500);
  --color-background-primary: var(--global-color-white);
  --color-background-disabled: var(--global-color-gray-100);
  --color-surface-interactive-default: var(--global-color-pink-500);
  --color-surface-interactive-hover: var(--global-color-pink-700);
  --color-surface-interactive-active: var(--global-color-pink-700);
  --color-surface-disabled: var(--global-color-gray-200);
  --color-surface-subtle-hover: var(--global-color-gray-100);
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  --color-border-interactive: var(--global-color-pink-500);
  --color-border-focus: var(--global-color-pink-500);
  --color-border-error: var(--global-color-red-500);
  --color-background-error: var(--global-color-red-500);
   /* Semantic Border Colors */
   --color-border-warning: var(--global-color-yellow-500);
   --color-border-success: var(--global-color-green-500 );
   --color-border-info: var(--global-color-blue-info-500);
 
   /* Semantic Text Colors */
   --color-text-warning: var(--global-color-yellow-600);
   --color-text-success: var(--global-color-green-600);
   --color-text-info: var(--global-color-blue-info-500);
 
   /* Semantic Background Colors */
   --color-background-warning: var(--global-color-yellow-500);
   --color-background-success: var(--global-color-green-500);
   --color-background-info: var(--global-color-blue-info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

   /* =======================
     RGB HELPER VARIABLES - For Alpha Transparency
     ======================= */
     --rgb-brand-primary: 233, 30, 99; /* From --color-brand-primary */
     --rgb-brand-secondary: 33, 150, 243; /* From --color-brand-secondary */
     --rgb-violet: 124, 58, 237; /* From --global-color-violet-500 */
     --rgb-royal-blue: 37, 99, 235; /* From --global-color-royal-blue-500 */
     --rgb-rose: 250, 112, 154; /* From --global-color-rose-500 */
     --rgb-marigold: 254, 225, 64; /* From --global-color-marigold-500 */
     --rgb-spearmint: 67, 189, 144; /* From --global-color-spearmint-500 */
     --rgb-white: 255, 255, 255; /* From --global-color-white */
     --rgb-black: 0, 0, 0; /* From --global-color-black */
   
     /* =======================
        GLASS (Surface) Intensity - Industry Leading Values
        ======================= */
     --surface-0:   var(--color-background-primary); /* Fully solid, no translucency */
     --surface-10:  rgba(var(--rgb-white), 0.95); /* Barely noticeable translucency */
     --surface-20:  rgba(var(--rgb-white), 0.90); /* Subtle translucency, maintains solidity */
     --surface-30:  rgba(var(--rgb-white), 0.85); /* Light glass effect, good readability */
     --surface-40:  rgba(var(--rgb-white), 0.80); /* Noticeable glass, enterprise friendly */
     --surface-50:  rgba(var(--rgb-white), 0.70); /* Medium frosted glass, balanced visibility */
     --surface-60:  rgba(var(--rgb-white), 0.60); /* Strong glass effect, creative contexts */
     --surface-70:  rgba(var(--rgb-white), 0.50); /* Heavy glass, floating UI elements */
     --surface-80:  rgba(var(--rgb-white), 0.40); /* Deep translucency, overlay effects */
     --surface-90:  rgba(var(--rgb-white), 0.30); /* Near-transparent, spotlight UIs */
     --surface-100: rgba(var(--rgb-white), 0.20); /* Maximum translucency, ambient overlays */
   
     /* Glass Blur Effects */
     --surface-blur-0:   0px;
     --surface-blur-10:  blur(2px);
     --surface-blur-20:  blur(4px);
     --surface-blur-30:  blur(6px);
     --surface-blur-40:  blur(8px);
     --surface-blur-50:  blur(12px);
     --surface-blur-60:  blur(16px);
     --surface-blur-70:  blur(20px);
     --surface-blur-80:  blur(24px);
     --surface-blur-90:  blur(30px);
     --surface-blur-100: blur(40px);
   
     /* =======================
        LIGHT (Feedback) Intensity - Premium Glow System
        ======================= */
     --light-0:   none; /* No feedback */
     --light-10:  0 1px 2px rgba(var(--rgb-black), 0.05); /* Minimal elevation */
     --light-20:  0 2px 4px rgba(var(--rgb-black), 0.08), 0 0 8px rgba(var(--rgb-brand-primary), 0.15); /* Subtle glow */
     --light-30:  0 4px 8px rgba(var(--rgb-black), 0.12), 0 0 12px rgba(var(--rgb-brand-primary), 0.25); /* Brand tint */
     --light-40:  0 6px 12px rgba(var(--rgb-black), 0.15), 0 0 16px rgba(var(--rgb-brand-primary), 0.35); /* Clear interaction */
     --light-50:  0 8px 16px rgba(var(--rgb-black), 0.18), 0 0 20px rgba(var(--rgb-brand-primary), 0.45); /* Focus state */
     --light-60:  0 12px 24px rgba(var(--rgb-black), 0.20), 0 0 24px rgba(var(--rgb-brand-primary), 0.50); /* Active state */
     --light-70:  0 16px 32px rgba(var(--rgb-black), 0.22), 0 0 28px rgba(var(--rgb-royal-blue), 0.55); /* Strong emphasis */
     --light-80:  0 20px 40px rgba(var(--rgb-black), 0.25), 0 0 32px rgba(var(--rgb-brand-primary), 0.60); /* High impact */
     --light-90:  0 24px 48px rgba(var(--rgb-black), 0.28), 0 0 36px rgba(var(--rgb-brand-secondary), 0.65); /* Dramatic effect */
     --light-100: 0 32px 64px rgba(var(--rgb-black), 0.30), 0 0 40px rgba(var(--rgb-brand-primary), 0.70); /* Maximum drama */
   
     /* Light Ring Effects for Focus */
     --light-ring-0:   none;
     --light-ring-10:  0 0 0 1px rgba(var(--rgb-brand-primary), 0.10);
     --light-ring-20:  0 0 0 2px rgba(var(--rgb-brand-primary), 0.15);
     --light-ring-30:  0 0 0 3px rgba(var(--rgb-brand-primary), 0.18);
     --light-ring-40:  0 0 0 4px rgba(var(--rgb-brand-primary), 0.20);
     --light-ring-50:  0 0 0 5px rgba(var(--rgb-brand-primary), 0.22);
     --light-ring-60:  0 0 0 6px rgba(var(--rgb-brand-primary), 0.25);
     --light-ring-70:  0 0 0 7px rgba(var(--rgb-brand-primary), 0.28);
     --light-ring-80:  0 0 0 8px rgba(var(--rgb-brand-primary), 0.30);
     --light-ring-90:  0 0 0 10px rgba(var(--rgb-brand-primary), 0.32);
     --light-ring-100: 0 0 0 12px rgba(var(--rgb-brand-primary), 0.35);
   
     /* =======================
        LIQUID (Motion) Intensity - Fluid Animation System
        ======================= */
     --motion-0:   linear; /* No easing, instant */
     --motion-10:  cubic-bezier(0.25, 0.1, 0.25, 1); /* Gentle ease */
     --motion-20:  cubic-bezier(0.4, 0, 0.2, 1); /* Material standard */
     --motion-30:  cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Smooth ease-out */
     --motion-40:  cubic-bezier(0.23, 1, 0.32, 1); /* Quart ease-out */
     --motion-50:  cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Back ease-out */
     --motion-60:  cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Back spring */
     --motion-70:  cubic-bezier(0.34, 1.56, 0.64, 1); /* Elastic ease-out */
     --motion-80:  cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Quint ease-out */
     --motion-90:  cubic-bezier(0.19, 1, 0.22, 1); /* Expo ease-out */
     --motion-100: cubic-bezier(0.86, 0, 0.07, 1); /* Extreme elastic */
   
     /* Motion Durations - More Distinct Differences */
     --motion-duration-0:   0ms;
     --motion-duration-10:  80ms;
     --motion-duration-20:  120ms;
     --motion-duration-30:  180ms;
     --motion-duration-40:  250ms;
     --motion-duration-50:  350ms;
     --motion-duration-60:  500ms;
     --motion-duration-70:  700ms;
     --motion-duration-80:  900ms;
     --motion-duration-90:  1200ms;
     --motion-duration-100: 1500ms;
   
     /* =======================
        GRADIENT (Expressive Layer) - Cinematic Gradients
        ======================= */
     --gradient-0:   none; /* No gradient */
     --gradient-10:  linear-gradient(90deg, transparent 0%, rgba(var(--rgb-brand-primary), 0.05) 100%); /* Subtle brand hint */
     --gradient-20:  linear-gradient(90deg, rgba(var(--rgb-brand-primary), 0.08) 0%, rgba(var(--rgb-brand-secondary), 0.08) 100%); /* Soft duo-tone */
     --gradient-30:  linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.12) 0%, rgba(var(--rgb-violet), 0.12) 100%); /* Diagonal flow */
     --gradient-40:  linear-gradient(120deg, rgba(var(--rgb-brand-primary), 0.15) 0%, rgba(var(--rgb-royal-blue), 0.15) 100%); /* Brand spectrum */
     --gradient-50:  linear-gradient(90deg, rgba(var(--rgb-brand-primary), 0.20) 0%, rgba(var(--rgb-violet), 0.15) 50%, rgba(var(--rgb-royal-blue), 0.20) 100%); /* Triple stop */
     --gradient-60:  linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.25) 0%, rgba(var(--rgb-marigold), 0.20) 50%, rgba(var(--rgb-violet), 0.25) 100%); /* Warm to cool */
     --gradient-70:  linear-gradient(120deg, rgba(var(--rgb-brand-primary), 0.30) 0%, rgba(var(--rgb-rose), 0.25) 25%, rgba(var(--rgb-violet), 0.30) 75%, rgba(var(--rgb-royal-blue), 0.25) 100%); /* Complex spectrum */
     --gradient-80:  radial-gradient(circle at 30% 30%, rgba(var(--rgb-brand-primary), 0.35) 0%, rgba(var(--rgb-violet), 0.30) 50%, rgba(var(--rgb-royal-blue), 0.35) 100%); /* Radial burst */
     --gradient-90:  conic-gradient(from 0deg at 50% 50%, rgba(var(--rgb-brand-primary), 0.40) 0deg, rgba(var(--rgb-marigold), 0.35) 72deg, rgba(var(--rgb-violet), 0.40) 144deg, rgba(var(--rgb-royal-blue), 0.35) 216deg, rgba(var(--rgb-brand-primary), 0.40) 288deg, rgba(var(--rgb-brand-primary), 0.40) 360deg); /* Conic spectrum */
     --gradient-100: linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.45) 0%, rgba(var(--rgb-rose), 0.40) 20%, rgba(var(--rgb-marigold), 0.35) 40%, rgba(var(--rgb-spearmint), 0.40) 60%, rgba(var(--rgb-violet), 0.45) 80%, rgba(var(--rgb-royal-blue), 0.40) 100%); /* Ultimate rainbow */
   
     /* Gradient Overlays for Glass Surfaces */
     --gradient-glass-0:   none;
     --gradient-glass-10:  linear-gradient(135deg, rgba(var(--rgb-white), 0.1) 0%, rgba(var(--rgb-white), 0.05) 100%);
     --gradient-glass-20:  linear-gradient(135deg, rgba(var(--rgb-white), 0.15) 0%, rgba(var(--rgb-brand-primary), 0.05) 100%);
     --gradient-glass-30:  linear-gradient(135deg, rgba(var(--rgb-white), 0.20) 0%, rgba(var(--rgb-brand-secondary), 0.08) 100%);
     --gradient-glass-40:  linear-gradient(135deg, rgba(var(--rgb-white), 0.25) 0%, rgba(var(--rgb-brand-primary), 0.10) 100%);
     --gradient-glass-50:  linear-gradient(135deg, rgba(var(--rgb-white), 0.30) 0%, rgba(var(--rgb-royal-blue), 0.12) 50%, rgba(var(--rgb-brand-primary), 0.10) 100%);
     --gradient-glass-60:  linear-gradient(135deg, rgba(var(--rgb-white), 0.35) 0%, rgba(var(--rgb-marigold), 0.15) 50%, rgba(var(--rgb-violet), 0.15) 100%);
     --gradient-glass-70:  linear-gradient(135deg, rgba(var(--rgb-white), 0.40) 0%, rgba(var(--rgb-rose), 0.18) 30%, rgba(var(--rgb-royal-blue), 0.18) 100%);
     --gradient-glass-80:  linear-gradient(135deg, rgba(var(--rgb-white), 0.45) 0%, rgba(var(--rgb-brand-primary), 0.20) 25%, rgba(var(--rgb-violet), 0.20) 75%, rgba(var(--rgb-royal-blue), 0.18) 100%);
     --gradient-glass-90:  linear-gradient(135deg, rgba(var(--rgb-white), 0.50) 0%, rgba(var(--rgb-marigold), 0.22) 33%, rgba(var(--rgb-brand-primary), 0.22) 66%, rgba(var(--rgb-violet), 0.22) 100%);
     --gradient-glass-100: linear-gradient(135deg, rgba(var(--rgb-white), 0.55) 0%, rgba(var(--rgb-rose), 0.25) 20%, rgba(var(--rgb-marigold), 0.20) 40%, rgba(var(--rgb-spearmint), 0.22) 60%, rgba(var(--rgb-violet), 0.25) 80%, rgba(var(--rgb-royal-blue), 0.22) 100%);

  /* Play+ Metaphor Semantic Variables for Textbox */
  /* Glass Metaphor */
  --surface-glass-bg: rgba(255,255,255,0.15);
  --surface-glass-border: rgba(255,255,255,0.7);
  --surface-glass-shadow: rgba(255,255,255,0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(233,30,99,0.45);
  --color-light-glow-focus: rgba(233,30,99,0.65);
  --color-light-shadow: rgba(80,0,255,0.10);
  --color-light-border: #e91e63;
  --color-light-border-focus: #7c3aed;

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: #43bd90;
  --color-liquid-shimmer-end: #e91e63;
}

[data-theme="light"][data-app-category="enterprise"] {
  --app-surface: var(--surface-10);
  --app-light: var(--light-40);
  --app-motion: var(--motion-10);
  --app-gradient: var(--gradient-70);
}

[data-theme="light"][data-app-category="consumer"] {
  --app-surface: var(--surface-50);
  --app-light: var(--light-50);
  --app-motion: var(--motion-50);
  --app-gradient: var(--gradient-50);
}

[data-theme="light"][data-app-category="marketing"] {
  --app-surface: var(--surface-80);
  --app-light: var(--light-100);
  --app-motion: var(--motion-100);
  --app-gradient: var(--gradient-100);
} 