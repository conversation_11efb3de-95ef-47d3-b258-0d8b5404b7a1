import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CardComponent } from '../../components/card/card.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ava-image-card',
  standalone: true,
  imports: [CardComponent, CommonModule],
  templateUrl: './image-card.component.html',
  styleUrl: './image-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ImageCardComponent {
  @Input() imageUrl: string = '';
  @Input() name: string = '';
  @Input() title: string = 'Welcome to Console 🚀';
  @Input() imagePosition: 'left' | 'right' | 'top' | 'bottom' = 'left' //default;
  getWrapperClass() {
    if (this.imagePosition === 'top' || this.imagePosition === 'bottom') {
      return 'img-card-wrapper-vertical';
    }
    return 'img-card-wrapper-horizontal';
  }
}
