import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

export type SpinnerType = 'circular' | 'dotted' | 'partial' |'gradient' | 'dashed' | 'double';
export type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'ava-spinner',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.scss']
})
export class SpinnerComponent {
  @Input() type: SpinnerType = 'circular';
  @Input() size: SpinnerSize = 'md';
  @Input() className: string = '';
  @Input() animation:boolean = true;
  @Input() color: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'|'purple' = 'primary';
  @Input() progressIndex?: number;

  // Size mapping (reused)
  get sizeClass(): string {
    const sizeMap: Record<SpinnerSize, string> = {
      sm: 'size-sm',
      md: 'size-md',
      lg: 'size-lg',
      xl: 'size-xl'
    };
    return sizeMap[this.size];
  }

  // Check if the type is a simple spinner type
  get isStandardSpinner(): boolean {
    return ['circular', 'dotted', 'partial','dashed'].includes(this.type);
  }
  
  get progressClass(): string {
  if (this.progressIndex === undefined) return '';
  const rounded = Math.round(this.progressIndex);

  if (rounded <= 25) return 'rotate-25';
  if (rounded <= 50) return 'rotate-50';
  if (rounded <= 75) return 'rotate-75';
  return 'rotate-100';
}

}

