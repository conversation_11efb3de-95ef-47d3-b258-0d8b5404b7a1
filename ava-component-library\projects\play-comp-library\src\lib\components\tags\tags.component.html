<ng-container>
  <span
    class="ava-tag"
    [ngClass]="[
      'ava-tag',
      'ava-tag--' + color,
      'ava-tag--' + variant,
      'ava-tag--' + size,
      pill ? 'ava-tag--pill' : '',
      disabled ? 'ava-tag--disabled' : '',
      customClass || '',
      clickable ? 'ava-tag--clickable' : ''
    ]"
    [ngStyle]="customStyle"
    [attr.tabindex]="clickable ? 0 : null"
    [attr.role]="clickable ? 'button' : null"
    [attr.aria-disabled]="disabled ? 'true' : null"
    (click)="onClick()"
    (keyup.enter)="onClick()"
  >
    <ng-container *ngIf="avatar">
      <span class="ava-tag__avatar" *ngIf="avatar && avatar.startsWith('http')">
        <img [src]="avatar" alt="avatar" />
      </span>
      <span
        class="ava-tag__avatar ava-tag__avatar--initials"
        *ngIf="avatar && !avatar.startsWith('http')"
      >
        {{ avatar }}
      </span>
    </ng-container>
    <ng-container *ngIf="icon && iconPosition === 'start'">
      <ava-icon
        [iconName]="icon || ''"
        [iconSize]="16"
        [iconColor]="iconColor || 'currentColor'"
        [cursor]="false"
        [disabled]="disabled"
        class="ava-tag__icon ava-tag__icon--start"
      ></ava-icon>
    </ng-container>
    <span class="ava-tag__label">{{ label }}</span>
    <button
      *ngIf="removable"
      type="button"
      class="ava-tag__remove-btn"
      [disabled]="disabled"
      tabindex="0"
      aria-label="Remove tag"
      (click)="onRemove($event)"
      (keyup.enter)="onRemove($event)"
    >
      <ava-icon
        iconName="x"
        [iconSize]="16"
        [iconColor]="iconColor || 'currentColor'"
        [cursor]="true"
        [disabled]="disabled"
        class="ava-tag__remove"
      ></ava-icon>
    </button>
  </span>
</ng-container>
