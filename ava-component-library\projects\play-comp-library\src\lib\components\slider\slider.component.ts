import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostListener, Input, Output, ViewChild, WritableSignal, forwardRef, signal } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'ava-slider',
  imports: [CommonModule],
  templateUrl: './slider.component.html',
  styleUrl: './slider.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SliderComponent),
      multi: true
    }
  ]
})
export class SliderComponent implements ControlValueAccessor {
  @Input() min = 0;
  @Input() max = 100;
  @Input() value = 0;
  @Input() step = 1;
  @Input() showTooltip = true;

  @Output() valueChange = new EventEmitter<number>();
  @ViewChild('sliderTrack') sliderTrack!: ElementRef;

  isHovered = false;
  isDragging = false;

  private onChange: (value: number) => void = () => {};
  private onTouched: () => void = () => {};

  decimalStepValue: WritableSignal<number> = signal(1);

  constructor(private elementRef: ElementRef) {}

  get percentage(): number {
    return ((this.value - this.min) / (this.max - this.min)) * 100;
  }

  // ControlValueAccessor methods
  writeValue(value: number): void {
    this.value = value || 0;
    // Optional: update internal state/UI if needed
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onTrackClick(event: MouseEvent): void {
    this.updateValueFromEvent(event);
  }

  startDrag(event: MouseEvent): void {
    event.preventDefault();
    this.isDragging = true;
    this.isHovered = true;
  }

  onKeyDown(event: KeyboardEvent): void {
    let newValue = this.value;
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowUp':
        newValue = this.value + this.step;
        break;
      case 'ArrowLeft':
      case 'ArrowDown':
        newValue = this.value - this.step;
        break;
      case 'Home':
        newValue = this.min;
        break;
      case 'End':
        newValue = this.max;
        break;
      default:
        return;
    }
    event.preventDefault();
    this.updateValue(newValue);
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (this.isDragging) {
      this.updateValueFromEvent(event);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isDragging) {
      this.isDragging = false;
      this.isHovered = false;
      this.onTouched();
    }
  }

  onDecimalStepChange(value: number): void {
  this.decimalStepValue.set(parseFloat(value.toFixed(2)));
}

  private updateValueFromEvent(event: MouseEvent): void {
    const rect = this.sliderTrack.nativeElement.getBoundingClientRect();
    const percentage = (event.clientX - rect.left) / rect.width;
    const newValue = this.min + percentage * (this.max - this.min);
    this.updateValue(newValue);
  }

  private updateValue(value: number): void {
    const roundedValue = Math.round(value / this.step) * this.step;
    const clampedValue = Math.max(this.min, Math.min(this.max, roundedValue));
    if (clampedValue !== this.value) {
      this.value = clampedValue;
      this.onChange(this.value);
      this.valueChange.emit(this.value);
    }
  }
}
