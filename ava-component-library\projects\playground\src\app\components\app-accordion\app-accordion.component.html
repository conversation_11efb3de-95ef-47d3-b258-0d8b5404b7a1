<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<!-- eslint-disable @angular-eslint/template/interactive-supports-focus -->
<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Accordion Component</h1>
        <p class="description">
          A versatile accordion component that supports multiple types and
          animations. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} AccordionComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div
                class="code-toggle"
                (click)="toggleCodeVisibility(i, $event)"
              >
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!--<awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-accordion
                    type="default"
                    [iconClosed]="'chevron-right'"
                    [iconOpen]="'chevron-down'"
                    [iconPosition]="'left'">
                    <div header>{{ title }}</div>
                    <div content>{{ description }}</div>
                  </ava-accordion>
                </div>
              </div>
            </ng-container>

            <!-- Accordion Types -->
            <ng-container *ngSwitchCase="'Accordion Types'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-accordion
                    type="default"
                    [iconClosed]="'chevron-right'"
                    [iconOpen]="'chevron-down'"
                    [iconPosition]="'left'">
                    <div header>{{ title }}</div>
                    <div content>{{ description }}</div>
                  </ava-accordion>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-accordion
                    type="titleIcon"
                    [titleIcon]="'home'"
                    [iconOpen]="'minus'"
                    [iconClosed]="'plus'"
                    [iconPosition]="'left'">
                    <div header>{{ title }}</div>
                    <div content>{{ description }}</div>
                  </ava-accordion>
                  
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-accordion
                    type="default"
                    [iconClosed]="'plus'"
                    [iconOpen]="'minus'"
                    [iconPosition]="'left'">
                    <div header>{{ title }}</div>
                    <div content>{{ description }}</div>
                  </ava-accordion>
                </div>
              </div>
            </ng-container>

            <!-- Animations -->
            <ng-container *ngSwitchCase="'Animations'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ng-container
                    *ngFor="let item of accordionItems; let i = index">
                    <ava-accordion
                      type="default"
                      [animation]="true"
                      [expanded]="activeIndex === i"
                      (click)="setActiveAccordion(i)"
                      [iconOpen]="'chevron-down'"
                    [iconClosed]="'chevron-up'"
                      [iconPosition]="'right'">
                      <div header>{{ item.title }}</div>
                      <div content>{{ item.description }}</div>
                    </ava-accordion>
                  </ng-container>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button
            class="copy-button"
            (click)="copyCode(section.title.toLowerCase())"
          >
            <!--<awe-icons iconName="awe_copy"></awe-icons> -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><code>type</code></td>
          <td><code>'simple' | 'add' | 'dark' | 'light'</code></td>
          <td><code>'simple'</code></td>
          <td>Sets the type of the accordion.</td>
        </tr>
        <tr>
          <td><code>animation</code></td>
          <td><code>boolean</code></td>
          <td><code>false</code></td>
          <td>Enables or disables animation.</td>
        </tr>
        <tr>
          <td><code>expanded</code></td>
          <td><code>boolean</code></td>
          <td><code>false</code></td>
          <td>Sets the initial expanded state of the accordion.</td>
        </tr>
        <tr>
          <td><code>hasFooter</code></td>
          <td><code>boolean</code></td>
          <td><code>false</code></td>
          <td>Indicates if the accordion has a footer.</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><code>toggle</code></td>
          <td><code>EventEmitter&lt;boolean&gt;</code></td>
          <td>Emitted when the accordion is toggled.</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
