<div class="documentation">
  <!-- Header -->
  <header class="doc-header">
    <h1>Input Component</h1>
    <p class="description">
      The Input component provides a versatile and customizable text input field with support for various states, icons, validation, and loading states. It offers different variants and themes to match your application's design requirements.
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} InputComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <awe-input 
                label="Basic Input"
                placeholder="Enter your text"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action">
              </awe-input>
            </ng-container>

            <!-- States and Colors -->
            <ng-container *ngSwitchCase="'States and Colors'">
              <awe-input 
                label="Border Color Blue" 
                placeholder="Enter your text" 
                [borderColor]="false"
                status="blue"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action">
              </awe-input>

              <awe-input 
                label="Border Color Green" 
                placeholder="Enter your text" 
                [borderColor]="false"
                status="green"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action">
              </awe-input>

              <awe-input 
                label="Border Color Red" 
                placeholder="Enter your text" 
                [borderColor]="false"
                status="red"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action">
              </awe-input>

              <awe-input 
                label="Border Color White" 
                placeholder="Enter your text" 
                [borderColor]="false"
                status="white"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action"
              ></awe-input>
            </ng-container>

            <!-- Validation -->
            <ng-container *ngSwitchCase="'Validation'">
              <awe-input
                label="Required Field"
                placeholder="This field is required"
                [required]="true"
                errorMessage="This field is required"
                [icons]="['awe_send']"
                iconColor="action">
              </awe-input>

              <awe-input 
                label="Email Validation" 
                placeholder="Enter your Gmail address"
                [required]="true" 
                pattern="[a-z0-9._%+-]+@gmail.com$"
                errorMessage="Please enter a valid Gmail address"
                [icons]="['awe_send']"
                iconColor="action">
              </awe-input>
            </ng-container>

            <!-- Variants -->
            <ng-container *ngSwitchCase="'Variants'">
              <awe-input
                label="Fixed Width"
                placeholder="Fixed width input"
                variant="fixed"
                [icons]="['awe_send']"
                iconColor="action">
              </awe-input>

              <awe-input
                label="Fluid Width"
                placeholder="Fluid width input"
                variant="fluid"
                [icons]="['awe_send']"
                iconColor="action">
              </awe-input>

              <awe-input
                label="Compact Width"
                placeholder="Compact width input"
                variant="compact"
                [icons]="['awe_send']"
                iconColor="action">
              </awe-input>
            </ng-container>

            <!-- Loading States -->
            <ng-container *ngSwitchCase="'Loading States'">
              <awe-input
                label="Spinner Loading"
                placeholder="Loading with spinner..."
                [loading]="isLoading"
                loadingType="spinner"
                [icons]="['awe_search']">
              </awe-input>

              <awe-input
                label="Skeleton Loading"
                placeholder="Loading with skeleton..."
                [loading]="isLoading"
                loadingType="skeleton"
                skeletonWidth="240px"
                [icons]="['awe_search']">
              </awe-input>

              <awe-button
                (click)="simulateLoading()"
                [disabled]="isLoading">
                {{ isLoading ? 'Loading...' : 'Simulate Loading' }}
              </awe-button>
              
              <span class="loading-hint" *ngIf="isLoading">
                Loading will complete in 2 seconds
              </span>
            </ng-container>

            <!-- input with Single Icons -->
            <ng-container *ngSwitchCase="'Input with Single Icons'">
              <awe-input
                label="Search"
                placeholder="Search items..."
                [icons]="['awe_search']"
                [clearable]="true"
                (iconClickEvent)="onIconClick($event)"
                (clear)="onClear()"
              ></awe-input>

              <awe-input
                label="Password"
                placeholder="Enter password"
                type="password"
                [icons]="['awe_visibility']"
                (iconClickEvent)="onIconClick($event)"
              ></awe-input>
            </ng-container>

            <!-- Expanded Input -->
             <ng-container *ngSwitchCase="'Expanded Input'">
              <awe-input
                label="Expanded Input"
                placeholder="This is an expanded input field"
                [expand]="true"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action">
              </awe-input>

              <awe-input
                label="Expanded Input without state color"
                placeholder="This is an expanded input field"
                [expand]="true"
                [icons]="['awe_send', 'awe_mic', 'awe_close']"
                iconColor="action"
                [borderColor]="false"
                status="green">
              </awe-input>
             </ng-container>
            
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title)"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title)">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td><code>{{ event.name }}</code></td>
          <td><code>{{ event.type }}</code></td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>

