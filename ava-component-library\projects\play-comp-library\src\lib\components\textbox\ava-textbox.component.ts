import {
  ChangeDetectionStrategy,
  Component,
  Input,
  forwardRef,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
  ViewChild,
  ElementRef,
  AfterViewInit
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
import { trigger, transition, style, animate } from '@angular/animations';

export type TextboxVariant = 'default' | 'primary' | 'success' | 'error' | 'warning' | 'info';
export type TextboxSize = 'sm' | 'md' | 'lg';
export type IconPosition = 'start' | 'end';

// Play+ Metaphor System Types
export type PersonalityTheme = 'minimal' | 'professional' | 'modern' | 'vibrant';
export type MetaphorIntensity = 0 | 10 | 25 | 50 | 75 | 100;

@Component({
  selector: 'ava-textbox',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './ava-textbox.component.html',
  styleUrl: './ava-textbox.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AvaTextboxComponent),
      multi: true,
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  animations: [
    trigger('fadeIcon', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 1, transform: 'scale(1)' }))
      ]),
      transition(':leave', [
        animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 0, transform: 'scale(0.8)' }))
      ])
    ])
  ]
})
export class AvaTextboxComponent implements ControlValueAccessor, AfterViewInit {
  @Input() label = '';
  @Input() placeholder = '';
  @Input() variant: TextboxVariant = 'default';
  @Input() size: TextboxSize = 'md';
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() error = '';
  @Input() helper = '';
  @Input() icon = '';
  @Input() iconPosition: IconPosition = 'start';
  @Input() iconColor = 'var(--textbox-icon-color)';
  @Input() id = '';
  @Input() name = '';
  @Input() autocomplete = '';
  @Input() type = 'text';
  @Input() maxlength?: number;
  @Input() minlength?: number;
  @Input() required = false;
  @Input() fullWidth = false;
  @Input() style?: Record<string, string>;
  
  // Play+ Metaphor System Props
  @Input() personality?: PersonalityTheme; // Override global personality
  @Input() glassIntensity?: MetaphorIntensity; // Override personality glass
  @Input() lightIntensity?: MetaphorIntensity; // Override personality light
  @Input() liquidIntensity?: MetaphorIntensity; // Override personality liquid
  @Input() gradientIntensity?: MetaphorIntensity; // Override personality gradient
  @Input() enableMetaphors = true; // Master toggle for metaphor system
  @Input() respectsGlobalPersonality = true; // Whether to inherit from global personality
  
  // Legacy metaphor support
  @Input() metaphor: string | string[] = '';

  @Output() textboxBlur = new EventEmitter<Event>();
  @Output() textboxFocus = new EventEmitter<Event>();
  @Output() textboxInput = new EventEmitter<Event>();
  @Output() textboxChange = new EventEmitter<Event>();
  @Output() iconStartClick = new EventEmitter<Event>();
  @Output() iconEndClick = new EventEmitter<Event>();

  @ViewChild('prefixContainer', { static: true }) prefixContainer!: ElementRef;
  @ViewChild('suffixContainer', { static: true }) suffixContainer!: ElementRef;
  @ViewChild('iconStartContainer', { static: true }) iconStartContainer!: ElementRef;
  @ViewChild('iconEndContainer', { static: true }) iconEndContainer!: ElementRef;

  value = '';
  isFocused = false;
  hasProjectedPrefix = false;
  hasProjectedSuffix = false;
  hasProjectedStartIcon = false;
  hasProjectedEndIcon = false;
  
  private onChange: (value: string) => void = () => { /* noop */ };
  private onTouched: () => void = () => { /* noop */ };

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterViewInit(): void {
    // Check for projected content in containers
    this.checkProjectedContent();
  }

  private checkProjectedContent(): void {
    // Use setTimeout to ensure content projection has completed
    setTimeout(() => {
      this.hasProjectedPrefix = this.prefixContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedSuffix = this.suffixContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedStartIcon = this.iconStartContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedEndIcon = this.iconEndContainer?.nativeElement?.children?.length > 0;
      this.cdr.markForCheck();
    });
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.value = value || '';
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }

  // Event handlers
  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);
    this.textboxInput.emit(event);
  }

  onFocus(event: Event): void {
    this.isFocused = true;
    this.textboxFocus.emit(event);
  }

  onBlur(event: Event): void {
    this.isFocused = false;
    this.onTouched();
    this.textboxBlur.emit(event);
  }

  onChange_(event: Event): void {
    this.textboxChange.emit(event);
  }

  // Icon click handlers
  onIconStartClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.stopPropagation();
    this.iconStartClick.emit(event);
  }

  onIconEndClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.stopPropagation();
    this.iconEndClick.emit(event);
  }

  // Keyboard accessibility for icons
  onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (position === 'start') this.onIconStartClick(event);
      else this.onIconEndClick(event);
    }
  }

  // Computed properties
  get hasError(): boolean {
    return !!this.error;
  }

  get hasHelper(): boolean {
    return !!this.helper && !this.hasError;
  }

  get hasIcon(): boolean {
    return !!this.icon;
  }



  get inputId(): string {
    return this.id || `ava-textbox-${Math.random().toString(36).substr(2, 9)}`;
  }

  get errorId(): string {
    return `${this.inputId}-error`;
  }

  get helperId(): string {
    return `${this.inputId}-helper`;
  }

  get ariaDescribedBy(): string {
    const ids: string[] = [];
    if (this.hasError) ids.push(this.errorId);
    if (this.hasHelper) ids.push(this.helperId);
    return ids.join(' ') || '';
  }

  /**
   * Generate metaphor intensity classes based on props and personality
   */
  get metaphorClasses(): string[] {
    if (!this.enableMetaphors) return [];

    const classes: string[] = [];

    // Add personality class if provided
    if (this.personality) {
      classes.push(`ava-textbox--personality-${this.personality}`);
    }

    // Add specific intensity overrides
    if (this.glassIntensity !== undefined) {
      classes.push(`ava-textbox--glass-${this.glassIntensity}`);
    }
    if (this.lightIntensity !== undefined) {
      classes.push(`ava-textbox--light-${this.lightIntensity}`);
    }
    if (this.liquidIntensity !== undefined) {
      classes.push(`ava-textbox--liquid-${this.liquidIntensity}`);
    }
    if (this.gradientIntensity !== undefined) {
      classes.push(`ava-textbox--gradient-${this.gradientIntensity}`);
    }

    return classes;
  }

  /**
   * Generate legacy metaphor classes for backward compatibility
   */
  get legacyMetaphorClasses(): string[] {
    if (!this.metaphor) return [];
    
    if (Array.isArray(this.metaphor)) {
      return this.metaphor.map(m => `ava-textbox--${m}`);
    } else {
      return [`ava-textbox--${this.metaphor}`];
    }
  }

  get inputClasses(): string {
    const classes = ['ava-textbox__input'];
    
    if (this.size) classes.push(`ava-textbox__input--${this.size}`);
    if (this.variant) classes.push(`ava-textbox__input--${this.variant}`);
    if (this.hasError) classes.push('ava-textbox__input--error');
    if (this.disabled) classes.push('ava-textbox__input--disabled');
    if (this.readonly) classes.push('ava-textbox__input--readonly');
    if (this.isFocused) classes.push('ava-textbox__input--focused');
    if (this.fullWidth) classes.push('ava-textbox__input--full-width');
    
    // Add classes based on projected content
    if (this.hasProjectedStartIcon) classes.push('ava-textbox__input--icon-start');
    if (this.hasProjectedEndIcon) classes.push('ava-textbox__input--icon-end');
    if (this.hasProjectedPrefix) classes.push('ava-textbox__input--with-prefix');
    if (this.hasProjectedSuffix) classes.push('ava-textbox__input--with-suffix');
    
    return classes.join(' ');
  }

  get wrapperClasses(): string {
    const classes = ['ava-textbox'];
    
    if (this.size) classes.push(`ava-textbox--${this.size}`);
    if (this.variant) classes.push(`ava-textbox--${this.variant}`);
    if (this.hasError) classes.push('ava-textbox--error');
    if (this.disabled) classes.push('ava-textbox--disabled');
    if (this.readonly) classes.push('ava-textbox--readonly');
    if (this.isFocused) classes.push('ava-textbox--focused');
    if (this.fullWidth) classes.push('ava-textbox--full-width');
    
    // Add Play+ metaphor classes
    const metaphorClasses = this.metaphorClasses;
    const legacyClasses = this.legacyMetaphorClasses;
    
    return [...classes, ...metaphorClasses, ...legacyClasses].join(' ');
  }
}
