<button class="ava-button" [class]="
    [
      basicOrAdvanced,
      variant,
      size,
      state,
      pill === true ? 'pill' : '',
      state === 'active' ? 'active' : '',
      isActive ? 'active-anim' : ''
    ].join(' ')
  " [style.width]="width ? width : null" [style.height.px]="height ? height : null"
  [style.background]="gradient || background || null" [style.color]="color ? color : null" [disabled]="disabled"
  [ngClass]="[
    visual,
    iconPosition === 'only' ? 'icon-only-ava-button' : '',
    iconPosition === 'left' || iconPosition === 'right'
      ? 'icon-lr-ava-button'
      : '',
    gradient ? 'gradient' : ''
  ]" (click)="handleClick($event)" (keydown)="onKeydown($event)">
  <ng-container *ngIf="(iconName && iconPosition === 'left') || iconPosition === 'only'">
    <ava-icon [iconName]="iconName" [iconColor]="computedIconColor" [iconSize]="iconSize"
      [disabled]="disabled"></ava-icon>
  </ng-container>
  <span *ngIf="iconPosition !== 'only'" class="ava-button__label">{{
    label
    }}</span>
  <ng-container *ngIf="iconName && iconPosition === 'right'">
    <ava-icon [iconName]="iconName" [iconColor]="computedIconColor" [iconSize]="iconSize"
      [disabled]="disabled"></ava-icon>
  </ng-container>

  <ava-icon class="b-dropdown" *ngIf="dropdown" iconName="chevron-down" [iconColor]="computedIconColor"
    [iconSize]="30"></ava-icon>
</button>