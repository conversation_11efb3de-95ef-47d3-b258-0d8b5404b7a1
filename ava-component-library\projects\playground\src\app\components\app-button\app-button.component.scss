
/* Header */
.doc-header { 
  h1 {
     text-align: left;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}
 
/* Sections */
.doc-sections {
  margin-top: 4rem;
}
 
.doc-section {
  margin-bottom: 1rem;
 
  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }
 
  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}
 
/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);
 
  h2 {
    margin-bottom: 0.5rem;
  }
 
  .description-container {
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
 
  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);
 
    &:hover {
      text-decoration: underline;
    }
 
    span {
      margin-right: 0.5rem;
    }
 
    ava-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;
 
      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}
 
/* Code example styles */
.code-example {
  margin-top: 1.5rem;
 
  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }
 
  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);
 
    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }
 
    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);
 
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
 
/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
 
  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }
 
  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }
 
  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}
.glass{
    padding-top: 50px;
    background: url("/assets/glass-image.jpg") no-repeat center center fixed;
    background-size: cover;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(18px);
    -webkit-backdrop-filter: blur(18px);
    box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.15),
    inset 0 0 0.5px rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 2rem;
    height: 350px;
     margin: 0 auto;
     text-align: center;
     .glass-panel-container{
      margin: 0 auto;
      width:500px;
          .glass-panel{
              color: #fff;
              margin: 40px auto;
              background-color: rgba(255, 255, 255, 0.06);
              border: 1px solid rgba(255, 255, 255, 0.1);
              width: 100%;
              border-radius: 15px;
              padding: 32px;
              -webkit-backdrop-filter: blur(10px);
              backdrop-filter: blur(10px);
            
              p{
                color:#fff !important;
                font-size:20px !important
              }
          }
     }



    

}

/////////////




button.neumorphic {
  container-type: inline-size;
  aspect-ratio: 1/1;
  border: 0.5rem solid transparent;
  border-radius: 1rem;
  color: hsl(132, 62%, 39%);
  background: none;  
  display: grid;
  place-content: center;
  gap: 1rem;  
  --shadow: 
    -.5rem -.5rem 1rem hsl(0 0% 100% / .75),
    .5rem .5rem 1rem hsl(0 0% 50% / .5);
  box-shadow: var(--shadow);
  outline: none;  
  transition: all 0.1s;
  
  &:hover, &:focus-visible {
    color: hsl(10 80% 50%);
    scale: 1.1
  }
  &:active, &.active{
    box-shadow:
      var(--shadow),
      inset .5rem .5rem 1rem hsl(0 0% 50% / .5),
      inset -.5rem -.5rem 1rem hsl(0 0% 100% / .75);
    color: hsl(10 80% 50%);
    > span { font-size: 13cqi};
  }

  >i {
    font-size: 31cqi;
  }
  > span {
    font-family: system-ui, sans-serif;
    font-size: 16cqi;
  }
}

body {
}
h1 {
  text-align: center;
  color: hsl(0 0% 10%);
  font-family: system-ui, sans-serif;
  font-size: 3rem;
}
.buttons {
  display: grid;
  width: min(75rem, 100%);
  margin-inline: auto;
  grid-template-columns: repeat(auto-fit, minmax(min(8rem, 100%), 1fr));
  gap: 2rem;
}
.neomorphic-container{
   background-color: #e5e9f4;
  padding: 2rem;
}
////////////
/// 
/// 
.bg-b{
  background-color: #0d32a1;
  padding:50px
}
.n-container{
  padding:100px;
  background-color: #0d32a1;
}
