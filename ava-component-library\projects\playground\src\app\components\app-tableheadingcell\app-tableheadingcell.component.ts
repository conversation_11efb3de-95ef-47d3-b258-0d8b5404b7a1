import { Component } from '@angular/core';
import { TableHeaderComponent } from "../../../../../play-comp-library/src/lib/components/table-header/table-header.component";

@Component({
  selector: 'app-app-tableheadingcell',
  imports: [],
  templateUrl: './app-tableheadingcell.component.html',
  styleUrl: './app-tableheadingcell.component.scss'
})
export class AppTableheadingcellComponent {

  // columns = [
  //   { header: 'Project Name', field: 'projectName', isSortable: true, ariaSort: null },
  //   { header: 'Req ID', field: 'reqId', isSortable: false, ariaSort: null },
  //   { header: 'Document Type', field: 'documentType', isSortable: true, ariaSort: null },
  //   { header: 'Created On', field: 'createdOn', isSortable: true, ariaSort: null },
  //   { header: 'Source', field: 'source', isSortable: true, ariaSort: null },
  //   { header: 'Action', field: 'action', isSortable: true, ariaSort: null },
  //   { header: 'Artifacts ID', field: 'artifactsId', isSortable: true, ariaSort: null },
  //   { header: 'Status', field: 'status', isSortable: true, ariaSort: null },
  //   { header: 'App Domain', field: 'appDomain', isSortable: true, ariaSort: null },
  //   { header: 'Project ID', field: 'projectId', isSortable: true, ariaSort: null },
  //   { header: 'App Name', field: 'appName', isSortable: true, ariaSort: null },
  //   { header: 'Request Type', field: 'requestType', isSortable: true, ariaSort: null },
  // ];

}
