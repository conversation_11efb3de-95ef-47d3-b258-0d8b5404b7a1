import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BadgesComponent, BadgeState, BadgeSize } from '../badges/badges.component';

export type AvatarSize = 'small' | 'medium' | 'large';
export type AvatarShape = 'pill' | 'square';
export type AvatarTextType = 'status' | 'profile';

@Component({
selector: 'ava-avatars',
imports: [CommonModule, BadgesComponent],
templateUrl: './avatars.component.html',
styleUrl: './avatars.component.scss',
changeDetection: ChangeDetectionStrategy.OnPush
})
export class AvatarsComponent {
@Input() size: AvatarSize = 'large';
@Input() shape: AvatarShape = 'pill';
@Input() imageUrl: string = '';
@Input() statusText?: string;
@Input() profileText?: string;
@Input() badgeState?: BadgeState;
@Input() badgeSize?: BadgeSize;
@Input() badgeCount?: number;

get avatarClasses(): string {
return `avatar avatar--${this.size} avatar--${this.shape}`;
}

get hasBadge(): boolean {
return !!(this.badgeState || this.badgeCount);
}

get hasStatusText(): boolean {
return !!this.statusText;
}

get hasProfileText(): boolean {
return !!this.profileText;
}

get hasAnyText(): boolean {
return this.hasStatusText || this.hasProfileText;
}
}