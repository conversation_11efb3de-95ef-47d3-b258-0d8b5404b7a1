    .slider-container {
      width: 100%;
    }

    .slider {
      position: relative;
      width: 100%;
      height: var(--slider-track-height);
      background: var(--slider-track-background);
      border-radius: var(--slider-track-border-radius);
      cursor: var(--slider-cursor);
      margin-bottom: var(--slider-tooltip-margin);
    }

    .slider-fill {
      height: 100%;
      background: var(--slider-progress-background);
      border-radius: var(--slider-progress-border-radius);
      transition: var(--slider-transition);
    }

    .slider-handle {
      position: absolute;
      top: calc((var(--slider-track-height) - var(--slider-thumb-size)) / 2);
      width: var(--slider-thumb-size);
      height: var(--slider-thumb-size);
      transform: translateX(-50%);
      cursor: grab;
      outline: none;
    }

    .slider-handle:active,
    .slider-handle.dragging {
      cursor: grabbing;
    }

    .handle-ring {
      position: absolute;
      top: calc((var(--slider-thumb-size) - var(--slider-thumb-size) - 12px) / 2);
      left: calc((var(--slider-thumb-size) - var(--slider-thumb-size) - 12px) / 2);
      width: calc(var(--slider-thumb-size) + 12px);
      height: calc(var(--slider-thumb-size) + 12px);
      border-radius: var(--slider-thumb-border-radius);
      background: color-mix(in srgb, var(--slider-progress-background) 10%, transparent);
      border: 2px solid color-mix(in srgb, var(--slider-progress-background) 20%, transparent);
      opacity: 0;
      transition: var(--slider-transition);
    }

    .slider-handle.hover .handle-ring,
    .slider-handle.dragging .handle-ring,
    .slider-handle:focus .handle-ring {
      opacity: 1;
    }

    .handle-core {
      position: absolute;
      top: 0;
      left: 0;
      width: var(--slider-thumb-size);
      height: var(--slider-thumb-size);
      background: var(--slider-thumb-inner-background);
      border: var(--slider-focus-ring);
      border-radius: var(--slider-thumb-border-radius);
      box-shadow: var(--slider-thumb-shadow);
      transition: var(--slider-transition);
    }

    .slider-handle.hover .handle-core,
    .slider-handle.dragging .handle-core,
    .slider-handle:focus .handle-core {
      box-shadow: var(--slider-thumb-shadow-hover);
    }

    .slider-handle:focus {
      outline: var(--slider-focus-ring);
      outline-offset: var(--slider-focus-ring-offset);
    }

    // Movable Tooltip
    .slider-tooltip {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: --slider-tooltip-margin;
      padding: --slider-tooltip-padding;
      background: var(--slider-thumb-inner-background);
      color: var(--slider-value-color);
      font: var(--slider-size-md-label);
      border-radius: --slider-tooltip-border-radius;
      box-shadow: var(--slider-thumb-shadow);
      white-space: nowrap;
      pointer-events: none;
      z-index: var(--tooltip-z-index);

      // Tooltip arrow pointing up
      &::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: --slider-tooltip-arrow-size solid transparent;
        border-bottom-color: var(--slider-thumb-inner-background);
      }

      // Arrow border for shadow effect
      &::after {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: calc(--slider-tooltip-arrow-size ) solid transparent;
        border-bottom-color: var(--slider-mark-background);
        z-index: -1;
      }
    }