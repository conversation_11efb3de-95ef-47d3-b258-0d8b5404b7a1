import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { FileUploadComponent } from "../../../../../play-comp-library/src/lib/components/fileupload/fileupload.component";
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@Component({
  selector: 'app-fileupload-documentation',
  imports: [FileUploadComponent, CommonModule, IconsComponent],
  templateUrl: './app-fileupload.component.html',
  styleUrl: './app-fileupload.component.scss',
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppFileuploadComponent {
  @ViewChild('codeBlock') codeBlock!: ElementRef;

  sections = [
    {
      title: 'Basic Usage',
      description: 'Demonstrates the basic functionality of the file upload component with light and dark themes.',
      showCode: false,
    },
    {
      title: 'Animated Upload',
      description: 'Displays file upload components with animation enabled for a more interactive experience.',
      showCode: false,
    },
    {
      title: 'Single File Upload',
      description: 'Uploaders that only allow one file at a time. When a new file is uploaded, it replaces the existing one.',
      showCode: false,
    },
    {
      title: 'Limited File Upload',
      description: 'Uploaders that limit the maximum number of files to 5. Shows an error message when trying to upload more files.',
      showCode: false,
    }
  ];

  apiProps = [
    { name: 'theme', type: '"light" | "dark"', default: '"light"', description: 'The visual theme of the file upload component.' },
    { name: 'uploaderId', type: 'string', default: '""', description: 'A unique identifier for the uploader instance. Required for managing multiple uploaders.' },
    { name: 'enableAnimation', type: 'boolean', default: 'false', description: 'Enables animation effects during the upload process.' },
    { name: 'singleFileMode', type: 'boolean', default: 'false', description: 'When enabled, only one file can be uploaded. New files replace the existing one.' },
    { name: 'maxFiles', type: 'number | null', default: 'null', description: 'Maximum number of files that can be uploaded. Null means no limit.' },
    { name: 'accept', type: 'string', default: '""', description: 'Standard HTML accept attribute value for defining accepted file types (e.g., ".pdf,.jpg,.png").' },
    { name: 'multiple', type: 'boolean', default: 'false', description: 'Allows selection of multiple files at once.' }
  ];

  eventProps = [
    { name: 'fileUploaded', type: 'EventEmitter<File>', description: 'Emitted when a file is successfully uploaded.' },
    { name: 'filesListChanged', type: 'EventEmitter<File[]>', description: 'Emitted whenever the list of uploaded files changes (adding, removing, or replacing files).' },
    { name: 'fileError', type: 'EventEmitter<{file: File, error: string}>', description: 'Emitted when an error occurs during file upload.' }
  ];

  // Store uploaded files separately for each uploader
  uploadedFiles: Record<string, File[]> = {};

  toggleSection(index: number): void {
    this.sections.forEach((section, i) => {
      section.showCode = (i === index) ? !section.showCode : false;
    });
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (this.codeBlock && !this.codeBlock.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  onFileUpload(file: File, uploaderId: string) {
    // Initialize the array if it doesn't exist
    if (!this.uploadedFiles[uploaderId]) {
      this.uploadedFiles[uploaderId] = [];
    }
    
    // For single file mode uploaders, replace existing file
    if (uploaderId.includes('single')) {
      this.uploadedFiles[uploaderId] = [file];
    } else {
      // For regular uploaders, add the file if not duplicate
      const isDuplicate = this.uploadedFiles[uploaderId].some(existingFile => 
        existingFile.name === file.name && existingFile.size === file.size);
      
      if (!isDuplicate) {
        this.uploadedFiles[uploaderId].push(file);
      }
    }
    
    console.log(`File uploaded to ${uploaderId}:`, file.name);
    this.uploadFileToServer(file, uploaderId);
  }

  onFilesChanged(files: File[], uploaderId: string) {
    this.uploadedFiles[uploaderId] = [...files];
    console.log(`Files list changed for ${uploaderId}:`, files.map(f => f.name));
  }

  uploadFileToServer(file: File, uploaderId: string) {
    const formData = new FormData();
    formData.append('file', file);
    console.log(`Simulated file upload for ${uploaderId}:`, file.name);
    // Your actual upload logic here
  }

  getFileUploadCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
import { Component } from '@angular/core';
import { FileUploadComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-fileupload',
  standalone: true,
  imports: [FileUploadComponent],
  template: \`
    <div class="row">
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'light-uploader')" 
          [theme]="'light'" 
          uploaderId="light-uploader">
        </awe-file-upload>
      </div>
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'dark-uploader')" 
          [theme]="'dark'" 
          uploaderId="dark-uploader">
        </awe-file-upload>
      </div>
    </div>
  \`
})
export class BasicFileUploadComponent {
  onFileUpload(file: File, uploaderId: string) {
    console.log(\`File uploaded to \${uploaderId}:\`, file.name);
    // Handle the file upload
  }
}`,
      'animated upload': `
import { Component } from '@angular/core';
import { FileUploadComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-animated-fileupload',
  standalone: true,
  imports: [FileUploadComponent],
  template: \`
    <div class="row">
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'light-animated-uploader')" 
          [theme]="'light'" 
          [enableAnimation]="true" 
          uploaderId="light-animated-uploader">
        </awe-file-upload>
      </div>
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'dark-animated-uploader')" 
          [theme]="'dark'" 
          [enableAnimation]="true" 
          uploaderId="dark-animated-uploader">
        </awe-file-upload>
      </div>
    </div>
  \`
})
export class AnimatedFileUploadComponent {
  onFileUpload(file: File, uploaderId: string) {
    console.log(\`File uploaded to \${uploaderId}:\`, file.name);
    // Handle the file upload
  }
}`,
      'single file upload': `
import { Component } from '@angular/core';
import { FileUploadComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-single-file-upload',
  standalone: true,
  imports: [FileUploadComponent],
  template: \`
    <div class="row">
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'light-single-uploader')"
          (filesListChanged)="onFilesChanged($event, 'light-single-uploader')"
          [theme]="'light'" 
          [singleFileMode]="true"
          uploaderId="light-single-uploader">
        </awe-file-upload>
      </div>
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'dark-single-uploader')"
          (filesListChanged)="onFilesChanged($event, 'dark-single-uploader')"
          [theme]="'dark'" 
          [singleFileMode]="true"
          uploaderId="dark-single-uploader">
        </awe-file-upload>
      </div>
    </div>
  \`
})
export class SingleFileUploadComponent {
  onFileUpload(file: File, uploaderId: string) {
    console.log(\`File uploaded to \${uploaderId}:\`, file.name);
    // Handle the file upload
  }
  
  onFilesChanged(files: File[], uploaderId: string) {
    console.log(\`Files list changed for \${uploaderId}:\`, files.map(f => f.name));
    // Update your file tracking logic
  }
}`,
      'limited file upload': `
import { Component } from '@angular/core';
import { FileUploadComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-limited-file-upload',
  standalone: true,
  imports: [FileUploadComponent],
  template: \`
    <div class="row">
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'light-limited-uploader')"
          (filesListChanged)="onFilesChanged($event, 'light-limited-uploader')"
          [theme]="'light'" 
          [maxFiles]="5"
          uploaderId="light-limited-uploader">
        </awe-file-upload>
      </div>
      <div class="col-12 col-md-6">
        <awe-file-upload 
          (fileUploaded)="onFileUpload($event, 'dark-limited-uploader')"
          (filesListChanged)="onFilesChanged($event, 'dark-limited-uploader')"
          [theme]="'dark'" 
          [maxFiles]="5"
          uploaderId="dark-limited-uploader">
        </awe-file-upload>
      </div>
    </div>
  \`
})
export class LimitedFileUploadComponent {
  onFileUpload(file: File, uploaderId: string) {
    console.log(\`File uploaded to \${uploaderId}:\`, file.name);
    // Handle the file upload
  }
  
  onFilesChanged(files: File[], uploaderId: string) {
    console.log(\`Files list changed for \${uploaderId}:\`, files.map(f => f.name));
    // Update your file tracking logic
  }
}`,
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }
  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getFileUploadCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
 
}
