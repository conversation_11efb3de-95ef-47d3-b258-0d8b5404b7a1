// snackbar.service.ts
import { Injectable, signal } from '@angular/core';

export type SnackbarPosition =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top-center'
  | 'bottom-center'
  | 'center';

export interface SnackbarData {
  message: string;
  duration: number;
  position: SnackbarPosition;
  color: string;
  backgroundColor: string;
}

@Injectable({ providedIn: 'root' })
export class SnackbarService {
  private snackbarSignal = signal<SnackbarData | null>(null);
  readonly snackbar$ = this.snackbarSignal.asReadonly();

  show(
    message: string,
    position: SnackbarPosition = 'bottom-center',
    duration = 3000,
    color = '#fff',
    backgroundColor = '#6B7280'
  ) {
    this.snackbarSignal.set({
      message,
      duration,
      position,
      color,
      backgroundColor
    });

    // Clear after duration
    setTimeout(() => {
      this.snackbarSignal.set(null);
    }, duration);
  }
}
