import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../components/card/card.component';
import { IconComponent } from '../../components/icon/icon.component';

@Component({
  selector: 'ava-text-card',
  standalone: true,
  imports: [CardComponent, CommonModule, IconComponent],
  templateUrl: './text-card.component.html',
  styleUrls: ['./text-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextCardComponent {
  @Input() iconName = 'trending-up';
  @Input() title: string = '';
  @Input() value: string | number = '';
  @Input() description: string = '';
}
