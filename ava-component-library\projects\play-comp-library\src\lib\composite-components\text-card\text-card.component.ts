import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../components/icon/icon.component';
import { FeatureCardComponent } from '../../components/feature-card/feature-card.component';
import { CardComponent } from "../../components/card/card.component";

@Component({
  selector: 'ava-text-card',
  standalone: true,
  imports: [CommonModule, IconComponent, CardComponent, CardComponent],
  templateUrl: './text-card.component.html',
  styleUrls: ['./text-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class TextCardComponent {
  @Input() iconName = 'trending-up';
  @Input() title: string = '';
  @Input() value: string | number = '';
  @Input() description: string = '';
  @Input() width = 0;
  @Input() type: 'default' | 'create' | 'prompt' = 'default';
  @Input() iconColor: string = ''
  @Input() userCount: number = 0;
  @Input() promptName: string = '';
  @Input() name: string = '';
  @Input() date: string = ''
  @Input() iconList: string[] = []
  @Output() cardClick = new EventEmitter<void>();

  onCardClick() {
    this.cardClick.emit();
  }
}
