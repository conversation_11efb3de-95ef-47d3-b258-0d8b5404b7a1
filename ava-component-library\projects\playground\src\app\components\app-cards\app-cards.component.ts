import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from "../../../../../play-comp-library/src/lib/components/card/card.component";
import { FeatureCardComponent } from '../../../../../play-comp-library/src/lib/components/feature-card/feature-card.component';
//import { AdvancedCardComponent } from '../../../../../play-comp-library/src/lib/components/advanced-card/advanced-card.component';
import { ApprovalCardComponent } from '../../../../../play-comp-library/src/lib/composite-components/approval-card/approval-card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';
//import { ImageCardComponent } from '../../../../../play-comp-library/src/lib/composite-components/image-card/image-card.component';
import { TextCardComponent } from '../../../../../play-comp-library/src/lib/composite-components/text-card/text-card.component'


@Component({
  selector: 'app-app-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardComponent, FeatureCardComponent
    , ApprovalCardComponent, ButtonComponent, IconComponent, TextCardComponent],
  templateUrl: './app-cards.component.html',
  styleUrl: './app-cards.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppCardsComponent {

  consoleApproval = {
    header: {
      iconName: 'home',
      title: 'High Priority Approval',
      viewAll: true,
    },
    contents: [{
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },

    },
    {
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },


    },
    {
      session1: {
        title: 'Autonomous Systems.... Agent1',
        labels: [
          {
            name: 'Agent',
            color: 'success',
            background: 'red',
            type: 'normal'
          },
          {
            name: 'High',
            color: 'error',
            background: 'red',
            type: 'pill'
          }
        ]
      },
      session2: [
        {
          name: 'Agent',
          color: 'default',
          background: 'red',
          type: 'normal'
        },
        {
          name: 'High',
          color: 'default',
          background: 'red',
          type: 'normal'
        }
      ],
      session3: [
        {
          iconName: 'user',
          label: '<EMAIL>',
        },
        {
          iconName: 'calendar-days',
          label: '12 May 2025',
        },
      ],
      session4:
      {
        status: 'Execution was successful',
        iconName: 'circle-check-big',
      },
    }
    ],
    footer: {},
  }
  uClick(i: any) {

    console.log("log" + i);

  }
}
