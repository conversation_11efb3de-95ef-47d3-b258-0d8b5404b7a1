@if (type === 'circular') {
  <div 
    class="progress-container" 
    role="progressbar" 
    [attr.aria-valuenow]="percentage" 
    aria-valuemin="0" 
    aria-valuemax="100"
  >
    <svg 
      [attr.width]="svgSize" 
      [attr.height]="svgSize" 
      viewBox="0 0 100 100"
    >
      <circle class="progress-background" cx="50" cy="50" r="45" stroke-width="10" fill="none"></circle>
      <circle
        class="progress-bar"
        cx="50" cy="50"
        r="45"
        stroke-width="10"
        fill="none"
        [attr.id]="progressId"
        [attr.stroke]="color"
        [attr.stroke-dasharray]="circumference"
        [attr.stroke-dashoffset]="dashOffset"
        [class.indeterminate]="mode === 'indeterminate'"
      ></circle>

      <!-- Centered percentage text inside the circle -->
      <text 
        x="50" 
        y="50" 
        text-anchor="middle" 
        dy="6" 
        transform="translate(0, 2)"
        class="progress-text"
      >
        {{ percentage }}%
      </text>
    </svg>

    <div class="progress-label">{{ label }}</div>
  </div>
}

@if (type === 'linear') {
  <div 
    class="linear-progress-container" 
    role="progressbar" 
    [attr.aria-valuenow]="percentage" 
    aria-valuemin="0" 
    aria-valuemax="100"
  >
    <div class="progress-label">{{ label }}</div>

    @if (mode === 'determinate' || mode === 'buffer') {
      <div class="progress-percentage">{{ percentage }}%</div>
    }

    <div class="linear-bar">
      @if (mode === 'determinate') {
        <div 
          class="linear-progress" 
          [attr.id]="progressId"
          [style.width.%]="displayPercentage"
          [style.background]="color"
        ></div>
      }

      @if (mode === 'buffer') {
        <div 
          class="buffer-bar" 
          [style.width.%]="bufferValue"
          [style.background]="color"
        ></div>
      }

      @if (mode === 'indeterminate' || mode === 'query') {
        <div 
          class="indeterminate-bar" 
          [style.background]="color"
        ></div>
      }
    </div>
  </div>
}
<div *ngIf="errorMessage" class="progress-error">{{ errorMessage }}</div>