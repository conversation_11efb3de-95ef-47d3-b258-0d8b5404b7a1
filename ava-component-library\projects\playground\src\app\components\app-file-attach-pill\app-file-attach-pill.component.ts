import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FileAttachPillComponent, FileAttachOption } from '../../../../../play-comp-library/src/lib/components/file-attach-pill/file-attach-pill.component';
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-file-attach-pill',
  standalone: true,
  imports: [CommonModule, FileAttachPillComponent, IconsComponent],
  templateUrl: './app-file-attach-pill.component.html',
  styleUrls: ['./app-file-attach-pill.component.scss'],
   encapsulation: ViewEncapsulation.None
})
export class AppFileAttachPillComponent {
  // File attachment options
  fileOptions: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
  ];

  selectedOption: FileAttachOption | null = null;

  onOptionSelected(option: FileAttachOption): void {
    console.log('Selected option:', option);
    this.selectedOption = option;

    // Simulate file attachment based on the selected option
    switch (option.value) {
      case 'computer':
        this.openFileDialog();
        break;
      case 'cloud':
        this.openCloudDialog();
        break;
    }
  }

  private openFileDialog(): void {
    // In a real implementation, this would open a file dialog
    console.log('Opening file dialog...');
    alert('File dialog opened. In a real implementation, this would allow selecting files from your computer.');
  }

  private openCloudDialog(): void {
    // In a real implementation, this would open a cloud storage dialog
    console.log('Opening cloud storage dialog...');
    alert('Cloud storage dialog opened. In a real implementation, this would allow selecting files from cloud storage.');
  }

  private promptForUrl(): void {
    // In a real implementation, this would prompt for a URL
    const url = prompt('Enter the URL of the file you want to attach:');
    if (url) {
      console.log('File URL entered:', url);
      alert(`File URL "${url}" received. In a real implementation, this would download and attach the file.`);
    }
  }

  // Documentation Sections for File Attach Pill Component
  sections = [
    {
      title: 'Basic Usage',
      description: 'This section demonstrates the basic usage of the file attach pill component with an icon and options.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'options',
      type: 'FileAttachOption[]',
      default: 'Computer, Cloud, URL options',
      description: 'Array of file attachment options to display in the dropdown'
    },
    {
      name: 'mainIcon',
      type: 'string',
      default: "'awe_attach_file'",
      description: 'The main icon to display'
    },
    {
      name: 'mainText',
      type: 'string',
      default: "'Attach File'",
      description: 'The text to display when hovering'
    }
  ];

  // Events
  events = [
    {
      name: 'optionSelected',
      type: 'EventEmitter<FileAttachOption>',
      description: 'Emitted when a file attachment option is selected'
    }
  ];

  // Toggle Section Expansion (for showing code examples)
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Toggle Code Visibility (to show or hide the code examples)
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Get Example Code for a Section
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
        import { Component } from '@angular/core';
        import { FileAttachPillComponent, FileAttachOption } from '@awe/play-comp-library';

        @Component({
          selector: 'app-basic-file-attach-pill',
          standalone: true,
          imports: [FileAttachPillComponent],
          template: \`
            <awe-file-attach-pill
              [options]="fileOptions"
              (optionSelected)="onOptionSelected($event)"
            ></awe-file-attach-pill>
          \`
        })
        export class BasicFileAttachPillComponent {
          fileOptions: FileAttachOption[] = [
            { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
            { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
            { name: 'From URL', icon: 'awe_link', value: 'url' }
          ];

          selectedOption: FileAttachOption | null = null;

          onOptionSelected(option: FileAttachOption): void {
            console.log('Selected option:', option);
            this.selectedOption = option;

            // Handle the selected option
            switch (option.value) {
              case 'computer':
                // Open file dialog
                break;
              case 'cloud':
                // Open cloud storage dialog
                break;
              case 'url':
                // Prompt for URL
                break;
            }
          }
        }
      `
    };

    return examples[section] || '';
  }

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }



}
