 <div class="date-picker" [class.open]="isOpen">
      <!-- Input Fields -->
      <div class="input-wrapper">
        <ng-container *ngTemplateOutlet="isRange ? rangeInputs : singleInput"></ng-container>
      </div>

      <!-- Calendar Popup -->
      <div class="calendar-popup" *ngIf="isOpen" (click)="$event.stopPropagation()">
        <!-- Navigation Header -->
        <header class="calendar-header">
          <div class="month-year-display">
            <button class="month-selector"
                    [class.selected]="selectedNavigation === 'month'"
                    (click)="selectNavigation('month')"
                    (keydown)="onMonthYearKeyDown($event, 'month')"
                    [attr.aria-label]="'Select month: ' + monthNames[currentMonth]"
                    [attr.aria-pressed]="selectedNavigation === 'month'"
                    tabindex="0">
              {{ monthNames[currentMonth] }}
            </button>
            <button class="year-selector"
                    [class.selected]="selectedNavigation === 'year'"
                    (click)="selectNavigation('year')"
                    (keydown)="onMonthYearKeyDown($event, 'year')"
                    [attr.aria-label]="'Select year: ' + currentYear"
                    [attr.aria-pressed]="selectedNavigation === 'year'"
                    tabindex="0">
              {{ currentYear }}
            </button>
          </div>
          <div class="nav-controls">
            <button class="nav-btn" (click)="navigate(-1)" [attr.aria-label]="getNavLabel(-1)">
              <ava-icon iconName="ChevronLeft" [iconSize]="18"></ava-icon>
            </button>
            <button class="nav-btn" (click)="navigate(1)" [attr.aria-label]="getNavLabel(1)">
              <ava-icon iconName="ChevronRight" [iconSize]="18"></ava-icon>
            </button>
          </div>
        </header>

        <!-- Calendar Grid -->
        <div class="calendar-grid">
          <div class="weekdays">
            <span *ngFor="let day of weekDays" class="weekday">{{ day }}</span>
          </div>
          <div class="days">
            <button *ngFor="let day of calendarDays; trackBy: trackByDate"
                    class="day"
                    [class]="getDayClasses(day)"
                    (click)="selectDate(day.date)"
                    (mouseenter)="onDayHover(day.date)"
                    (mouseleave)="onDayLeave()">
              {{ day.date.getDate() }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Templates -->
    <ng-template #singleInput>
      <div class="input-group">
        <div class="structured-input" (click)="onInputClick()">
          <input #dayInput
                 class="date-segment day-segment"
                 [(ngModel)]="dayValue"
                 placeholder="DD"
                 maxlength="2"
                 (input)="onDayInput($event)"
                 (keydown)="onKeyDown($event, 'day')"
                 (focus)="onSegmentFocus('day')"
                 (blur)="onSegmentBlur('day')">
          <span class="separator">/</span>
          <input #monthInput
                 class="date-segment month-segment"
                 [(ngModel)]="monthValue"
                 placeholder="MM"
                 maxlength="2"
                 (input)="onMonthInput($event)"
                 (keydown)="onKeyDown($event, 'month')"
                 (focus)="onSegmentFocus('month')"
                 (blur)="onSegmentBlur('month')">
          <span class="separator">/</span>
          <input #yearInput
                 class="date-segment year-segment"
                 [(ngModel)]="yearValue"
                 placeholder="YYYY"
                 maxlength="4"
                 (input)="onYearInput($event)"
                 (keydown)="onKeyDown($event, 'year')"
                 (focus)="onSegmentFocus('year')"
                 (blur)="onSegmentBlur('year')">
        </div>
        <button class="input-btn" (click)="toggle()">
          <ava-icon iconName="CalendarDays" [iconSize]="16"></ava-icon>
        </button>
      </div>
    </ng-template>

    <ng-template #rangeInputs>
      <div class="input-group">
        <div class="structured-input range-structured" (click)="onInputClick()">
          <!-- Start Date -->
          <div class="date-part">
            <input #startDayInput
                   class="date-segment day-segment"
                   [(ngModel)]="startDayValue"
                   placeholder="DD"
                   maxlength="2"
                   (input)="onStartDayInput($event)"
                   (keydown)="onKeyDown($event, 'startDay')"
                   (focus)="onSegmentFocus('startDay')"
                   (blur)="onSegmentBlur('startDay')">
            <span class="separator">/</span>
            <input #startMonthInput
                   class="date-segment month-segment"
                   [(ngModel)]="startMonthValue"
                   placeholder="MM"
                   maxlength="2"
                   (input)="onStartMonthInput($event)"
                   (keydown)="onKeyDown($event, 'startMonth')"
                   (focus)="onSegmentFocus('startMonth')"
                   (blur)="onSegmentBlur('startMonth')">
            <span class="separator">/</span>
            <input #startYearInput
                   class="date-segment year-segment"
                   [(ngModel)]="startYearValue"
                   placeholder="YYYY"
                   maxlength="4"
                   (input)="onStartYearInput($event)"
                   (keydown)="onKeyDown($event, 'startYear')"
                   (focus)="onSegmentFocus('startYear')"
                   (blur)="onSegmentBlur('startYear')">
          </div>

          <span class="range-separator"> - </span>

          <!-- End Date -->
          <div class="date-part">
            <input #endDayInput
                   class="date-segment day-segment"
                   [(ngModel)]="endDayValue"
                   placeholder="DD"
                   maxlength="2"
                   (input)="onEndDayInput($event)"
                   (keydown)="onKeyDown($event, 'endDay')"
                   (focus)="onSegmentFocus('endDay')"
                   (blur)="onSegmentBlur('endDay')">
            <span class="separator">/</span>
            <input #endMonthInput
                   class="date-segment month-segment"
                   [(ngModel)]="endMonthValue"
                   placeholder="MM"
                   maxlength="2"
                   (input)="onEndMonthInput($event)"
                   (keydown)="onKeyDown($event, 'endMonth')"
                   (focus)="onSegmentFocus('endMonth')"
                   (blur)="onSegmentBlur('endMonth')">
            <span class="separator">/</span>
            <input #endYearInput
                   class="date-segment year-segment"
                   [(ngModel)]="endYearValue"
                   placeholder="YYYY"
                   maxlength="4"
                   (input)="onEndYearInput($event)"
                   (keydown)="onKeyDown($event, 'endYear')"
                   (focus)="onSegmentFocus('endYear')"
                   (blur)="onSegmentBlur('endYear')">
          </div>
        </div>
        <button class="input-btn" (click)="toggle()">
          <ava-icon iconName="CalendarDays" [iconSize]="16"></ava-icon>
        </button>
      </div>
    </ng-template>