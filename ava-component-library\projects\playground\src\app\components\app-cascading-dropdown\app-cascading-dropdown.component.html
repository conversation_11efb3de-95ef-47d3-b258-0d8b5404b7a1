<div class="cascading-dropdown-demo">
  <div class="demo-header">
    <h2>Cascading Dropdown Example</h2>
    <p>Select a category to enable the second dropdown with filtered options.</p>
  </div>

  <div class="dropdown-container">
    <!-- First Dropdown - Category Selection -->
    <div class="dropdown-section">
      <h4>Step 1: Select Category</h4>
      <ava-dropdown
        dropdownTitle="Select Category"
        [options]="categoryOptions"
        [search]="true"
        (selectionChange)="onCategoryChange($event)">
      </ava-dropdown>
    </div>

    <!-- Second Dropdown - Sub-category (Disabled until first selection) -->
    <div class="dropdown-section">
      <h4>Step 2: Select Item</h4>
      <ava-dropdown
        *ngIf="!isSecondDropdownDisabled"
        [dropdownTitle]="getSecondDropdownTitle()"
        [options]="subCategoryOptions"
        [disabled]="false"
        [search]="true"
        (selectionChange)="onSubCategoryChange($event)">
      </ava-dropdown>
      <ava-dropdown
        *ngIf="isSecondDropdownDisabled"
        [dropdownTitle]="getSecondDropdownTitle()"
        [options]="[]"
        [disabled]="true"
        [search]="true">
      </ava-dropdown>
    </div>
  </div>

  <!-- Display Current Selections -->
  <div class="selections-display" *ngIf="selectedCategory || selectedSubCategory">
    <h4>Current Selections:</h4>
    <div class="selection-item" *ngIf="selectedCategory">
      <strong>Category:</strong> {{ selectedCategory }}
    </div>
    <div class="selection-item" *ngIf="selectedSubCategory">
      <strong>{{ selectedCategory }}:</strong> {{ selectedSubCategory }}
    </div>
  </div>

  <!-- Reset Button -->
  <div class="actions" *ngIf="selectedCategory">
    <button class="reset-btn" (click)="resetSelections()">
      Reset Selections
    </button>
  </div>

  <!-- Instructions -->
  <div class="instructions">
    <h4>How it works:</h4>
    <ol>
      <li>Select a category from the first dropdown (Car, Fruits, or Vegetables)</li>
      <li>The second dropdown will be enabled and populated with relevant options</li>
      <li>Choose an item from the second dropdown</li>
      <li>Use the reset button to clear selections and start over</li>
    </ol>
  </div>

  <!-- Code Example Section -->
  <div class="code-section">
    <h4>Code Example:</h4>
    <div class="code-block">
      <pre><code>{{ getCodeExample() }}</code></pre>
    </div>
  </div>
</div>
