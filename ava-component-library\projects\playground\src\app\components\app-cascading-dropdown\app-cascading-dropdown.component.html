<div class="cascading-dropdown-demo">
  <div class="demo-header">
    <h2>Cascading Dropdown Example</h2>
    <p>Select a category to enable the second dropdown with filtered options.</p>
  </div>

  <div class="dropdown-container">
    <!-- First Dropdown - Category Selection -->
    <div class="dropdown-section">
      <h4>Step 1: Select Category</h4>
      <ava-dropdown
        dropdownTitle="Select Category"
        [options]="categoryOptions"
        [search]="true"
        (selectionChange)="onCategoryChange($event)">
      </ava-dropdown>
    </div>

    <!-- Second Dropdown - Sub-category (Disabled until first selection) -->
    <div class="dropdown-section">
      <h4>Step 2: Select Item</h4>
      <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">
        <ava-dropdown
          [dropdownTitle]="getSecondDropdownTitle()"
          [options]="subCategoryOptions"
          [disabled]="false"
          [search]="true"
          [selectedValue]="''"
          (selectionChange)="onSubCategoryChange($event)">
        </ava-dropdown>
      </ng-container>

      <ng-template #disabledDropdown>
        <ava-dropdown
          [dropdownTitle]="getSecondDropdownTitle()"
          [options]="[]"
          [disabled]="true"
          [search]="true">
        </ava-dropdown>
      </ng-template>
    </div>
  </div>

  <!-- Display Current Selections -->
  <div class="selections-display" *ngIf="selectedCategory || selectedSubCategory">
    <h4>Current Selections:</h4>
    <div class="selection-item" *ngIf="selectedCategory">
      <strong>Category:</strong> {{ selectedCategory }} ({{ selectedCategoryValue }})
    </div>
    <div class="selection-item" *ngIf="selectedSubCategory">
      <strong>{{ selectedCategory }}:</strong> {{ selectedSubCategory }} ({{ selectedSubCategoryValue }})
    </div>
  </div>

  <!-- Developer Data Access -->
  <div class="data-display" *ngIf="selectionData.isComplete">
    <h4>Available Data for Developers:</h4>
    <div class="data-section">
      <h5>Selection Data Object:</h5>
      <pre>{{ selectionData | json }}</pre>
    </div>
    <div class="data-section">
      <h5>Individual Properties:</h5>
      <ul>
        <li><code>selectedCategory</code>: "{{ selectedCategory }}"</li>
        <li><code>selectedCategoryValue</code>: "{{ selectedCategoryValue }}"</li>
        <li><code>selectedSubCategory</code>: "{{ selectedSubCategory }}"</li>
        <li><code>selectedSubCategoryValue</code>: "{{ selectedSubCategoryValue }}"</li>
        <li><code>selectionData.isComplete</code>: {{ selectionData.isComplete }}</li>
      </ul>
    </div>
  </div>

  <!-- Reset Button -->
  <div class="actions" *ngIf="selectedCategory">
    <button class="reset-btn" (click)="resetSelections()">
      Reset Selections
    </button>
  </div>

  <!-- Code Example Section -->
  <div class="code-section">
    <h4>Code Example:</h4>
    <div class="code-block">
      <pre><code>{{ getCodeExample() }}</code></pre>
    </div>
  </div>
</div>
