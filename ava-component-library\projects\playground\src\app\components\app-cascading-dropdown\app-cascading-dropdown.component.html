<div class="cascading-dropdown-demo">
  <div class="demo-header">
    <h2>Cascading Dropdown Example</h2>
    <p>Select a category to enable the second dropdown with filtered options.</p>
  </div>

  <div class="dropdown-container">
    <!-- First Dropdown - Category Selection -->
    <div class="dropdown-section">
      <h4>Step 1: Select Category</h4>
      <ava-dropdown
        dropdownTitle="Select Category"
        [options]="categoryOptions"
        [search]="true"
        (selectionChange)="onCategoryChange($event)">
      </ava-dropdown>
    </div>

    <!-- Second Dropdown - Sub-category (Disabled until first selection) -->
    <div class="dropdown-section">
      <h4>Step 2: Select Item</h4>
      <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">
        <ava-dropdown
          [dropdownTitle]="getSecondDropdownTitle()"
          [options]="subCategoryOptions"
          [disabled]="false"
          [search]="true"
          [selectedValue]="''"
          (selectionChange)="onSubCategoryChange($event)">
        </ava-dropdown>
      </ng-container>

      <ng-template #disabledDropdown>
        <ava-dropdown
          [dropdownTitle]="getSecondDropdownTitle()"
          [options]="[]"
          [disabled]="true"
          [search]="true">
        </ava-dropdown>
      </ng-template>
    </div>
  </div>

  <!-- Display Current Selections -->
  <div class="selections-display" *ngIf="selectedCategory || selectedSubCategory">
    <h4>Current Selections:</h4>
    <div class="selection-item" *ngIf="selectedCategory">
      <strong>Category:</strong> {{ selectedCategory }} ({{ selectedCategoryValue }})
    </div>
    <div class="selection-item" *ngIf="selectedSubCategory">
      <strong>{{ selectedCategory }}:</strong> {{ selectedSubCategory }} ({{ selectedSubCategoryValue }})
    </div>
  </div>

  <!-- Developer Data Access -->
  <div class="data-display" *ngIf="selectionData.isComplete">
    <h4>Available Data for Developers:</h4>
    <div class="data-section">
      <h5>Selection Data Object:</h5>
      <pre>{{ selectionData | json }}</pre>
    </div>
    <div class="data-section">
      <h5>Individual Properties:</h5>
      <ul>
        <li><code>selectedCategory</code>: "{{ selectedCategory }}"</li>
        <li><code>selectedCategoryValue</code>: "{{ selectedCategoryValue }}"</li>
        <li><code>selectedSubCategory</code>: "{{ selectedSubCategory }}"</li>
        <li><code>selectedSubCategoryValue</code>: "{{ selectedSubCategoryValue }}"</li>
        <li><code>selectionData.isComplete</code>: {{ selectionData.isComplete }}</li>
      </ul>
    </div>
  </div>

  <!-- Reset Button -->
  <div class="actions" *ngIf="selectedCategory">
    <button class="reset-btn" (click)="resetSelections()">
      Reset Selections
    </button>
  </div>

  <!-- 5-Level Cascading Dropdown Example -->
  <div class="five-level-example">
    <h2>5-Level Cascading Dropdown Example</h2>
    <p>Country → State → District → Area → Pincode</p>

    <div class="five-level-container">
      <!-- Country Dropdown -->
      <div class="dropdown-section">
        <h4>Step 1: Select Country</h4>
        <ava-dropdown
          dropdownTitle="Select Country"
          [options]="countryOptions"
          [search]="true"
          (selectionChange)="onCountryChange($event)">
        </ava-dropdown>
      </div>

      <!-- State Dropdown -->
      <div class="dropdown-section">
        <h4>Step 2: Select State</h4>
        <ng-container *ngIf="selectedCountryValue === 'india'">
          <ng-container *ngIf="!isStateDisabled; else disabledStateIndia">
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="stateOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onStateChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledStateIndia>
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="selectedCountryValue === 'usa'">
          <ng-container *ngIf="!isStateDisabled; else disabledStateUSA">
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="stateOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onStateChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledStateUSA>
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="selectedCountryValue === 'uk'">
          <ng-container *ngIf="!isStateDisabled; else disabledStateUK">
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="stateOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onStateChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledStateUK>
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="selectedCountryValue === 'canada'">
          <ng-container *ngIf="!isStateDisabled; else disabledStateCanada">
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="stateOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onStateChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledStateCanada>
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="selectedCountryValue === 'australia'">
          <ng-container *ngIf="!isStateDisabled; else disabledStateAustralia">
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="stateOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onStateChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledStateAustralia>
            <ava-dropdown
              dropdownTitle="Select State"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="!selectedCountryValue">
          <ava-dropdown
            dropdownTitle="Select State"
            [options]="[]"
            [disabled]="true"
            [search]="true">
          </ava-dropdown>
        </ng-container>
      </div>

      <!-- District Dropdown -->
      <div class="dropdown-section">
        <h4>Step 3: Select District</h4>
        <ng-container *ngIf="selectedStateValue && selectedCountryValue">
          <ng-container *ngIf="!isDistrictDisabled; else disabledDistrict">
            <ava-dropdown
              dropdownTitle="Select District"
              [options]="districtOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onDistrictChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledDistrict>
            <ava-dropdown
              dropdownTitle="Select District"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>
        <ng-container *ngIf="!selectedStateValue || !selectedCountryValue">
          <ava-dropdown
            dropdownTitle="Select District"
            [options]="[]"
            [disabled]="true"
            [search]="true">
          </ava-dropdown>
        </ng-container>
      </div>

      <!-- Area Dropdown -->
      <div class="dropdown-section">
        <h4>Step 4: Select Area</h4>
        <ng-container *ngIf="selectedDistrictValue && selectedStateValue && selectedCountryValue">
          <ng-container *ngIf="!isAreaDisabled; else disabledArea">
            <ava-dropdown
              dropdownTitle="Select Area"
              [options]="areaOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onAreaChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledArea>
            <ava-dropdown
              dropdownTitle="Select Area"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>
        <ng-container *ngIf="!selectedDistrictValue || !selectedStateValue || !selectedCountryValue">
          <ava-dropdown
            dropdownTitle="Select Area"
            [options]="[]"
            [disabled]="true"
            [search]="true">
          </ava-dropdown>
        </ng-container>
      </div>

      <!-- Pincode Dropdown -->
      <div class="dropdown-section">
        <h4>Step 5: Select Pincode</h4>
        <ng-container *ngIf="selectedAreaValue && selectedDistrictValue && selectedStateValue && selectedCountryValue">
          <ng-container *ngIf="!isPincodeDisabled; else disabledPincode">
            <ava-dropdown
              dropdownTitle="Select Pincode"
              [options]="pincodeOptions"
              [disabled]="false"
              [search]="true"
              [selectedValue]="''"
              (selectionChange)="onPincodeChange($event)">
            </ava-dropdown>
          </ng-container>
          <ng-template #disabledPincode>
            <ava-dropdown
              dropdownTitle="Select Pincode"
              [options]="[]"
              [disabled]="true"
              [search]="true">
            </ava-dropdown>
          </ng-template>
        </ng-container>
        <ng-container *ngIf="!selectedAreaValue || !selectedDistrictValue || !selectedStateValue || !selectedCountryValue">
          <ava-dropdown
            dropdownTitle="Select Pincode"
            [options]="[]"
            [disabled]="true"
            [search]="true">
          </ava-dropdown>
        </ng-container>
      </div>
    </div>

    <!-- Display 5-Level Selections -->
    <div class="location-display" *ngIf="selectedCountry">
      <h4>Selected Location:</h4>
      <div class="location-item" *ngIf="selectedCountry">
        <strong>Country:</strong> {{ selectedCountry }} ({{ selectedCountryValue }})
      </div>
      <div class="location-item" *ngIf="selectedState">
        <strong>State:</strong> {{ selectedState }} ({{ selectedStateValue }})
      </div>
      <div class="location-item" *ngIf="selectedDistrict">
        <strong>District:</strong> {{ selectedDistrict }} ({{ selectedDistrictValue }})
      </div>
      <div class="location-item" *ngIf="selectedArea">
        <strong>Area:</strong> {{ selectedArea }} ({{ selectedAreaValue }})
      </div>
      <div class="location-item" *ngIf="selectedPincode">
        <strong>Pincode:</strong> {{ selectedPincode }} ({{ selectedPincodeValue }})
      </div>
    </div>

    <!-- Complete Location Data -->
    <div class="location-data-display" *ngIf="locationData.isComplete">
      <h4>Complete Location Data for Developers:</h4>
      <div class="location-data-section">
        <h5>Location Data Object:</h5>
        <pre>{{ locationData | json }}</pre>
      </div>
    </div>

    <!-- Reset Button for 5-Level -->
    <div class="actions" *ngIf="selectedCountry">
      <button class="reset-btn" (click)="resetLocationSelections()">
        Reset Location Selections
      </button>
    </div>
  </div>

  <!-- Code Example Section -->
  <div class="code-section">
    <h4>Code Example:</h4>
    <div class="code-block">
      <pre><code>{{ getCodeExample() }}</code></pre>
    </div>
  </div>
</div>
