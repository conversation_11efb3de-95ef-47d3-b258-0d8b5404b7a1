// Optional: define variables at the top
$button-padding: var(--global-spacing-4) var(--global-spacing-5);
$button-font-size: var(--font-body-2);
$button-gap: 8px;
$button-radius: var(--button-border-radius, 12px);
$button-primary-bg: var(--button-primary-background);
$button-primary-text: var(--button-primary-text);
$button-secondary-bg: var(--button-secondary-background);
$button-secondary-text: var(--button-secondary-text);
$glass-blur: blur(10px);
$glass-brightness: brightness(1.8);
$glass-color: rgba(255, 255, 255, 0.2);
$glass-shadow: 0 0 20px rgba(255, 255, 255, 0.15);
$glass-radius: 50px;

.ava-button {
  display: inline-flex;
  position: relative;
  padding: $button-padding;
  font-size: $button-font-size;
  gap: $button-gap;
  border: none;
  justify-content: center;
  align-items: center;
  backdrop-filter: $glass-blur;
  -webkit-backdrop-filter: $glass-blur;
  border-radius: $button-radius;
  cursor: pointer;
  overflow: hidden;
  transition: background 0.3s ease, transform 0.3s ease;
  z-index: 0;

  &.primary {
    background: $button-primary-bg;
    color: $button-primary-text;

    &.active {
      background: var(--button-primary-background-active)
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
      pointer-events: none;
      color: var(--button-primary-text-disabled);
      background: var(--button-primary-background-disabled);

      &::after {
        display: none;
      }

      &:hover {
        transform: none;
      }
    }

    &.danger {
      color: var(--button-danger-text);
      background: var(--button-danger-background);

      &:hover {
        background: var(--button-danger-background-hover);
      }

      &.active,
      &:active {
        background: var(--button-danger-background-active);
      }
    }

    &.warning {
      color: var(--button-warning-text);
      background: var(--button-warning-background);

      &:hover {
        background: var(--button-warning-background-hover);
      }

      &.active,
      &:active {
        background: var(--button-warning-background-active);
      }
    }

    &.pill {
      border-radius: 50px;
    }

    &.glass {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: var(--button-glassmorphic-blur-30);
      -webkit-backdrop-filter: var(--button-glassmorphic-blur-30);
      box-shadow: var(--button-glassmorphic-light-90);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &.neo {
      color: $button-primary-text;
      transition: all 0.2s ease;
      box-shadow:
        -6px -6px 12px rgba(255, 255, 255, 0.1),
        6px 6px 12px rgba(0, 0, 0, 0.3);

      &:hover {
        box-shadow:
          -4px -4px 8px rgba(255, 255, 255, 0.12),
          4px 4px 8px rgba(0, 0, 0, 0.25);
        transform: translateY(-1px);
      }

      &.active,
      &:active {
        box-shadow:
          inset -4px -4px 8px rgba(255, 255, 255, 0.12),
          inset 4px 4px 8px rgba(0, 0, 0, 0.3);
        transform: translateY(1px);
      }

      &:focus {
        outline: none;
      }

    }


  }

  &.secondary {
    background: $button-secondary-bg;
    color: $button-secondary-text;
    border: var(--button-secondary-border);

    &:hover {
      background: var(--button-secondary-background-hover);
      color: $button-primary-text;
      border: var(--button-secondary-border-hover)
    }

    &.active {
      background: var(--button-secondary-background-active);
      color: var(--button-secondary-text-active);
    }

  }

  &.small {
    padding: var(--button-size-sm-padding);
    font-size: var(--button-size-sm-font);
    height: var(--button-size-sm-height);
    min-width: var(--button-size-sm-min-width);
  }

  &.medium {
    padding: var(--button-size-md-padding);
    font-size: var(--button-size-md-font);
    height: var(--button-size-md-height);
    min-width: var(--button-size-md-min-width);
  }

  &.large {
    font-size: var(--button-size-lg-font);
    height: var(--button-size-lg-height);
    min-width: var(--button-size-lg-min-width);
  }

  &.icon-only-ava-button {
    padding: var(--button-icon-margin);
  }

  &.gradient {
    border: 1px solid var(--button-gradient-border);
  }



  >span {
    position: relative;
    z-index: 2;
  }

  &.ava-button-basic {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: $glass-color;
      backdrop-filter: $glass-blur $glass-brightness;
      -webkit-backdrop-filter: $glass-blur $glass-brightness;
      box-shadow: $glass-shadow;
      pointer-events: none;
      z-index: 3;
      opacity: 0;
      transition: opacity 0.2s;
      border-radius: $glass-radius;
    }

    &:hover::after {
      opacity: 1;
      animation: sweep-in 0.4s ease-out forwards;
    }

    &:not(:hover)::after {
      animation: sweep-out 0.4s ease-out forwards;
    }


  }


}

// === Animations ===
@keyframes sweep-in {
  0% {
    left: -100%;
    opacity: .6;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
  }

  100% {
    left: 0%;
    opacity: .3;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

@keyframes sweep-out {
  0% {
    left: 0%;
    opacity: 0.6;
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  100% {
    left: -100%;
    opacity: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
  }
}