# AVA Component Library (@awe/play-comp-library)

A comprehensive Angular component library providing reusable UI components for AVA platform applications.

## Installation

To install this package in your project:

```bash
npm install @ava/play-comp-library --save
```

### Prerequisites

This library requires the following peer dependencies:
- Angular ^17.0.0

### Setting up .npmrc

Before installing the package, you need to configure your project's `.npmrc` file to authenticate with Azure Artifacts:

1. Create a `.npmrc` file in your project root:
```bash
registry=https://registry.npmjs.org/
@ava:registry=https://localhost:7843
#@ava:registry=https://pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/

; always auth
always-auth=true

; auth token
//pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/:username=ascendionava
//pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/:_authToken=${AZURE_DEVOPS_PAT}
//pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/:email=not-used
```

2. Generate a Personal Access Token (PAT):
   - Go to Azure DevOps → User Settings → Personal Access Tokens
   - Create a new token with "Packaging Read & Write" permissions
   - Set the token as an environment variable:
     ```bash
     export AZURE_DEVOPS_PAT=your_pat_here
     ```

## Usage

1. Import the components you need in your module:
```typescript
import { ButtonComponent } from '@ava/play-comp-library';

@NgModule({
  imports: [
    ButtonComponent
  ]
})
export class YourModule { }
```

2. Use them in your templates:
```html
<awe-button>Click me</awe-button>
```

## Development

### Making Changes to the Library

1. Navigate to the library source:
```bash
cd projects/play-comp-library/src/lib
```

2. Make your changes to the components

3. Update version in `projects/play-comp-library/package.json` following semantic versioning:
   - Patch version (1.0.x) for bug fixes
   - Minor version (1.x.0) for new features
   - Major version (x.0.0) for breaking changes

4. Build the library:
```bash
npm run build-library
```

### Publishing Updates

1. Ensure you have the correct Azure DevOps PAT set up:
```bash
export AZURE_DEVOPS_PAT=your_pat_here
```

2. Navigate to the distribution directory and publish:
```bash
cd dist/play-comp-library
npm publish
```

## Troubleshooting

### Common Issues

1. Authentication Errors:
   - Verify your PAT is correctly set in the environment
   - Ensure your .npmrc file is properly configured
   - Check if your PAT has the correct permissions

2. Version Conflicts:
   - If you encounter peer dependency warnings, you may need to use `--force` or `--legacy-peer-deps`
   - Ensure your project's Angular version matches the library's peer dependencies

## Contributing

1. Create a feature branch from main
2. Make your changes
3. Update tests and documentation
4. Create a pull request

## License

MIT License - see LICENSE file for details
