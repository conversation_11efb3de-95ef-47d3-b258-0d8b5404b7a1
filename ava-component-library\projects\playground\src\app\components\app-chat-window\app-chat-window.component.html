<!-- <div class="wrapper"><awe-chat-window
  [theme]="'dark'"
  [defaultText]="'Ask me'"
  [rightIcons]="rightIcons"
  [(textValue)]="darkPrompt"
  [chatMessages]="darkMessages"
  (iconClicked)="handleIconClick($event)"
  (enterPressed)="handleEnhancedSendDark()">
</awe-chat-window></div>


<br><br>

<awe-chat-window
  [theme]="'light'"
  [defaultText]="'Ask me'"
  [rightIcons]="rightIcons"
  [(textValue)]="lightPrompt"
  [chatMessages]="lightMessages"
  (iconClicked)="handleIconClick($event)"
  (enterPressed)="handleEnhancedSendLight()">
</awe-chat-window> -->



<div class="documentation container">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Chat Window Component</h1>
        <p class="description">
          A versatile chat window component that supports both light and dark themes, with customizable icons and message handling. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ChatWindowComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-chat-window
                    [theme]="'light'"
                    [defaultText]="'Ask me'"
                    [rightIcons]="rightIcons"
                    [(textValue)]="lightPrompt"
                    [chatMessages]="lightMessages"
                    (iconClicked)="handleIconClick($event)"
                    (enterPressed)="handleEnhancedSendLight()">
                  </awe-chat-window>
                </div>
              </div>
            </ng-container>

            <!-- Dark Theme -->
            <ng-container *ngSwitchCase="'Dark Theme'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-chat-window
                    [theme]="'dark'"
                    [defaultText]="'Ask me'"
                    [rightIcons]="rightIcons"
                    [(textValue)]="darkPrompt"
                    [chatMessages]="darkMessages"
                    (iconClicked)="handleIconClick($event)"
                    (enterPressed)="handleEnhancedSendDark()">
                  </awe-chat-window>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getChatWindowCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>

      </div>
    </section>
  </div>

<!-- API Reference -->
<div class="row">
  <div class="col-12">
    <section class="doc-section api-reference">
      <h2>API Reference</h2>
      <h3>Properties</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let prop of apiProps">
            <td><code>{{ prop.name }}</code></td>
            <td><code>{{ prop.type }}</code></td>
            <td><code>{{ prop.default }}</code></td>
            <td>{{ prop.description }}</td>
          </tr>
        </tbody>
      </table>
    </section>
  </div>
</div>

<!-- Events -->
<div class="row">
  <div class="col-12">
    <section class="doc-section">
      <h2>Events</h2>
      <table class="api-table">
        <thead>
          <tr>
            <th>Event</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let event of events">
            <td><code>{{ event.name }}</code></td>
            <td><code>{{ event.type }}</code></td>
            <td>{{ event.description }}</td>
          </tr>
        </tbody>
      </table>
    </section>
  </div>
</div>

