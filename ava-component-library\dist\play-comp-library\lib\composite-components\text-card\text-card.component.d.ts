import * as i0 from "@angular/core";
export declare class TextCardComponent {
    iconName: string;
    title: string;
    value: string | number;
    description: string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TextCardComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TextCardComponent, "ava-text-card", never, { "iconName": { "alias": "iconName"; "required": false; }; "title": { "alias": "title"; "required": false; }; "value": { "alias": "value"; "required": false; }; "description": { "alias": "description"; "required": false; }; }, {}, never, never, true, never>;
}
