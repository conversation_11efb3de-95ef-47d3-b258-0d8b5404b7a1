/**
 * =========================================================================
 * Play+ Design System: Main Styles
 *
 * This file imports the design token system and provides
 * global styles, font definitions, and utility classes.
 * =========================================================================
 */

/* ==========================================================================
   DESIGN TOKENS
   ========================================================================== */
@use "./tokens/index";
@use "./grid.scss";

/* ==========================================================================
   GOOGLE FONTS (CDN)
   ========================================================================== */
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,400;0,700;1,400&family=Mulish:ital,wght@0,400;0,700;1,400&display=swap");

/* ==========================================================================
   LOCAL FONT DEFINITIONS (PP Neue Machina only)
   ========================================================================== */
@font-face {
  font-family: "PP Neue Machina";
  src: url("../assets/fonts/PPNeueMachina-PlainRegular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "PP Neue Machina";
  src: url("../assets/fonts/PPNeueMachina-PlainRegularItalic.ttf")
    format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "PP Neue Machina";
  src: url("../assets/fonts/PPNeueMachina-PlainUltrabold.ttf")
    format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* ==========================================================================
   GLOBAL STYLES
   ========================================================================== */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-body);
  font-size: 16px;
  line-height: var(--global-line-height-default);
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
}

body {
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   FOCUS STYLES
   ========================================================================== */
*:focus-visible {
  outline: var(--accessibility-focus-ring-width)
    var(--accessibility-focus-ring-style) var(--accessibility-focus-ring-color);
  outline-offset: var(--accessibility-focus-ring-offset);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.glass {
  backdrop-filter: blur(var(--glass-backdrop-blur));
  background-color: var(--glass-background-color);
  border: var(--glass-border-width) solid var(--glass-border-color);
  box-shadow: var(--glass-elevation);
}
