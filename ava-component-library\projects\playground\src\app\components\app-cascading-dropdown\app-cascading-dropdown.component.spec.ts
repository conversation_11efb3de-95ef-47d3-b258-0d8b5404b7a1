import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppCascadingDropdownComponent } from './app-cascading-dropdown.component';

describe('AppCascadingDropdownComponent', () => {
  let component: AppCascadingDropdownComponent;
  let fixture: ComponentFixture<AppCascadingDropdownComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppCascadingDropdownComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AppCascadingDropdownComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default values', () => {
    expect(component.selectedCategory).toBe('');
    expect(component.selectedSubCategory).toBe('');
    expect(component.isSecondDropdownDisabled).toBe(true);
    expect(component.subCategoryOptions).toEqual([]);
  });

  it('should have correct category options', () => {
    expect(component.categoryOptions).toEqual([
      { name: 'Car', value: 'car', icon: 'car' },
      { name: 'Fruits', value: 'fruits', icon: 'apple' },
      { name: 'Vegetables', value: 'vegetables', icon: 'carrot' }
    ]);
  });

  it('should enable second dropdown when category is selected', () => {
    const mockSelection = { name: 'Car', value: 'car' };
    
    component.onCategoryChange(mockSelection);
    
    expect(component.selectedCategory).toBe('Car');
    expect(component.isSecondDropdownDisabled).toBe(false);
    expect(component.subCategoryOptions.length).toBeGreaterThan(0);
  });

  it('should load correct sub-options for car category', () => {
    const mockSelection = { name: 'Car', value: 'car' };
    
    component.onCategoryChange(mockSelection);
    
    expect(component.subCategoryOptions).toEqual([
      { name: 'Toyota', value: 'toyota', icon: 'car' },
      { name: 'Honda', value: 'honda', icon: 'car' },
      { name: 'Ford', value: 'ford', icon: 'car' },
      { name: 'BMW', value: 'bmw', icon: 'car' },
      { name: 'Mercedes', value: 'mercedes', icon: 'car' },
      { name: 'Audi', value: 'audi', icon: 'car' }
    ]);
  });

  it('should reset selections correctly', () => {
    // First select a category
    component.onCategoryChange({ name: 'Car', value: 'car' });
    component.onSubCategoryChange({ name: 'Toyota', value: 'toyota' });
    
    // Then reset
    component.resetSelections();
    
    expect(component.selectedCategory).toBe('');
    expect(component.selectedSubCategory).toBe('');
    expect(component.isSecondDropdownDisabled).toBe(true);
    expect(component.subCategoryOptions).toEqual([]);
  });

  it('should return correct dropdown title', () => {
    expect(component.getSecondDropdownTitle()).toBe('Select Sub-category');
    
    component.selectedCategory = 'Car';
    expect(component.getSecondDropdownTitle()).toBe('Select Car');
  });
});
