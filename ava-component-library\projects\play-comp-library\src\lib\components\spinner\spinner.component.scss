$spinner-brand-secondary:#9C27B0;
$spinner-brand-primary:#e2c6fa;


.ava-spinner-container {
  display: flex;
  flex-direction: row; // horizontal alignment
  justify-content: center;
  align-items: center;
  gap: 1.5rem; // space between spinners
  padding: 1rem;
  

  .spinner {
    border-radius: 50%;
    transition: transform 0.45s linear;
    
 &.progress-spinner {
      animation: none !important;
    }

    /* Dynamic Rotation States */
    &.rotate-25 { transform: rotate(90deg); }
    &.rotate-50 { transform: rotate(180deg); }
    &.rotate-75 { transform: rotate(270deg); }
    &.rotate-100 { transform: rotate(360deg); }


  /* Named color variants */
  &.primary {
    border: 3px solid var(--spinner-primary-track);
    border-top-color: var(--spinner-primary-fill);
  }

  &.secondary {
    border: 3px solid var(--spinner-secondary-track);
    border-top-color: var(--spinner-secondary-fill);
  }

  &.success {
    border: 3px solid var(--spinner-success-track);
    border-top-color: var(--spinner-success-fill);
  }

  &.warning {
    border: 3px solid var(--spinner-warning-track);
    border-top-color: var(--spinner-warning-fill);

  }

  &.danger {
     border: 3px solid var(--spinner-error-track);
    border-top-color: var(--spinner-error-fill);
   
  }

  &.purple {
     border: 3px solid $spinner-brand-primary;
    border-top-color: $spinner-brand-secondary;
  }
    &.animated {
      animation: spin 3s linear infinite;
    }

    &.circular {
      border-width: 5px;
    }

    &.dotted {
      border-style: dotted;
      border-width: 3px;
    }
    &.partial {
      border: 3px solid transparent;
      border-top-color: $spinner-brand-secondary;
    }

    &.gradient {
      border: 3px solid transparent;
      border-image: linear-gradient(45deg, $spinner-brand-primary, $spinner-brand-secondary) 1;
    }

    &.dashed {
      border-style: dashed;
      border-width: 3px;
    }

    &.double {
      border-style: double;
      border-width: 3px;
    }

    &.size-sm {
      width: var(--spinner-size-sm);
      height: var(--spinner-size-sm);
    }

    &.size-md {
      width: var(--spinner-size-md);
      height: var(--spinner-size-md);
    }

    &.size-lg {
      width: var(--spinner-size-lg);
      height: var(--spinner-size-lg);
    }

    &.size-xl {
      width: var(--spinner-size-xl);
      height: var(--spinner-size-xl);

    }
  }
}

/* Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }

  
}

