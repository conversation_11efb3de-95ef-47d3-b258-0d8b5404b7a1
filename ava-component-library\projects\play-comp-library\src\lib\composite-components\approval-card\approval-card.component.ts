import { ChangeDetectionStrategy, Component, Input, input, TemplateRef } from '@angular/core';
import { CardComponent } from '../../components/card/card.component';
import { ButtonComponent } from '../../components/button/button.component';
import { CommonModule, NgFor, NgIf } from '@angular/common';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { IconComponent } from '../../components/icon/icon.component';

export interface CardData {
  header?: {
    title: string,
    iconName: string,
    viewAll: boolean
  };
  contents?: [
    { session1: any }
  ];
  footer?: {};
}

@Component({
  selector: 'ava-approval-card',
  imports: [CommonModule, NgIf, NgFor, CardComponent, AvaTagComponent, IconComponent],
  templateUrl: './approval-card.component.html',
  styleUrl: './approval-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApprovalCardComponent {
  @Input() cardData!: any;
  @Input() height: string = '300px';
  @Input() contentTemplate!: TemplateRef<any>;
  @Input() contentsBackground: string = '';
  @Input() cardContainerBackground: string = '';



}
