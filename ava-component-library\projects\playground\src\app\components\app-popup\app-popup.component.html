<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>PopUp Component</h1>
        <p class="description">
          A versatile pop-up component that displays messages with customizable content . Built with
          accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} PopUpComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>



  <!-- Documentation Sections -->
  <section *ngFor="let section of sections; let i = index" class="doc-section">
    <div class="section-header" (click)="toggleCodeVisibility(i, $event)">
      <h2>{{ section.title }}</h2>
      <div class="description-container">
        <p>{{ section.description }}</p>
        <div class="code-toggle">
          <span *ngIf="!section.showCode">View Code</span>
          <span *ngIf="section.showCode">Hide Code</span>
          <!--<awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'"
            iconColor="action"></awe-icons>-->
        </div>
      </div>
    </div>

    <div class="code-example" [class.expanded]="section.showCode">
      <div class="example-preview">
        <!-- Live popup preview based on type -->
        <ng-container [ngSwitch]="section.popupType">
          <div *ngSwitchCase="'info'"><ava-button label="Show Info Popup" variant="primary"
              (click)="showInfoPopup = true">
            </ava-button></div>
          <!-- Info -->
          <ava-popup *ngSwitchCase="'info'" messageAlignment="center" [show]="showInfoPopup"
            title="Message Sent Successfully!" message="Thank you for contacting us." [showHeaderIcon]="true"
            headerIconName="circle-check" iconColor="green" [showClose]="true" (closed)="showInfoPopup = false">
          </ava-popup>

          <div *ngSwitchCase="'warning'">

            <ava-button label="Show Warning Popup" variant="primary" (click)="showWarningPopup = true">
            </ava-button>
          </div>

          <!-- Warning -->
          <ava-popup *ngSwitchCase="'warning'" [show]="showWarningPopup" [title]="''" [showTitle]="false"
            [showHeaderIcon]="false" [showInlineMessage]="true" [inlineIconName]="'info'" [inlineIconSize]="48"
            [inlineIconColor]="'#007bff'" [inlineMessage]="'Heads up!'"
            [message]="'Deleting this item will remove it permanently from your records. This action is not reversible.'"
            [showClose]="true" [showCancel]="true" [cancelButtonLabel]="'Okay'" [cancelButtonVariant]="'primary'"
            [popupWidth]="'400px'" (closed)="showWarningPopup = false">
          </ava-popup>

          <div *ngSwitchCase="'delete'">
            <ava-button label="Show Delete Popup" variant="primary" (click)="showDeletePopup = true">
            </ava-button>
          </div>


          <!-- Delete -->
          <ava-popup *ngSwitchCase="'delete'" [show]="showDeletePopup" [title]="'Delete This Item?'" [showTitle]="true"
            [showHeaderIcon]="true" [headerIconName]="'trash'" [iconSize]="48" [iconColor]="'#dc3545'"
            [message]="'Are you sure you want to delete this item?'" [showClose]="true" [showCancel]="true"
            [showConfirm]="true" [cancelButtonLabel]="'Cancel'" [confirmButtonLabel]="'Delete'"
            [confirmButtonBackground]="'#dc3545'" [confirmButtonVariant]="'primary'" [popupWidth]="'400px'"
            (closed)="showDeletePopup = false">
          </ava-popup>

          <!-- Feedback -->
          <div *ngSwitchCase="'feedback'">
            <ava-button label="Show Feedback Popup" variant="primary" (click)="openSendBackPopup()">
            </ava-button>
            <ava-confirmation-popup *ngSwitchCase="'feedback'" [show]="showSendBackPopup" title="We Value Your Feedback"
              message="Please share your feedback below. Your input helps us make meaningful improvements."
              confirmationLabel="Send Back" (closed)="showSendBackPopup = false">
            </ava-confirmation-popup>
          </div>

          <div *ngSwitchCase="'contact'">
            <ava-button label="Show Contact Popup" variant="primary" (click)="showContactForm = true">
            </ava-button>
          </div>

          <div class="contact-form">

            <ava-popup title="Contact Form" message="Thank you for contacting us." [show]="showContactForm"
              [popupWidth]="'1000px'" [showHeaderIcon]="false" [showClose]="true" [showConfirm]="false"
              [showCancel]="false" role="dialog" aria-modal="true" aria-labelledby="popup-title"
              aria-describedby="popup-message" (closed)="showContactForm = false">

              <form [formGroup]="demoForm" (ngSubmit)="onSubmit()" style="display: grid; gap: 1rem">
                <ava-textbox label="Username" class="left" placeholder="Enter username" formControlName="username"
                  [error]="getFieldError('username')" [required]="true">
                  <ava-icon slot="icon-start" iconName="user" [iconColor]="'purple'"></ava-icon>
                </ava-textbox>

                <ava-textbox label="Email" class="left" type="email" placeholder="<EMAIL>"
                  formControlName="email" [error]="getFieldError('email')" [required]="true">
                  <ava-icon slot="icon-start" iconName="mail" [iconColor]="'purple'"></ava-icon>
                </ava-textbox>

                <ava-textbox label="Phone" class="left" type="tel" placeholder="+****************"
                  formControlName="phone">
                  <ava-icon slot="icon-start" iconName="phone" [iconColor]="'purple'"></ava-icon>
                </ava-textbox>

                <ava-textbox label="Website" class="left" placeholder="your-site" formControlName="website">
                  <span slot="prefix">https://</span>
                  <span slot="suffix">.com</span>
                </ava-textbox>

                <ava-button label="Submit Form" variant="primary" buttonSize="lg" type="submit"></ava-button>
              </form>

              <div style="margin-top: 1rem">
                <strong>Form Status:</strong> {{ demoForm.valid ? "Valid" : "Invalid" }}
                <br />
                <strong>Form Value:</strong> {{ demoForm.value | json }}
              </div>

            </ava-popup>
          </div>

          <!-- Popup Positions Preview -->
          <div *ngSwitchCase="'positions'" class="popup-positions-demo">
            <div class="position-preview-grid">
              <div *ngFor="let pos of popupPositions" class="position-button">
                <ava-button [label]="'Show ' + pos" variant="primary" (click)="openPositionPopup[pos] = true">
                </ava-button>

                <ava-popup [show]="openPositionPopup[pos]" [position]="pos" title="Message Sent Successfully!
" message="Thank you for contacting us." [popupWidth]="'350px'" [showHeaderIcon]="true" [showClose]="true"
                  (closed)="openPositionPopup[pos] = false">
                </ava-popup>
              </div>
            </div>
          </div>


        </ng-container>
      </div>

      <!-- Toggle Code -->
      <div class="code-block" *ngIf="section.showCode">
        <pre><code [innerText]="getExampleCode(section.popupType)"></code></pre>
        <button class="copy-button" (click)="copyCode(section.popupType)">
          <!--<awe-icons iconName="awe_copy"></awe-icons>-->
        </button>
      </div>
    </div>
  </section>