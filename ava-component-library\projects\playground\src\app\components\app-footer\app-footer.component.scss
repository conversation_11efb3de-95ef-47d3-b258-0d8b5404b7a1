.footer-subtitle {
  color: var(--Text-White, #FFF);
  font-family: Mulish;
  font-size: var(--Font-Font-size-Subtitle-1, 24px);
  font-style: normal;
  font-weight: 600;
  line-height: 140%; /* 33.6px */
}

/* Footer Text Clickable Styles */
.footer .footer-text.clickable {
  color: var(--Text-White, #FFF) !important;
  font-family: var(--Font-Font-Family-Body, Inter) !important;
  font-size: var(--Font-Font-size-Body, 16px) !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 140% !important;
}

/* Footer Middle Link Styles */
.footer .footer-link {
  color: var(--Text-White, #FFF) !important;
  font-family: var(--Font-Font-Family-Body, Inter) !important;
  font-size: var(--Font-Font-size-Body, 16px) !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 140% !important;
  text-decoration-line: underline !important;
  text-decoration-style: solid !important;
  text-decoration-skip-ink: auto !important;
  text-decoration-thickness: auto !important;
  text-underline-offset: auto !important;
  text-underline-position: from-font !important;
}

  .footer-logo img {
    width: 190px !important;
    height: 170px !important;
    max-width: none !important;
    max-height: none !important;
  margin-top: 10px;
  margin-left: 350px;
  margin-right: 50px;
}

.footer-bottom {
  display: flex;
  justify-content: center;
  padding-top: var(--spacing-3x);
}

.footer-ascendion {
  font-family: var(--font-font-family-body);
  font-size: var(--font-font-size-body);
  font-style: normal;
  font-weight: var(--font-font-weight-regular);
  line-height: var(--font-line-height-body-regular);
  text-align: center;
  align-items: center;
  justify-content: center;
  color: #FFF;
}


// for only
.footer-only-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-3x);

  img {
    margin-bottom: var(--spacing-2x);
  }

  h3 {
    font-family: var(--font-font-family-body);
    font-size: var(--font-font-size-body);
    font-weight: var(--font-font-weight-bold);
    margin-bottom: var(--spacing-2x);
  }

  button {
    padding: var(--spacing-2x) var(--spacing-3x);
    font-size: var(--font-font-size-body);
    cursor: pointer;
  }
}


.side-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: var(--spacing-3x);

  h3 {
    font-family: var(--font-font-family-body);
    font-size: var(--font-font-size-body);
    font-weight: var(--font-font-weight-bold);
    margin-bottom: var(--spacing-2x);
  }

  button {
    padding: var(--spacing-2x) var(--spacing-3x);
    font-size: var(--font-font-size-body);
    cursor: pointer;
  }
}
.top-content, .middle-content, .bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-3x);

  h3 {
    font-family: var(--font-font-family-body);
    font-size: var(--font-font-size-body);
    font-weight: var(--font-font-weight-bold);
    margin-bottom: var(--spacing-2x);
  }

  button {
    padding: var(--spacing-2x) var(--spacing-3x);
    font-size: var(--font-font-size-body);
    cursor: pointer;
  }
}

.footer-bottom {
  display: flex;
  justify-content: center;
  padding-top: var(--spacing-3x);

  .footer-ascendion {
    font-family: var(--font-font-family-body);
    font-size: var(--font-font-size-body);
    font-style: normal;
    font-weight: var(--font-font-weight-regular);
    line-height: var(--font-line-height-body-regular);
    text-align: center;
  }
}



// .documentation {
//   font-family: Arial, sans-serif;
//   color: #333;
//   line-height: 1.6;

//   .doc-header {
//     margin-bottom: 2rem;

//     h1 {
//       font-size: 2.5rem;
//       margin-bottom: 0.5rem;
//     }

//     .description {
//       font-size: 1.25rem;
//       color: #666;
//     }
//   }

  // .doc-section {
  //   margin-bottom: 3rem;

  //   h2 {
  //     font-size: 2rem;
  //     margin-bottom: 1rem;
  //   }

  //   .code-block {
  //     background-color: #f4f4f4;
  //     // padding: 1rem;
  //     border-radius: 4px;
  //     overflow-x: auto;
  //     // margin-top: 1rem;

  //     pre {
  //       margin: 0;
  //       white-space: pre-wrap;
  //       word-wrap: break-word;
  //       padding: 1rem;
  //     }

  //     .copy-button {
  //       background-color: #0a8148;
  //       color: white;
  //       border: none;
  //       padding: 0.5rem 1rem;
  //       border-radius: 4px;
  //       cursor: pointer;
  //       margin-top: 1rem;
  //       display: inline-block;
  //       margin: 1rem;

  //       &:hover {
  //         background-color: #034525;
  //       }

  //       awe-icons {
  //         vertical-align: middle;
  //       }
  //     }
  //   }

    // .section-header {
    //   cursor: pointer;
    //   padding: 1rem;
    //   border-radius: 4px;
    //   transition: background-color 0.3s;

    //   &:hover {
    //     background-color: #e9ecef;
    //   }

    //   h2 {
    //     margin: 0;
    //     font-size: 1.5rem;
    //   }

    //   p {
    //     margin: 0.5rem 0 0;
    //     color: #6c757d;
    //   }
    // }

    .code-example {
      // border: 1px solid #ddd;
      border-radius: 4px;
      overflow: visible; /* Allow the dropdown to expand beyond the container */
      transition: max-height 0.3s ease, background-color 0.3s;

      &.expanded {
        max-height: 2000px; /* Set a high value to ensure full expansion */
        // background-color: #e4e5e6; /* Light blue background for better contrast */
      }

      .example-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        padding: 1rem;
        // background-color: #e4e5e6; /* Match the expanded background color */

        awe-dropdown {
          flex: 1;
          min-width: 200px;
          max-width: 100%; /* Ensure dropdowns can expand */
        }
      }
    }


//   .api-reference, .events-section {
//     margin-bottom: 3rem;

//     h2 {
//       font-size: 2rem;
//       margin-bottom: 1rem;
//     }

//     .api-table {
//       width: 100%;
//       border-collapse: collapse;

//       th, td {
//         border: 1px solid #ddd;
//         padding: 0.75rem;
//         text-align: left;
//       }

//       th {
//         background-color: #f8f9fa;
//       }

//       tr:nth-child(even) {
//         background-color: #f4f4f4;
//       }
//     }
//   }
// }

// @media (max-width: 768px) {
//   .documentation {
//     padding: 1rem;
//   }

//   .doc-header h1 {
//     font-size: 2rem;
//   }

//   .doc-section h2 {
//     font-size: 1.75rem;
//   }

//   .section-header h2 {
//     font-size: 1.25rem;
//   }

//   .example-preview {
//     flex-direction: column;
//     align-items: stretch;
//   }
// }



:host {
  display: block;
  width: 100%;
}
 
/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}
 
* {
  box-sizing: border-box;
}
 
/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
 
  @media (max-width: 768px) {
    padding: 1rem;
  }
}
 
/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}
 
/* Sections */
.doc-sections {
  margin-top: 4rem;
}
 
.doc-section {
  margin-bottom: 1rem;
 
  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }
 
  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}
 
/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);
 
  h2 {
    margin-bottom: 0.5rem;
  }
 
  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
 
  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);
 
    &:hover {
      text-decoration: underline;
    }
 
    span {
      margin-right: 0.5rem;
    }
 
    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;
 
      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}
 
/* Code example styles */
.code-example {
  margin-top: 1.5rem;
 
  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }
 
  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);
 
    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }
 
    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);
 
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
 
/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
 
  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }
 
  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }
 
  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}
 
/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}