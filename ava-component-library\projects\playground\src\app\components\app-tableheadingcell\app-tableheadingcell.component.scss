// //table css
// table {
//     width: 100%;
//     border-collapse: collapse;
//     table-layout: auto;
//     }
     
     
//     thead {
//       font-family:  var(--font-font-family-body);
//     font-weight: var(--font-font-weight-medium);
//     font-size: var(--font-font-size-body);
//     line-height: var(--font-line-height-button-regular);
//     color: var(--text-title);
//     }
//     tbody {
//       font-family: var(--font-font-family-body);
//     font-weight:  var(--font-font-weight-regular);
//     font-size: var(--font-font-size-body);
//     line-height: var(--font-line-height-caption-regular);
//     letter-spacing: 0%;
//     color: var(--text-body);
//     }