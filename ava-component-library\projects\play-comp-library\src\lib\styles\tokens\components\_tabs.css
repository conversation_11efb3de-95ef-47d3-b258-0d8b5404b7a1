/* Component: Tabs
 * Purpose: Design tokens for Tabs component
 */
:root {
  --tabs-gap: 1rem;
  --tabs-background: var(--color-background-primary, transparent);
  --tabs-list-padding: 0.5rem 0;
  --tabs-tab-color: var(--color-text-primary, #333);
  --tabs-tab-active-color: var(--color-brand-primary, #4A5568);
  --tabs-tab-active-font-weight: 600;
  --tabs-tab-disabled-color: var(--color-text-disabled, #aaa);
  --tabs-tab-font: 400 0.95rem/1.4 'Inter', sans-serif;
  --tabs-tab-padding: 0.25rem 0.75rem;
  --tabs-icon-gap: 0.25rem;
  --tabs-icon-size: 1.5rem;
  --tabs-underline-height: 2px;
  --tabs-underline-color: var(--color-brand-primary, #4A5568);
  --tabs-content-margin: var(--global-spacing-8, 2rem);
  --tabs-content-font: var(--font-body-1, 400 1rem/1.5 'Inter', sans-serif);
  --tabs-content-color: var(--color-text-primary, #222);

  /* <PERSON><PERSON> Variant */
  --tabs-button-radius: var(--global-radius-md, 6px);
  --tabs-button-border: transparent;
  --tabs-button-bg: transparent;
  --tabs-button-shadow: none;
  --tabs-button-active-bg: #fff;
  --tabs-button-active-color: #222;
  --tabs-button-active-border: var(--color-brand-primary, #ff2d55);
  --tabs-button-active-shadow: 0 2px 8px 0 rgba(255, 45, 85, 0.08);
  --tabs-button-inactive-color: #222;
  --tabs-button-hover-bg: #f8f9fb;
  --tabs-button-hover-border: #e5e7eb;
  --tabs-button-hover-color: #222;
  --tabs-button-disabled-bg: transparent;
  --tabs-button-disabled-border: #e5e7eb;
  --tabs-button-disabled-color: #aaa;

  /* Icon Variant */
  --tabs-icon-radius: var(--global-radius-lg, 8px);
  --tabs-icon-bg: transparent;
  --tabs-icon-border: transparent;
  --tabs-icon-shadow: none;
  --tabs-icon-active-bg: #fff;
  --tabs-icon-active-border: var(--color-brand-primary, #ff2d55);
  --tabs-icon-active-shadow: 0 2px 8px 0 rgba(255, 45, 85, 0.08);
  --tabs-icon-hover-bg: #f8f9fb;
  --tabs-icon-hover-border: #e5e7eb;
  --tabs-icon-disabled-bg: transparent;
  --tabs-icon-disabled-border: #e5e7eb;

  /* Scroll Buttons */
  --tabs-scroll-btn-bg: rgba(255, 255, 255, 0.85);
  --tabs-scroll-btn-shadow: 0 2px 8px rgba(0,0,0,0.10);
  --tabs-scroll-btn-hover-bg: #f1f5f9;
  --tabs-scroll-btn-hover-shadow: 0 4px 12px rgba(0,0,0,0.14);
  --tabs-scroll-btn-disabled-bg: rgba(255,255,255,0.7);
  --tabs-scroll-btn-icon-color: #64748b;

  /* Dropdown */
  --tabs-dropdown-arrow-color: #888;
  --tabs-dropdown-arrow-active-color: #ff2d55;
  --tabs-dropdown-bg: #fff;
  --tabs-dropdown-radius: var(--global-radius-lg, 8px);
  --tabs-dropdown-shadow: 0 8px 24px 0 rgba(0,0,0,0.10);
  --tabs-dropdown-item-bg: none;
  --tabs-dropdown-item-color: var(--color-text-secondary);
  --tabs-dropdown-item-radius: var(--global-radius-md, 4px);
  --tabs-dropdown-item-hover-bg: #f8f9fb;
  --tabs-dropdown-item-hover-color: #ff2d55;

  --color-tabs-dropdown-item-text: var(--color-text-secondary);
  --color-tabs-dropdown-item-subtitle: var(--color-text-secondary);
  --tab-dropdown-item-color: var(--color-tabs-dropdown-item-text);
}

.ava-tabs__dropdown-label-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}
.ava-tabs__dropdown-label {
  font-weight: 600;
  font-size: 1rem;
  color: var(--color-tabs-dropdown-item-text);
}
.ava-tabs__dropdown-subtitle {
  font-size: 0.92rem;
  color: var(--color-tabs-dropdown-item-subtitle);
  opacity: 0.8;
  margin-top: 2px;
}
.ava-tabs__dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
} 