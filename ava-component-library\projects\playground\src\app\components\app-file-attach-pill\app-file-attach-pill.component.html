<!-- <div class="container">
  <div class="header">
    <h1>File Attach Pill Component</h1>
    <p>A simple icon component that expands on hover and shows file attachment options on click.</p>
  </div>

  <div class="demo-section">
    <h2>Basic Usage</h2>
    <p>Hover over the icon to see it expand, click to show file attachment options. The dropdown appears upward.</p>

    <div class="demo-container">
      <div class="demo-item">
        <h3>File Attachment</h3>
        <div class="demo-content">
          <awe-file-attach-pill
            [options]="fileOptions"
            (optionSelected)="onOptionSelected($event)"
          ></awe-file-attach-pill>
        </div>
        <p class="demo-caption" *ngIf="selectedOption">Last selected: {{ selectedOption.name }}</p>
      </div>
    </div>
  </div>

  <div class="code-section">
    <h2>Implementation</h2>

    <div class="code-block">
      <h3>HTML</h3>
      <pre><code>&lt;awe-file-attach-pill
  [options]="fileOptions"
  (optionSelected)="onOptionSelected($event)"
&gt;&lt;/awe-file-attach-pill&gt;</code></pre>
    </div>

    <div class="code-block">
      <h3>TypeScript Implementation</h3>
      <pre><code>import {{ '{' }} FileAttachPillComponent, FileAttachOption {{ '}' }} from '&#64;awe/play-comp-library';

&#64;Component({{ '{' }}
  // ...
  imports: [FileAttachPillComponent],
  // ...
{{ '}' }})
export class YourComponent {{ '{' }}
  fileOptions: FileAttachOption[] = [
    {{ '{' }} name: 'From Computer', icon: 'awe_upload', value: 'computer' {{ '}' }},
    {{ '{' }} name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' {{ '}' }},
    {{ '{' }} name: 'From URL', icon: 'awe_link', value: 'url' {{ '}' }}
  ];

  selectedOption: FileAttachOption | null = null;

  onOptionSelected(option: FileAttachOption): void {{ '{' }}
    console.log('Selected option:', option);
    this.selectedOption = option;

    // Handle the selected option
    switch (option.value) {{ '{' }}
      case 'computer':
        // Open file dialog
        break;
      case 'cloud':
        // Open cloud storage dialog
        break;
      case 'url':
        // Prompt for URL
        break;
    {{ '}' }}
  {{ '}' }}
{{ '}' }}</code></pre>
    </div>

  </div>

  <div class="api-section">
    <h2>API Reference</h2>

    <div class="api-block">
      <h3>Inputs</h3>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>options</td>
            <td>FileAttachOption[]</td>
            <td>Computer, Cloud, URL options</td>
            <td>Array of file attachment options to display in the dropdown</td>
          </tr>
          <tr>
            <td>mainIcon</td>
            <td>string</td>
            <td>'awe_attach_file'</td>
            <td>The main icon to display</td>
          </tr>
          <tr>
            <td>mainText</td>
            <td>string</td>
            <td>'Attach File'</td>
            <td>The text to display when hovering</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="api-block">
      <h3>Outputs</h3>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>optionSelected</td>
            <td>EventEmitter&lt;FileAttachOption&gt;</td>
            <td>Emitted when a file attachment option is selected</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="api-block">
      <h3>FileAttachOption Interface</h3>
      <pre><code>interface FileAttachOption {{ '{' }}
  name: string;   // Display name
  icon: string;   // Icon name
  value: string;  // Value identifier
{{ '}' }}</code></pre>
    </div>
  </div>
</div> -->


<div class="documentation">
  <!-- Header -->
  <header class="doc-header">
    <h1>File Attach Pill Component</h1>
    <p class="description">
      A simple icon component that expands on hover and shows file attachment options on click.
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} FileAttachPillComponent, FileAttachOption {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="demo-item">
                <h3>File Attachment</h3>
                <div class="demo-content">
                  <awe-file-attach-pill
                    [options]="fileOptions"
                    (optionSelected)="onOptionSelected($event)"
                  ></awe-file-attach-pill>
                </div>
                <p class="demo-caption" *ngIf="selectedOption">Last selected: {{ selectedOption.name }}</p>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <div class="api-block">
      <h3>Inputs</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let prop of apiProps">
            <td><code>{{ prop.name }}</code></td>
            <td><code>{{ prop.type }}</code></td>
            <td><code>{{ prop.default }}</code></td>
            <td>{{ prop.description }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <div class="api-block">
      <table class="api-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let event of events">
            <td><code>{{ event.name }}</code></td>
            <td><code>{{ event.type }}</code></td>
            <td>{{ event.description }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>
  
</div>
