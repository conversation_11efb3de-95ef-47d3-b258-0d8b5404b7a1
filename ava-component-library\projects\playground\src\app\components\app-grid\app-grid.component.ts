import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";

@Component({
  selector: 'app-grid',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './app-grid.component.html',
  styleUrl: './app-grid.component.scss',
  encapsulation:ViewEncapsulation.None
})
export class AppGridComponent {

   // Documentation Sections for History Cards Component
  sections = [
    {
      title: 'Container',
      description: 'Creates a responsive fixed-width container that centers your content ',
      showCode: false
    },
    {
      title: 'Container Fluid',
      description: ' Creates a full-width container that spans the entire viewport width',
      showCode: false
    },
    {
      title: 'Column System',
      description: 'The grid uses a 12-column system. Each column class represents a portion of the 12 columns',
      showCode: false
    },
    {
      title: 'Responsive Grid Classes',
      description: 'Responsive classes allow different column widths at different screen sizes',
      showCode: false
    },
    {
      title: 'Column Offsets',
      description: 'Offset classes move columns to the right by adding margin-left',
      showCode: false
    },
    {
      title: 'No Gutters',
      description: 'Remove padding between columns using the .no-gutters class on the row',
      showCode: false
    },
    {
      title: 'Flexbox Utilities',
      description: 'Flexbox utility classes for controlling layout, alignment, and distribution of grid items',
      showCode: false
    },
    {
      title: 'Ordering Classes',
      description: 'Change the visual order of specific columns with ordering classes',
      showCode: false
    },
    {
      title: 'Push and Pull Classes',
      description: 'Change the visual position of columns with push and pull classes',
      showCode: false
    }

  ];

  // API Documentation
  apiProps = [
    { 
      name: 'imageUrl', 
      type: 'string', 
      default: '""', 
      description: 'The URL to the image displayed on the history card.' 
    },
    { 
      name: 'title', 
      type: 'string', 
      default: '""', 
      description: 'The title text displayed on the history card.' 
    },
    { 
      name: 'mode', 
      type: "'light' | 'dark'", 
      default: "'light'", 
      description: 'Sets the theme for the history card (light or dark).' 
    }
  ];


  // Toggle Section Expansion (for showing code examples)
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Toggle Code Visibility (to show or hide the code examples)
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Get Example Code for a Section
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'container': `
<div class="container demo-container">
  <div class="demo-content">Fixed-width container</div>
</div>
      `,
      
      'container fluid': `
<div class="container-fluid demo-container">
  <div class="demo-content">Full-width container</div>
</div>      
      `,
      'column system': `
<h4>Full Width (col-12)</h4>
<div class="container">
    <div class="row">
        <div class="col-12 demo-col">col-12</div>
    </div>
</div>

<h4>Two Equal Columns (col-6)</h4>
<div class="container">
    <div class="row">
        <div class="col-6 demo-col">col-6</div>
        <div class="col-6 demo-col">col-6</div>
    </div>
</div>

<h4>Three Equal Columns (col-4)</h4>
<div class="container">
    <div class="row">
        <div class="col-4 demo-col">col-4</div>
        <div class="col-4 demo-col">col-4</div>
        <div class="col-4 demo-col">col-4</div>
    </div>
</div>

<h4>Four Equal Columns (col-3)</h4>
<div class="container">
    <div class="row">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
    </div>
</div>  
    `,
      'responsive grid classes': `
<div class="container">
  <div class="row">
      <div class="col-12 col-sm-6 col-md-4 demo-col">
          Full width on mobile
          <br>Half width on small
          <br>One-third on medium
      </div>
      <div class="col-12 col-sm-6 col-md-4 demo-col">
          Full width on mobile
          <br>Half width on small
          <br>One-third on medium
      </div>
      <div class="col-12 col-sm-12 col-md-4 demo-col">
          Full width on mobile and small
          <br>One-third on medium
      </div>
  </div>
</div>
      `,
      'column offsets': `
<div class="container">
  <div class="row">
    <div class="col-5 offset-4 demo-col">col-5 offset-4</div>
  </div>
  <div class="row mt-3">
    <div class="col-3 offset-3 demo-col">col-3 offset-3</div>
    <div class="col-3 offset-1 demo-col">col-3 offset-3</div>
  </div>
</div>
      `,
      
      'no gutters': `
  <h4>With Gutters (Default)</h4>
  <div class="container">
      <div class="row">
          <div class="col-6 demo-col">With gutter</div>
          <div class="col-6 demo-col">With gutter</div>
      </div>
  </div>

  <h4>Without Gutters</h4>
  <div class="container">
      <div class="row no-gutters">
          <div class="col-6 demo-col">No gutter</div>
          <div class="col-6 demo-col">No gutter</div>
      </div>
  </div>
      `,
      'flexbox utilities': `

            <h4>Flex Direction Column</h4>
            <div class="container">
                <div class="row flex-column">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction Row</h4>
            <div class="container">
                <div class="row flex-row">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction flex-row-reverse</h4>
            <div class="container">
                <div class="row flex-row-reverse" >
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction flex-column-reverse</h4>
            <div class="container">
                <div class="row flex-column-reverse">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-start</h4>
            <div class="container">
                <div class="row justify-content-start">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-end</h4>
            <div class="container">
                <div class="row justify-content-end">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-center</h4>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-around</h4>
            <div class="container">
                <div class="row justify-content-around">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-between</h4>
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Flex Wrap</h4>
            <div class="container">
                <div class="row flex-wrap">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                    
                </div>
            </div>

            <h4>No-Wrap</h4>
            <div class="container">
                <div class="row flex-nowrap">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                </div>
            </div>

            <h4> Flex Wrap reverse</h4>
            <div class="container">
                <div class="row flex-wrap-reverse">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                    <div class="col-3 demo-col">Item 6 </div>
                    <div class="col-3 demo-col">Item 7</div>
                    <div class="col-3 demo-col">Item 8</div>
                </div>
            </div>

            <h4>Align Item Start</h4>
            <div class="container">
                <div class="row align-items-start" >
                    <div class="col-4 demo-col-flex ">Item 1</div>
                    <div class="col-4 demo-col-flex ">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>
            <h4>Align Item Center</h4>
            <div class="container">
                <div class="row align-items-center" >
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item End</h4>
            <div class="container">
                <div class="row align-items-end" >
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item Baseline</h4>
            <div class="container">
                <div class="row align-items-baseline">
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item Stretch</h4>
            <div class="container">
                <div class="row align-items-stretch">
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Content Example</h4>
            <div class="container">
                <div class="row" >
                    <div class="col-4 demo-col-flex align-content-start">align-content-start</div>
                    <div class="col-4 demo-col-flex align-content-center">align-content-center </div>
                    <div class="col-4 demo-col-flex align-content-end">align-content-end</div>
                </div>
            </div>

            <h4>Align Self Example</h4>
            <div class="container">
                <div class="row" >
                    <div class="col demo-col align-self-start">Align Self Start Item</div>
                    <div class="col demo-col align-self-center">Align Self Center Item</div>
                    <div class="col demo-col align-self-end">Align Self End Item</div>
                </div>                
            </div>
            
      `,
      'ordering classes': `
      <div class="container">
        <div class="row">
            <div class="col order-3 demo-col">First in code, but third in display</div>
            <div class="col order-1 demo-col">Second in code, but first in display</div>
            <div class="col order-2 demo-col">Third in code, but second in display</div>
        </div>
      </div>
      `,

      'push and pull classes': `
      <div class="container">
        <div class="row">
          <div class="col-9 push-3 demo-col">Column 1 (pushed right by 3)</div>
          <div class="col-3 pull-9 demo-col">Column 2 (pulled left by 9)</div>
        </div>
      </div>`
    };
  
    return examples[section] || '';
  }
  
  

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

}
