import { Component, ViewEncapsulation, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CheckboxComponent } from "../../../../../play-comp-library/src/lib/components/checkbox/checkbox.component";
// import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";
import { CommonModule } from '@angular/common';

interface CheckboxDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-checkbox',
  imports: [CheckboxComponent, CommonModule],
  templateUrl: './app-checkbox.component.html',
  styleUrls: ['./app-checkbox.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppCheckboxComponent implements AfterViewInit {
  @ViewChild('indeterminateCheckbox') indeterminateCheckbox!: ElementRef;

  // Documentation Sections
  sections: CheckboxDocSection[] = [
    {
      title: 'Basic Checkboxes',
      description: 'Checkbox component with basic states such as unchecked, checked, indeterminate, and disabled.',
      showCode: false
    },
    {
      title: 'Checkbox Variants',
      description: 'Different visual variants: default, with-bg (background fill), and animated (with scaling animation).',
      showCode: false
    },
    {
      title: 'Checkbox Sizes',
      description: 'Available sizes for the checkbox: small, medium, and large across different variants.',
      showCode: false
    },
    {
      title: 'Checkbox Indeterminate',
      description: 'Checkbox component with indeterminate state for partial selections.',
      showCode: false
    },
    {
      title: 'Checkbox Multiple',
      description: 'Nested checkbox hierarchy with automatic parent-child state management.',
      showCode: false
    },
    {
      title: 'Disabled States',
      description: 'Disabled checkboxes in various states and variants.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    { name: 'variant', type: "'default' | 'with-bg' | 'animated'", default: "'default'", description: 'Visual variant of the checkbox.' },
    { name: 'size', type: "'small' | 'medium' | 'large'", default: "'medium'", description: 'Sets the size of the checkbox.' },
    { name: 'label', type: 'string', default: '""', description: 'Label displayed next to the checkbox.' },
    { name: 'isChecked', type: 'boolean', default: 'false', description: 'Indicates whether the checkbox is checked.' },
    { name: 'indeterminate', type: 'boolean', default: 'false', description: 'Defines the indeterminate state for the checkbox.' },
    { name: 'disable', type: 'boolean', default: 'false', description: 'Disables the checkbox when true.' }
  ];

  // Events
  events = [
    { name: 'isCheckedChange', type: 'EventEmitter<boolean>', description: 'Emitted when the checkbox state changes (checked/unchecked).' }
  ];

  // Component State
  parentChecked = false;
  indeterminate = false;

  children = [
    { label: 'Child task 1', checked: false },
    { label: 'Child task 2', checked: false },
    { label: 'Child task 3', checked: false }
  ];

  multiLevelCheckboxes = [
    {
      label: 'Tall Things',
      checked: false,
      indeterminate: false,
      children: [
        { label: 'Buildings', checked: false },
        {
          label: 'Giants',
          checked: false,
          indeterminate: false,
          children: [
            { label: 'Andre', checked: false },
            { label: 'Paul Bunyan', checked: false }
          ]
        },
        { label: 'Two sandwiches', checked: false }
      ]
    },
    {
      label: 'Fast Things',
      checked: false,
      indeterminate: false,
      children: [
        { label: 'Cars', checked: false },
        { label: 'Planes', checked: false },
        {
          label: 'Animals',
          checked: false,
          indeterminate: false,
          children: [
            { label: 'Cheetah', checked: false },
            { label: 'Falcon', checked: false }
          ]
        }
      ]
    }
  ];

  // Demo states for interactive examples
  demoStates = {
    basicDefault: false,
    basicWithBg: false,
    basicAnimated: false,
    sizeSmall: false,
    sizeMedium: true,
    sizeLarge: false
  };

  ngAfterViewInit(): void {
    // Set the indeterminate state for the checkbox
    if (this.indeterminateCheckbox) {
      this.indeterminateCheckbox.nativeElement.indeterminate = true;
    }
  }

  // Section Management
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Parent-Child Checkbox Logic
  onParentChanged(checked: boolean): void {
    this.parentChecked = checked;
    this.indeterminate = false;
    this.children = this.children.map(child => ({ ...child, checked }));
  }

  onChildChanged(index: number, checked: boolean): void {
    this.children[index].checked = checked;
    this.updateParentState();
  }

  updateParentState(): void {
    const total = this.children.length;
    const checkedCount = this.children.filter(c => c.checked).length;

    if (checkedCount === total) {
      this.parentChecked = true;
      this.indeterminate = false;
    } else if (checkedCount === 0) {
      this.parentChecked = false;
      this.indeterminate = false;
    } else {
      this.parentChecked = false;
      this.indeterminate = true;
    }
  }

  // Multi-level Checkbox Logic
  onMultiCheckboxChanged(item: any, checked: boolean): void {
    item.checked = checked;
    item.indeterminate = false;
    
    if (item.children) {
      item.children.forEach((child: any) => this.onMultiCheckboxChanged(child, checked));
    }
    
    this.updateParentStates(this.multiLevelCheckboxes);
  }

  updateParentStates(items: any[]): void {
    items.forEach(item => {
      if (item.children) {
        this.updateParentStates(item.children);
        const total = item.children.length;
        const checkedCount = item.children.filter((c: any) => c.checked || c.indeterminate).length;
        const fullyCheckedCount = item.children.filter((c: any) => c.checked && !c.indeterminate).length;
        
        if (fullyCheckedCount === total) {
          item.checked = true;
          item.indeterminate = false;
        } else if (checkedCount === 0) {
          item.checked = false;
          item.indeterminate = false;
        } else {
          item.checked = false;
          item.indeterminate = true;
        }
      }
    });
  }

  // Example Code Generator
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic checkboxes': `<!-- Basic States -->
<ava-checkbox label="Unchecked"></ava-checkbox>
<ava-checkbox label="Checked" [isChecked]="true"></ava-checkbox>
<ava-checkbox label="Indeterminate" [indeterminate]="true"></ava-checkbox>
<ava-checkbox label="Disabled" [disable]="true"></ava-checkbox>
<ava-checkbox label="Disabled Checked" [disable]="true" [isChecked]="true"></ava-checkbox>`,

      'checkbox variants': `<!-- Default Variant -->
<ava-checkbox variant="default" label="Default" [isChecked]="demoStates.basicDefault" 
  (isCheckedChange)="demoStates.basicDefault = $event"></ava-checkbox>

<!-- With Background Variant -->
<ava-checkbox variant="with-bg" label="With Background" [isChecked]="demoStates.basicWithBg" 
  (isCheckedChange)="demoStates.basicWithBg = $event"></ava-checkbox>

<!-- Animated Variant -->
<ava-checkbox variant="animated" label="Animated" [isChecked]="demoStates.basicAnimated" 
  (isCheckedChange)="demoStates.basicAnimated = $event"></ava-checkbox>

<!-- Variants with Indeterminate State -->
<ava-checkbox variant="default" label="Default Indeterminate" [indeterminate]="true"></ava-checkbox>
<ava-checkbox variant="with-bg" label="With-bg Indeterminate" [indeterminate]="true"></ava-checkbox>
<ava-checkbox variant="animated" label="Animated Indeterminate" [indeterminate]="true"></ava-checkbox>`,

      'checkbox sizes': `<!-- Small Size -->
<ava-checkbox size="small" variant="default" label="Small Default" [isChecked]="demoStates.sizeSmall"></ava-checkbox>
<ava-checkbox size="small" variant="with-bg" label="Small With-bg" [isChecked]="true"></ava-checkbox>
<ava-checkbox size="small" variant="animated" label="Small Animated" [isChecked]="true"></ava-checkbox>

<!-- Medium Size -->
<ava-checkbox size="medium" variant="default" label="Medium Default" [isChecked]="demoStates.sizeMedium"></ava-checkbox>
<ava-checkbox size="medium" variant="with-bg" label="Medium With-bg" [isChecked]="true"></ava-checkbox>
<ava-checkbox size="medium" variant="animated" label="Medium Animated" [isChecked]="true"></ava-checkbox>

<!-- Large Size -->
<ava-checkbox size="large" variant="default" label="Large Default" [isChecked]="demoStates.sizeLarge"></ava-checkbox>
<ava-checkbox size="large" variant="with-bg" label="Large With-bg" [isChecked]="true"></ava-checkbox>
<ava-checkbox size="large" variant="animated" label="Large Animated" [isChecked]="true"></ava-checkbox>`,

      'checkbox indeterminate': `<ava-checkbox
  [label]="'Parent task'"
  [isChecked]="parentChecked"
  [indeterminate]="indeterminate"
  (isCheckedChange)="onParentChanged($event)">
</ava-checkbox>

<div style="margin-left: 20px;">
  <ava-checkbox
    *ngFor="let child of children; let i = index"
    [label]="child.label"
    [isChecked]="child.checked"
    (isCheckedChange)="onChildChanged(i, $event)">
  </ava-checkbox>
</div>

// Component Logic
onParentChanged(checked: boolean): void {
  this.parentChecked = checked;
  this.indeterminate = false;
  this.children = this.children.map(child => ({ ...child, checked }));
}

onChildChanged(index: number, checked: boolean): void {
  this.children[index].checked = checked;
  this.updateParentState();
}`,

      'checkbox multiple': `<ng-container *ngFor="let item of multiLevelCheckboxes">
  <ng-template [ngTemplateOutlet]="renderCheckbox" [ngTemplateOutletContext]="{ item: item }"></ng-template>
</ng-container>

<ng-template #renderCheckbox let-item="item">
  <div class="checkbox-item" [style.margin-left]="item.level ? (item.level * 20) + 'px' : '0'">
    <ava-checkbox
      [label]="item.label"
      [isChecked]="item.checked"
      [indeterminate]="item.indeterminate"
      (isCheckedChange)="onMultiCheckboxChanged(item, $event)">
    </ava-checkbox>

    <div *ngIf="item.children?.length > 0" style="margin-left: 20px;">
      <ng-container *ngFor="let child of item.children">
        <ng-template [ngTemplateOutlet]="renderCheckbox" [ngTemplateOutletContext]="{ item: child }"></ng-template>
      </ng-container>
    </div>
  </div>
</ng-template>`,

      'disabled states': `<!-- Disabled in different variants -->
<ava-checkbox variant="default" label="Disabled Default" [disable]="true"></ava-checkbox>
<ava-checkbox variant="default" label="Disabled Checked Default" [disable]="true" [isChecked]="true"></ava-checkbox>
<ava-checkbox variant="default" label="Disabled Indeterminate Default" [disable]="true" [indeterminate]="true"></ava-checkbox>

<ava-checkbox variant="with-bg" label="Disabled With-bg" [disable]="true"></ava-checkbox>
<ava-checkbox variant="with-bg" label="Disabled Checked With-bg" [disable]="true" [isChecked]="true"></ava-checkbox>
<ava-checkbox variant="with-bg" label="Disabled Indeterminate With-bg" [disable]="true" [indeterminate]="true"></ava-checkbox>

<ava-checkbox variant="animated" label="Disabled Animated" [disable]="true"></ava-checkbox>
<ava-checkbox variant="animated" label="Disabled Checked Animated" [disable]="true" [isChecked]="true"></ava-checkbox>
<ava-checkbox variant="animated" label="Disabled Indeterminate Animated" [disable]="true" [indeterminate]="true"></ava-checkbox>`
    };
    return examples[section] || '';
  }

  // Copy Code to Clipboard
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}