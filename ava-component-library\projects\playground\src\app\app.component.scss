.header {
  color: #fff;
  position: fixed;
  /* Fix the header position */
  top: 0;
  left: 0;
  width: 100%;
  /* Ensure it spans the full width */
  z-index: 1000;

  .outer-box {
    position: fixed;
    top: 0px;
    right: 0px;
    z-index: 1000;

  }
}



.layout {
  display: flex;
  height: 100vh; // full height layout

  .sidebar {
    width: 250px; // adjust as needed
    background-color: #f0f0f0; // example color
    padding: 1rem;
    overflow-y: scroll;

    ul {
      margin-top: 75px;
      margin-left: 0;
      padding: 0;

      li {
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid #c3bebe;
        padding: 10px 10px;

        a {
          text-decoration: none;
          color: #596b56;

          &:hover {

            font-weight: bold;
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    background-color: #ffffff; // main content background
    padding: 1rem;
    overflow-y: auto;
  }
}

html[data-theme="dark"] {
  background: black;

  body {
    background-color: transparent;
  }

  .layout {
    background-color: transparent;

    .sidebar {
      background-color: #1f1e1e;

      h2 {
        color: #fff !important;
      }

      ul {
        li {
          a {
            color: #fff;
          }
        }
      }

    }

    .content {

      h1,
      h2,
      p {
        color: #fff !important;
      }

      background-color: #151414;
    }
  }
}