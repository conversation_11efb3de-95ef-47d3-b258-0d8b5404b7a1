<ava-card>
    <div content>
        <div [ngClass]="getWrapperClass()">
            <ng-container [ngSwitch]="imagePosition || 'left'">
                <!-- Image first for left or top -->
                <ng-container *ngSwitchCase="'left'">
                    <div class="left-wrapper">
                        <img [src]="imageUrl" />
                    </div>
                    <div class="right-wrapper">
                        <p>{{ title }}</p>
                        <span>{{ name }}</span>
                    </div>
                </ng-container>

                <ng-container *ngSwitchCase="'right'">
                    <div class="right-wrapper">
                        <p>{{ title }}</p>
                        <span>{{ name }}</span>
                    </div>
                    <div class="left-wrapper">
                        <img [src]="imageUrl" />
                    </div>
                </ng-container>

                <ng-container *ngSwitchCase="'top'">
                    <div class="top-wrapper">
                        <img [src]="imageUrl" />
                    </div>
                    <div class="bottom-wrapper">
                        <p>{{ title }}</p>
                        <span>{{ name }}</span>
                    </div>
                </ng-container>

                <ng-container *ngSwitchCase="'bottom'">
                    <div class="bottom-wrapper">
                        <p>{{ title }}</p>
                        <span>{{ name }}</span>
                    </div>
                    <div class="top-wrapper">
                        <img [src]="imageUrl" />
                    </div>
                </ng-container>
            </ng-container>
        </div>
    </div>
</ava-card>