import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild, ElementRef } from '@angular/core';
import { IconComponent } from '../icon/icon.component';
export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isInRange: boolean;
  isRangeStart: boolean;
  isRangeEnd: boolean;
}


@Component({
  selector: 'ava-calendar',
  imports: [CommonModule, FormsModule, IconComponent],
  templateUrl: './calendar.component.html',
  styleUrl: './calendar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CalendarComponent implements OnInit, OnDestroy {
  @Input() isRange = false;
  @Input() selectedDate: Date | null = null;
  @Input() dateRange: DateRange = { start: null, end: null };

  @Output() dateSelected = new EventEmitter<Date>();
  @Output() rangeSelected = new EventEmitter<DateRange>();

  // ViewChild references for input navigation
  @ViewChild('dayInput') dayInput!: ElementRef<HTMLInputElement>;
  @ViewChild('monthInput') monthInput!: ElementRef<HTMLInputElement>;
  @ViewChild('yearInput') yearInput!: ElementRef<HTMLInputElement>;
  @ViewChild('startDayInput') startDayInput!: ElementRef<HTMLInputElement>;
  @ViewChild('startMonthInput') startMonthInput!: ElementRef<HTMLInputElement>;
  @ViewChild('startYearInput') startYearInput!: ElementRef<HTMLInputElement>;
  @ViewChild('endDayInput') endDayInput!: ElementRef<HTMLInputElement>;
  @ViewChild('endMonthInput') endMonthInput!: ElementRef<HTMLInputElement>;
  @ViewChild('endYearInput') endYearInput!: ElementRef<HTMLInputElement>;

  // State
  isOpen = false;
  currentMonth = new Date().getMonth();
  currentYear = new Date().getFullYear();
  hoverDate: Date | null = null;
  isSelectingRangeEnd = false;
  currentSegment: string = '';
  selectedNavigation: 'month' | 'year' = 'month';

  // Structured input values for single date
  dayValue = '';
  monthValue = '';
  yearValue = '';

  // Structured input values for range
  startDayValue = '';
  startMonthValue = '';
  startYearValue = '';
  endDayValue = '';
  endMonthValue = '';
  endYearValue = '';

  // Constants
  readonly monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  readonly weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  private readonly today = new Date();

  // Computed properties
  get yearRange(): number[] {
    const currentYear = new Date().getFullYear();
    const start = currentYear - 50; // Show 50 years before current year
    const end = currentYear + 50;   // Show 50 years after current year
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  get calendarDays(): CalendarDay[] {
    const firstDay = new Date(this.currentYear, this.currentMonth, 1);
    const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
    const daysInMonth = lastDay.getDate();

    // Calculate start date (Monday of the week containing the 1st)
    const startDate = new Date(firstDay);
    const dayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0, Sunday = 6
    startDate.setDate(firstDay.getDate() - dayOfWeek);

    // Calculate how many weeks we need
    const weeksNeeded = Math.ceil((daysInMonth + dayOfWeek) / 7);
    const totalCells = weeksNeeded * 7;

    const days: CalendarDay[] = [];

    // Generate all days for the calendar grid
    for (let i = 0; i < totalCells; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const isCurrentMonth = date.getMonth() === this.currentMonth && date.getFullYear() === this.currentYear;

      days.push({
        date,
        isCurrentMonth,
        isToday: this.isSameDay(date, this.today),
        isSelected: this.isDateSelected(date),
        isInRange: this.isDateInRange(date),
        isRangeStart: this.isDateRangeStart(date),
        isRangeEnd: this.isDateRangeEnd(date)
      });
    }

    return days;
  }

  ngOnInit(): void {
    this.updateInputValues();

    // Initialize structured input values if dates are provided
    if (this.isRange) {
      this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);
    } else if (this.selectedDate) {
      this.updateStructuredInputFromDate(this.selectedDate);
    }
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  // Public methods
  toggle(): void {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.updateInputValues();
    }
  }

  close(): void {
    this.isOpen = false;
  }

  navigate(direction: number): void {
    if (this.selectedNavigation === 'month') {
      this.navigateMonth(direction);
    } else if (this.selectedNavigation === 'year') {
      this.navigateYear(direction);
    }
  }

  selectNavigation(type: 'month' | 'year'): void {
    this.selectedNavigation = type;
  }

  onMonthYearKeyDown(event: KeyboardEvent, type: 'month' | 'year'): void {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.selectNavigation(type);
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.selectNavigation(type);
        this.navigate(-1);
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.selectNavigation(type);
        this.navigate(1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (type === 'month') {
          this.selectNavigation('month');
          this.navigate(-1);
        } else {
          this.selectNavigation('year');
          this.navigate(-1);
        }
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (type === 'month') {
          this.selectNavigation('month');
          this.navigate(1);
        } else {
          this.selectNavigation('year');
          this.navigate(1);
        }
        break;
      case 'Tab':
        // Allow default tab behavior to move between month and year
        break;
      default:
        // Ignore other keys
        break;
    }
  }

  getNavLabel(direction: number): string {
    const action = direction > 0 ? 'Next' : 'Previous';
    if (this.selectedNavigation === 'month') {
      return `${action} month`;
    } else {
      return `${action} year`;
    }
  }

  selectDate(date: Date): void {
    if (this.isRange) {
      this.handleRangeSelection(date);
    } else {
      this.handleSingleSelection(date);
    }
  }

  onDayHover(date: Date): void {
    if (this.isRange && this.dateRange.start && !this.dateRange.end) {
      this.hoverDate = date;
    }
  }

  onDayLeave(): void {
    this.hoverDate = null;
  }

  // Structured input event handlers
  onSegmentFocus(segment: string): void {
    this.currentSegment = segment;
  }

  onSegmentBlur(segment: string): void {
    this.validateAndUpdateDate();
  }

  onKeyDown(event: KeyboardEvent, segment: string): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    // Allow navigation keys
    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      if (event.key === 'Enter') {
        this.validateAndUpdateDate();
      }
      return;
    }

    // Only allow numbers
    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
      return;
    }

    // Auto-advance to next segment when current is complete
    setTimeout(() => {
      this.checkAutoAdvance(segment, target.value);
    }, 0);
  }

  onInputClick(): void {
    if (!this.isOpen) {
      this.toggle();
    }
  }

  // Day input handlers
  onDayInput(event: any): void {
    const value = event.target.value;
    this.dayValue = this.limitValue(value, 1, 31, 2);
    if (this.dayValue.length === 2) {
      this.focusNextSegment('day');
    }
  }

  onMonthInput(event: any): void {
    const value = event.target.value;
    this.monthValue = this.limitValue(value, 1, 12, 2);
    if (this.monthValue.length === 2) {
      this.focusNextSegment('month');
    }
  }

  onYearInput(event: any): void {
    const value = event.target.value;
    this.yearValue = this.limitValue(value, 1900, 2100, 4);
    if (this.yearValue.length === 4) {
      this.validateAndUpdateDate();
    }
  }

  // Range start input handlers
  onStartDayInput(event: any): void {
    const value = event.target.value;
    this.startDayValue = this.limitValue(value, 1, 31, 2);
    if (this.startDayValue.length === 2) {
      this.focusNextSegment('startDay');
    }
  }

  onStartMonthInput(event: any): void {
    const value = event.target.value;
    this.startMonthValue = this.limitValue(value, 1, 12, 2);
    if (this.startMonthValue.length === 2) {
      this.focusNextSegment('startMonth');
    }
  }

  onStartYearInput(event: any): void {
    const value = event.target.value;
    this.startYearValue = this.limitValue(value, 1900, 2100, 4);
    if (this.startYearValue.length === 4) {
      this.focusNextSegment('startYear');
    }
  }

  // Range end input handlers
  onEndDayInput(event: any): void {
    const value = event.target.value;
    this.endDayValue = this.limitValue(value, 1, 31, 2);
    if (this.endDayValue.length === 2) {
      this.focusNextSegment('endDay');
    }
  }

  onEndMonthInput(event: any): void {
    const value = event.target.value;
    this.endMonthValue = this.limitValue(value, 1, 12, 2);
    if (this.endMonthValue.length === 2) {
      this.focusNextSegment('endMonth');
    }
  }

  onEndYearInput(event: any): void {
    const value = event.target.value;
    this.endYearValue = this.limitValue(value, 1900, 2100, 4);
    if (this.endYearValue.length === 4) {
      this.validateAndUpdateDate();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.isOpen && !this.isClickInside(event)) {
      this.close();
    }
  }

  // Template methods
  trackByDate(_index: number, day: CalendarDay): string {
    return day.date.toDateString();
  }

  getDayClasses(day: CalendarDay): string {
    const classes = [];
    if (!day.isCurrentMonth) classes.push('other-month');
    if (day.isToday) classes.push('today');
    if (day.isSelected) classes.push('selected');
    if (day.isInRange) classes.push('in-range');
    if (day.isRangeStart) classes.push('range-start');
    if (day.isRangeEnd) classes.push('range-end');
    return classes.join(' ');
  }



  formatDate(date: Date | null): string {
    return date ? this.toDateString(date) : '';
  }

  formatDateRange(): string {
    if (!this.dateRange.start) return '';
    const start = this.toDateString(this.dateRange.start);
    const end = this.dateRange.end ? this.toDateString(this.dateRange.end) : '';
    return end ? `${start} - ${end}` : start;
  }

  // Helper methods for structured input
  private limitValue(value: string, min: number, max: number, maxLength: number): string {
    // Remove non-numeric characters
    const numericValue = value.replace(/\D/g, '');

    // Limit length
    const limitedValue = numericValue.slice(0, maxLength);

    // Convert to number and check bounds
    const num = parseInt(limitedValue, 10);
    if (isNaN(num)) return '';

    // For 2-digit fields, auto-pad and validate
    if (maxLength === 2 && limitedValue.length === 2) {
      if (num < min) return min.toString().padStart(2, '0');
      if (num > max) return max.toString().padStart(2, '0');
      return limitedValue.padStart(2, '0');
    }

    // For 4-digit year, validate range
    if (maxLength === 4 && limitedValue.length === 4) {
      if (num < min) return min.toString();
      if (num > max) return max.toString();
    }

    return limitedValue;
  }

  private focusNextSegment(currentSegment: string): void {
    const segmentMap: { [key: string]: ElementRef<HTMLInputElement> | null } = {
      'day': this.monthInput,
      'month': this.yearInput,
      'startDay': this.startMonthInput,
      'startMonth': this.startYearInput,
      'startYear': this.endDayInput,
      'endDay': this.endMonthInput,
      'endMonth': this.endYearInput
    };

    const nextInput = segmentMap[currentSegment];
    if (nextInput) {
      setTimeout(() => {
        nextInput.nativeElement.focus();
        nextInput.nativeElement.select();
      }, 0);
    }
  }

  private checkAutoAdvance(segment: string, value: string): void {
    const maxLengths: { [key: string]: number } = {
      'day': 2, 'month': 2, 'year': 4,
      'startDay': 2, 'startMonth': 2, 'startYear': 4,
      'endDay': 2, 'endMonth': 2, 'endYear': 4
    };

    if (value.length >= maxLengths[segment]) {
      this.focusNextSegment(segment);
    }
  }

  private validateAndUpdateDate(): void {
    if (this.isRange) {
      this.validateRangeDate();
    } else {
      this.validateSingleDate();
    }
  }

  private validateSingleDate(): void {
    if (this.dayValue && this.monthValue && this.yearValue) {
      const day = parseInt(this.dayValue, 10);
      const month = parseInt(this.monthValue, 10);
      const year = parseInt(this.yearValue, 10);

      const date = new Date(year, month - 1, day);
      if (this.isValidDate(date, day, month - 1, year)) {
        this.selectedDate = date;
        this.currentMonth = date.getMonth();
        this.currentYear = date.getFullYear();
        this.dateSelected.emit(date);
      }
    }
  }

  private validateRangeDate(): void {
    // Validate start date
    if (this.startDayValue && this.startMonthValue && this.startYearValue) {
      const startDay = parseInt(this.startDayValue, 10);
      const startMonth = parseInt(this.startMonthValue, 10);
      const startYear = parseInt(this.startYearValue, 10);

      const startDate = new Date(startYear, startMonth - 1, startDay);
      if (this.isValidDate(startDate, startDay, startMonth - 1, startYear)) {
        this.dateRange.start = startDate;
        this.currentMonth = startDate.getMonth();
        this.currentYear = startDate.getFullYear();
      }
    }

    // Validate end date
    if (this.endDayValue && this.endMonthValue && this.endYearValue) {
      const endDay = parseInt(this.endDayValue, 10);
      const endMonth = parseInt(this.endMonthValue, 10);
      const endYear = parseInt(this.endYearValue, 10);

      const endDate = new Date(endYear, endMonth - 1, endDay);
      if (this.isValidDate(endDate, endDay, endMonth - 1, endYear)) {
        this.dateRange.end = endDate;

        // Emit range if both dates are valid
        if (this.dateRange.start && this.dateRange.end) {
          this.rangeSelected.emit(this.dateRange);
        }
      }
    }
  }

  // Private methods
  private navigateMonth(direction: number): void {
    const newDate = new Date(this.currentYear, this.currentMonth + direction, 1);
    this.currentMonth = newDate.getMonth();
    this.currentYear = newDate.getFullYear();
  }

  private navigateYear(direction: number): void {
    this.currentYear += direction;
    // Ensure year stays within reasonable bounds
    if (this.currentYear < 1900) this.currentYear = 1900;
    if (this.currentYear > 2100) this.currentYear = 2100;
  }

  private handleSingleSelection(date: Date): void {
    this.selectedDate = date;
    this.updateStructuredInputFromDate(date);
    this.dateSelected.emit(date);
    this.close();
  }

  private handleRangeSelection(date: Date): void {
    if (!this.dateRange.start || (this.dateRange.start && this.dateRange.end)) {
      this.startNewRange(date);
    } else {
      this.completeRange(date);
    }
  }

  private startNewRange(date: Date): void {
    this.dateRange = { start: date, end: null };
    this.updateStructuredRangeInputFromDates(date, null);
    this.isSelectingRangeEnd = true;
  }

  private completeRange(date: Date): void {
    const start = this.dateRange.start!;
    if (date < start) {
      this.dateRange = { start: date, end: start };
    } else {
      this.dateRange.end = date;
    }
    this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);
    this.rangeSelected.emit(this.dateRange);
    this.isSelectingRangeEnd = false;
    this.close();
  }

  private updateStructuredInputFromDate(date: Date | null): void {
    if (date) {
      this.dayValue = date.getDate().toString().padStart(2, '0');
      this.monthValue = (date.getMonth() + 1).toString().padStart(2, '0');
      this.yearValue = date.getFullYear().toString();
    } else {
      this.dayValue = '';
      this.monthValue = '';
      this.yearValue = '';
    }
  }

  private updateStructuredRangeInputFromDates(startDate: Date | null, endDate: Date | null): void {
    if (startDate) {
      this.startDayValue = startDate.getDate().toString().padStart(2, '0');
      this.startMonthValue = (startDate.getMonth() + 1).toString().padStart(2, '0');
      this.startYearValue = startDate.getFullYear().toString();
    } else {
      this.startDayValue = '';
      this.startMonthValue = '';
      this.startYearValue = '';
    }

    if (endDate) {
      this.endDayValue = endDate.getDate().toString().padStart(2, '0');
      this.endMonthValue = (endDate.getMonth() + 1).toString().padStart(2, '0');
      this.endYearValue = endDate.getFullYear().toString();
    } else {
      this.endDayValue = '';
      this.endMonthValue = '';
      this.endYearValue = '';
    }
  }

  private updateInputValues(): void {
    if (this.isRange) {
      this.updateStructuredRangeInputFromDates(this.dateRange.start, this.dateRange.end);
    } else {
      this.updateStructuredInputFromDate(this.selectedDate);
    }
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    
    const parts = dateStr.split('/');
    if (parts.length !== 3) return null;
    
    const [day, month, year] = parts.map(p => parseInt(p, 10));
    if ([day, month, year].some(isNaN)) return null;
    
    const date = new Date(year, month - 1, day);
    return this.isValidDate(date, day, month - 1, year) ? date : null;
  }

  private isValidDate(date: Date, day: number, month: number, year: number): boolean {
    return date.getDate() === day && 
           date.getMonth() === month && 
           date.getFullYear() === year &&
           year >= 1900 && year <= 2100;
  }

  private toDateString(date: Date): string {
    return [
      date.getDate().toString().padStart(2, '0'),
      (date.getMonth() + 1).toString().padStart(2, '0'),
      date.getFullYear()
    ].join('/');
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.toDateString() === date2.toDateString();
  }

  private isDateSelected(date: Date): boolean {
    return !this.isRange && this.selectedDate ? 
           this.isSameDay(date, this.selectedDate) : false;
  }

  private isDateInRange(date: Date): boolean {
    if (!this.isRange || !this.dateRange.start) return false;
    
    const end = this.dateRange.end || this.hoverDate;
    if (!end) return false;
    
    const [rangeStart, rangeEnd] = this.dateRange.start < end ? 
                                   [this.dateRange.start, end] : 
                                   [end, this.dateRange.start];
    
    return date > rangeStart && date < rangeEnd;
  }

  private isDateRangeStart(date: Date): boolean {  
    return this.isRange && this.dateRange.start ? 
           this.isSameDay(date, this.dateRange.start) : false;
  }

  private isDateRangeEnd(date: Date): boolean {
    return this.isRange && this.dateRange.end ? 
           this.isSameDay(date, this.dateRange.end) : false;
  }

  private isClickInside(event: Event): boolean {
    const target = event.target as Element;
    return target.closest('.date-picker') !== null;
  }
}