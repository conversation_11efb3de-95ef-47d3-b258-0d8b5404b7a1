<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Dividers Component</h1>
        <p class="description">
          A versatile divider component that supports custom colors, thickness, types, and optional animations. Designed
          for flexible styling and visual separation.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} DividersComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'"
                  iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12 mb-3">
                  <awe-dividers>
                  </awe-dividers>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Divider Thickness'">
              <div class="row g-3">
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'2px'">
                  </awe-dividers>
                </div>
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'4px'">
                  </awe-dividers>
                </div>
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'6px'">
                  </awe-dividers>
                </div>
              </div>
            </ng-container>

            <!-- Divider Colors -->
            <ng-container *ngSwitchCase="'Divider Colors'">
              <div class="row g-3">
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#018266'" [thickness]="'5px'">
                  </awe-dividers>
                </div>
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#c9372c'" [hoverColor]="'#ab2e25'" [thickness]="'5px'">
                  </awe-dividers>
                </div>
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#ffcf40'" [hoverColor]="'#b88a00'" [thickness]="'5px'">
                  </awe-dividers>
                </div>

              </div>
            </ng-container>



            <ng-container *ngSwitchCase="'Divider Styles'">
              <div class="row g-3">
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#018266'" [hoverColor]="'#01654f'" [thickness]="'2px'"
                    [dividerType]="'solid'">
                  </awe-dividers>
                </div>
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#c9372c'" [hoverColor]="'#ab2e25'" [thickness]="'4px'"
                    [dividerType]="'dashed'"></awe-dividers>
                </div>

                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#ffcf40'" [hoverColor]="'#b88a00'" [thickness]="'5px'"
                    [dividerType]="'dotted'">
                  </awe-dividers>
                </div>
              </div>
            </ng-container>


            <!-- Animated Dividers -->
            <ng-container *ngSwitchCase="'Animated Dividers'">
              <div class="row g-3">
                <div class="col-12 mb-3">
                  <awe-dividers [defaultColor]="'#858AAD'" [thickness]="'4px'" [dividerType]="'solid'"
                    [animation]="true"></awe-dividers>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>