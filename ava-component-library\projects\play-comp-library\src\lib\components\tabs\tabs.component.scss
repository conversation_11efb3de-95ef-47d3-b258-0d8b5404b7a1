.ava-tabs__list {
  display: flex;
  align-items: center;
  gap: var(--tabs-gap, 2rem);
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
  background: var(--tabs-background, transparent);
  padding: var(--tabs-list-padding, 1rem 0);
  min-width: 0;
  width: 100%;
  max-width: 100%;
  scrollbar-width: none; // For Firefox
  padding-left: 32px;
  padding-right: 32px;
  &::-webkit-scrollbar {
    display: none; // For Chrome, Safari, and Opera
  }
}

.ava-tabs__tab {
  --tab-icon-color: var(--tabs-tab-color, #333);
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  color: var(--tabs-tab-color, #333);
  font: var(--tabs-tab-font, var(--font-body-1));
  padding: var(--tabs-tab-padding, 0.5rem 1.5rem);
  position: relative;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  gap: var(--tabs-icon-gap, 0.5rem);
  white-space: nowrap;
  flex-shrink: 0; // Prevent tabs from shrinking

  &--active {
    --tab-icon-color: var(--tabs-tab-active-color, #4A5568);
    color: var(--tabs-tab-active-color, #4A5568);
    font-weight: var(--tabs-tab-active-font-weight, 600);
  }

  &--disabled {
    color: var(--tabs-tab-disabled-color, #aaa);
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.icon-top {
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  &.icon-bottom {
    flex-direction: column-reverse;
    align-items: center;
    gap: 0.25rem;
  }
}

.ava-tabs__list.awe-tabs--highlight-active .ava-tabs__tab--active {
  color: var(--tabs-tab-active-color, #4A5568);
  font-weight: var(--tabs-tab-active-font-weight, 600);
}

.ava-tabs__icon {
  font-size: var(--tabs-icon-size, 1.5rem);
  display: inline-flex;
  align-items: center;  
}

.ava-tabs__underline {
  position: absolute;
  left: 0;
  bottom: 0;
  height: var(--tabs-underline-height, 2px);
  background: var(--tabs-underline-color, #4A5568);
  border-radius: 1px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, width;
  opacity: 1;
}

.ava-tabs__content {
  margin-top: var(--tabs-content-margin, 2rem);
  font: var(--tabs-content-font, var(--font-body-1));
  color: var(--tabs-content-color, var(--color-text-primary));
}

.awe-tabs__container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.awe-tabs__scroll-area {
  position: relative;
  width: 100%;
}

.awe-tabs__scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  background: var(--tabs-scroll-btn-bg, rgba(255, 255, 255, 0.85));
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--tabs-scroll-btn-shadow, 0 2px 8px rgba(0,0,0,0.10));
  transition: background 0.2s, box-shadow 0.2s, opacity 0.2s;
  opacity: 0.85;

  &:hover:not(:disabled) {
    background: var(--tabs-scroll-btn-hover-bg, #f1f5f9);
    box-shadow: var(--tabs-scroll-btn-hover-shadow, 0 4px 12px rgba(0,0,0,0.14));
    opacity: 1;
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: var(--tabs-scroll-btn-disabled-bg, rgba(255,255,255,0.7));
  }

  &--left {
    left: -22px;
  }

  &--right {
    right: -22px;
  }

  lucide-icon {
    width: 24px;
    height: 24px;
    color: var(--tabs-scroll-btn-icon-color, #64748b);
  }
}

.ava-tabs__icon-top, .ava-tabs__icon-bottom {
  display: flex;
  justify-content: center;
  width: 100%;
}

.ava-tabs__tab--button-variant {
  border-radius: var(--tabs-button-radius, 6px);
  border: 1.5px solid var(--tabs-button-border, transparent);
  background: var(--tabs-button-bg, transparent);
  color: var(--tabs-tab-color, #333);
  font-weight: 500;
  transition: background 0.2s, color 0.2s, border 0.2s, box-shadow 0.2s;
  box-shadow: var(--tabs-button-shadow, none);
  margin: 0 2px;
  padding: 0.5rem 1.5rem;

  &.ava-tabs__tab--active {
    background: var(--tabs-button-active-bg, #fff);
    color: var(--tabs-button-active-color, #222);
    border: 1.5px solid var(--tabs-button-active-border, #ff2d55);
    box-shadow: var(--tabs-button-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, 0.08));
    font-weight: 600;
    z-index: 1;
  }

  &:not(.ava-tabs__tab--active) {
    background: var(--tabs-button-bg, transparent);
    color: var(--tabs-button-inactive-color, #222);
    border: 1.5px solid var(--tabs-button-border, transparent);
    box-shadow: var(--tabs-button-shadow, none);
    font-weight: 500;
    opacity: 0.85;
  }

  &:hover:not(:disabled):not(.ava-tabs__tab--active) {
    background: var(--tabs-button-hover-bg, #f8f9fb);
    border: 1.5px solid var(--tabs-button-hover-border, #e5e7eb);
    color: var(--tabs-button-hover-color, #222);
    opacity: 1;
  }

  &.ava-tabs__tab--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--tabs-button-disabled-bg, transparent);
    border: 1.5px solid var(--tabs-button-disabled-border, #e5e7eb);
    color: var(--tabs-button-disabled-color, #aaa);
  }

  // Support icon-top/bottom
  &.icon-top, &.icon-bottom {
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }
}

.ava-tabs__tab--icon-variant {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  max-width: 40px;
  max-height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--tabs-icon-radius, 8px);
  background: var(--tabs-icon-bg, transparent);
  border: 1.5px solid var(--tabs-icon-border, transparent);
  transition: background 0.2s, border 0.2s, box-shadow 0.2s;
  box-shadow: var(--tabs-icon-shadow, none);
  font-size: 1.5rem;
  margin: 0 2px;

  &.ava-tabs__tab--active {
    background: var(--tabs-icon-active-bg, #fff);
    border: 1.5px solid var(--tabs-icon-active-border, #ff2d55);
    box-shadow: var(--tabs-icon-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, 0.08));
  }

  &:not(.ava-tabs__tab--active) {
    background: var(--tabs-icon-bg, transparent);
    border: 1.5px solid var(--tabs-icon-border, transparent);
    opacity: 0.85;
  }

  &:hover:not(:disabled):not(.ava-tabs__tab--active) {
    background: var(--tabs-icon-hover-bg, #f8f9fb);
    border: 1.5px solid var(--tabs-icon-hover-border, #e5e7eb);
    opacity: 1;
  }

  &.ava-tabs__tab--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--tabs-icon-disabled-bg, transparent);
    border: 1.5px solid var(--tabs-icon-disabled-border, #e5e7eb);
  }

  .ava-tabs__label {
    display: none !important;
  }
}

.ava-tabs__tab--has-dropdown {
  position: relative;
}
.ava-tabs__dropdown-arrow {
  margin-left: 0.25em;
  font-size: 0.85em;
  vertical-align: middle;
  color: var(--tabs-dropdown-arrow-color, #888);
  transition: color 0.2s;
}
.ava-tabs__tab--active .ava-tabs__dropdown-arrow {
  color: var(--tabs-dropdown-arrow-active-color, #ff2d55);
}
.ava-tabs__dropdown-menu {
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%) translateY(8px);
  min-width: 160px;
  background: var(--tabs-dropdown-bg, #fff);
  border-radius: var(--tabs-dropdown-radius, 8px);
  box-shadow: var(--tabs-dropdown-shadow, 0 8px 24px 0 rgba(0,0,0,0.10));
  padding: 0.5rem 0;
  z-index: 9999;
  opacity: 1;
  pointer-events: auto;
  animation: dropdown-fade-in 0.18s cubic-bezier(0.4,0,0.2,1);
}
@keyframes dropdown-fade-in {
  from { opacity: 0; transform: translateX(-50%) translateY(0); }
  to { opacity: 1; transform: translateX(-50%) translateY(8px); }
}
.ava-tabs__dropdown-item {
  width: 100%;
  background: var(--tabs-dropdown-item-bg, none);
  border: none;
  outline: none;
  color: var(--tabs-dropdown-item-color, #222);
  font: inherit;
  padding: 0.5rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  border-radius: var(--tabs-dropdown-item-radius, 4px);
}
.ava-tabs__dropdown-item:hover,
.ava-tabs__dropdown-item:focus {
  background: var(--tabs-dropdown-item-hover-bg, #f8f9fb);
  color: var(--tabs-dropdown-item-hover-color, #ff2d55);
}

// Container wrapper for optional tabs container
.ava-tabs__container-wrapper {
  display: block;
  width: 100%;
  // No background, border, or padding by default
  // Add theme styles here if desired
}

.ava-tabs__dropdown-chevron {
  display: inline-block;
  margin-left: 0.5em;
  font-size: 1em;
  vertical-align: middle;
  color: inherit;
  opacity: 0.7;
  transition: transform 0.2s;
} 