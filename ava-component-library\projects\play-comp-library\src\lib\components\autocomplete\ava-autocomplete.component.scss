// Root
.ava-autocomplete {
  position: relative;
  width: 100%;
  &--full-width {
    width: 100%;
  }
}

.ava-autocomplete__dropdown {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  background: var(--autocomplete-background, var(--textbox-background));
  border: var(--autocomplete-border-width, 1px) solid var(--autocomplete-border-color, var(--textbox-border-color));
  border-radius: var(--autocomplete-border-radius, var(--textbox-border-radius));
  box-shadow: var(--autocomplete-shadow, 0 4px 24px rgba(0,0,0,0.08));
  margin-top: 0.25rem;
  max-height: 320px;
  overflow-y: auto;
  padding: 0.25rem 0;
}

.ava-autocomplete__option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  color: var(--autocomplete-option-color, var(--textbox-input-color));
  background: transparent;
  border: none;
  font: inherit;
  transition: background 0.15s, color 0.15s;
  outline: none;

  &:hover,
  &--highlighted {
    background: var(--autocomplete-option-hover-bg, var(--textbox-background-hover, #f3f3f3));
    color: var(--autocomplete-option-hover-color, var(--textbox-label-color));
  }
}

.ava-autocomplete__option-icon {
  display: flex;
  align-items: center;
  color: var(--autocomplete-option-icon-color, var(--textbox-icon-color));
}

.ava-autocomplete__option-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ava-autocomplete__loading,
.ava-autocomplete__empty {
  padding: 1rem;
  color: var(--autocomplete-empty-color, var(--textbox-helper-color));
  text-align: center;
  font-size: 1rem;
}

.ava-autocomplete__chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.5rem 0 0.25rem 0;
}
.ava-autocomplete__chip {
  display: flex;
  align-items: center;
  background: var(--autocomplete-chip-bg, var(--textbox-background-readonly));
  color: var(--autocomplete-chip-color, var(--textbox-label-color));
  border-radius: 1rem;
  padding: 0.25rem 0.75rem 0.25rem 0.75rem;
  font-size: 0.95rem;
  gap: 0.5rem;
}
.ava-autocomplete__chip-remove {
  background: none;
  border: none;
  color: var(--autocomplete-chip-remove-color, var(--textbox-error-color));
  font-size: 1.1em;
  cursor: pointer;
  margin-left: 0.25rem;
  padding: 0;
  line-height: 1;
} 

.ava-autocomplete__options-ul {
  padding: 0;
  margin: 0;
  list-style: none;
}