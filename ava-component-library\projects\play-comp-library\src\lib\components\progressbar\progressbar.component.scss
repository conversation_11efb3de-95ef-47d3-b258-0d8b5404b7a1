// 🔹 Circular Progress
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 100%;

  svg {
    width: var( --progress-percentage-line-height);
    height: var( --progress-percentage-weight);
    max-width: 100%;
  }

  .progress-background {
    stroke: var(--progress-circular-background);
  }

  .progress-bar {
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: center;
    transition: var(--progress-transition);
    stroke: var(--progress-circular-fill-stroke);

    &.indeterminate {
      animation:var(--progress-indeterminate-animation);
    }
  }

  .progress-text {
    fill: var(--progress-text-fill);
    font-weight: var(--progress-text-weight);
    color:var(--progress-text-color);
    font-size:var(--progress-text-font);
  }

  .progress-label {
    font-size: var(--progress-label-font);
    color: var(--progress-label-color);
    line-height: var(--progress-label-line-height);
    font-weight: var( --progress-label-weight);
    margin-bottom: 10px;
    padding:20px;
  }
}

// 🔹 Linear Progress
.linear-progress-container {
  width: 100%;
  max-width: 90vw;
  display: flex;
  flex-direction: column;

  .progress-label {
    font-size: var(-progress-label-font);
    color: var(--progress-label-color);
    font-weight: var(--progress-label-weight);
    padding-bottom:20px;
  }

  .progress-percentage {
    font-size: var(--progress-percentage-font);
    color: var(--progress-percentage-color);
    font-weight: var(--progress-percentage-weight);
   
  }

  .linear-bar {
    width: 100%;
    height: var(--progress-linear-height);
    background: var(--progress-linear-background);
    border-radius: var(--progress-linear-border-radius);
    overflow: hidden;
    position: relative;


    .linear-progress {
      height: 100%;
      border-radius: inherit;
      transition: var(--progress-transition);
      background: var(--progress-linear-background);
    }

    .buffer-bar {
      height: 100%;
      background: var(--progress-linear-background);
      transition: var(--progress-transition);
    }

    .indeterminate-bar {
      width: 100%;
      height: 100%;
      position: absolute;
      overflow: hidden;

      &::before {
        content: '';
        display: block;
        width: 30%;
        height: 100%;
        background: var(--progress-linear-background);
        position: absolute;
        left: -30%;
        animation:var(--progress-indeterminate-animation);
      }
    }
  }
}

// 🔹 Animations
@keyframes indeterminate-move {
  0% {
    left: -30%;
    width: 30%;
  }
  50% {
    left: 50%;
    width: 60%;
  }
  100% {
    left: 100%;
    width: 30%;
  }
}

@keyframes spin {
  0% {
    stroke-dashoffset: 100;
  }
  50% {
    stroke-dashoffset: 50;
  }
  100% {
    stroke-dashoffset: 100;
  }
}

// // 🔹 Responsive Adjustments
// @media (max-width: 768px) {
//   .progress-container {
//     max-width: 90%;
//   }

//   .linear-bar {
//     height: var(--tooltip-padding);
//   }

//   .progress-text {
//     font-size: var(--toggle-font-size);
//   }

//   svg {
//     width: var(--prompt-enhance-width);
//     height: var(--prompt-enhance-width);
//   }
// }

// @media (max-width: 480px) {
//   .progress-container {
//     max-width: 100%;
//   }

//   .linear-bar {
//     height: var(--prompt-bar-icon-only-right);
//   }

//   .progress-text {
//     font-size: var(--toggle-width-thumb);
//   }

//   svg {
//     width: var(--step-circle-size);
//     height: var(--step-circle-size);
//   }
// }

// // 🔹 Error Message
// .progress-error {
//   color: var(--auth-error-message-color);
//   font-size: var(--backdrop-blur);
//   margin-top: var(--border-radius-small);
//   text-align: center;
// }