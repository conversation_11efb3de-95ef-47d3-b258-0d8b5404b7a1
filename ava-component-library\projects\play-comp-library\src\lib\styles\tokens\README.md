# Play+ Design System: Tokens Documentation

## Overview

The `tokens/` folder contains the foundational design tokens for the Play+ Design System. This folder implements a hierarchical token system that follows industry best practices and the design token manifesto.

## Architecture

The token system is organized in three distinct layers, each building upon the previous:

```
tokens/
├── _base.css          # Foundation layer (Global + Semantic tokens)
├── _index.css         # Main entry point
├── components/        # Component-specific semantic tokens
│   ├── _index.css
│   ├── _button.css
│   ├── _input.css
│   ├── _typography.css
│   └── ... (other components)
└── themes/           # Theme overrides
    ├── _light.css
    └── _dark.css
```

## Token Hierarchy

### 1. Base Tokens (`_base.css`)
The foundational layer containing two types of tokens:

#### Global Tokens (Raw Palette)
- **Purpose**: Context-agnostic primitive values
- **Naming**: `--global-*`
- **Usage**: Never used directly in components
- **Examples**:
  - `--global-color-pink-500: #e91e63`
  - `--global-font-size-md: 1rem`
  - `--global-spacing-4: 1rem`

#### Semantic Tokens (Design Decisions)
- **Purpose**: Contextual design decisions that give purpose to global tokens
- **Naming**: `--color-*`, `--font-*`, `--motion-*`, etc.
- **Usage**: Consumed by components and themes
- **Examples**:
  - `--color-brand-primary: var(--global-color-pink-500)`
  - `--font-body-1: var(--global-font-weight-regular) var(--global-font-size-md) / var(--global-line-height-normal) var(--font-family-body)`

### 2. Component Tokens (`components/`)
Component-specific semantic tokens that build upon base tokens:

- **Purpose**: Component-specific design decisions
- **Naming**: `--component-*` (e.g., `--button-*`, `--input-*`)
- **Usage**: Used directly in component styles
- **Examples**:
  - `--button-primary-background: var(--color-surface-interactive-default)`
  - `--input-border-color: var(--color-border-default)`

### 3. Theme Tokens (`themes/`)
Theme-specific overrides that modify semantic tokens:

- **Purpose**: Theme variations (light, dark, etc.)
- **Naming**: Same as semantic tokens but with different values
- **Usage**: Applied via CSS classes or data attributes
- **Examples**:
  - `[data-theme="dark"] { --color-background-primary: var(--global-color-gray-900); }`

## File Structure

### Core Files

#### `_index.css`
Main entry point that imports all tokens in the correct order:
1. Base tokens (foundation)
2. Component tokens (semantic layer)
3. Theme tokens (overrides)

#### `_base.css`
Contains the foundational token system:

**Global Palette:**
- Base brand colors (pink, purple)
- Extended colors for theming (violet, cyan, etc.)
- Neutral colors (grays)
- Feedback colors (success, error, warning)

**Typography:**
- Font families (display, heading, body)
- Font weights (regular, medium, semibold, bold)
- Font sizes (xs to xxxl)
- Line heights (none to extra-loose)
- Letter spacing

**Spacing & Layout:**
- Spacing scale (0 to 15, negative values)
- Border radius (sm to 2xl, special values)
- Grid system (columns, gutters, margins)

**Elevation & Motion:**
- Shadow system (01 to 05)
- Motion durations and easing
- Glassmorphic effects

**Semantic Tokens:**
- Color system (brand, text, background, surface, border)
- Typography system (font stacks)
- Motion patterns (fade, slide, pop)
- Layout tokens
- Accessibility tokens

### Component Tokens (`components/`)

Each component has its own token file:

- `_button.css` - Button component tokens
- `_input.css` - Input component tokens
- `_typography.css` - Typography component tokens
- `_badge.css` - Badge component tokens
- `_spinner.css` - Spinner component tokens
- `_progress.css` - Progress component tokens
- `_tooltip.css` - Tooltip component tokens
- `_slider.css` - Slider component tokens
- `_radio.css` - Radio component tokens
- `_checkbox.css` - Checkbox component tokens
- `_toast.css` - Toast component tokens
- `_dropdown.css` - Dropdown component tokens

### Theme Tokens (`themes/`)

- `_light.css` - Light theme overrides
- `_dark.css` - Dark theme overrides

## Usage Guidelines

### For Designers

1. **Global Tokens**: Use as the raw palette for design decisions
2. **Semantic Tokens**: Reference in design specifications
3. **Component Tokens**: Use for component-specific styling
4. **Theme Tokens**: Use for theme variations

### For Developers

#### Importing Tokens
```css
/* Import the entire token system */
@import "./tokens/_index.css";

/* Or import specific layers */
@import "./tokens/_base.css";
@import "./tokens/components/_button.css";
```

#### Using Tokens in Components
```css
.my-button {
  background-color: var(--button-primary-background);
  color: var(--button-primary-text);
  padding: var(--global-spacing-3) var(--global-spacing-4);
  border-radius: var(--global-radius-md);
  font: var(--font-body-1);
}
```

#### Theme Switching
```css
/* Apply theme via data attribute */
[data-theme="dark"] {
  /* Dark theme tokens are automatically applied */
}

/* Or via class */
.theme-dark {
  /* Dark theme tokens are automatically applied */
}
```

### Token Naming Conventions

1. **Global Tokens**: `--global-{category}-{property}-{value}`
   - Example: `--global-color-pink-500`

2. **Semantic Tokens**: `--{category}-{property}-{variant}`
   - Example: `--color-brand-primary`

3. **Component Tokens**: `--{component}-{property}-{variant}`
   - Example: `--button-primary-background`

## Best Practices

### 1. Token Hierarchy
- Always use semantic tokens in components, never global tokens
- Build component tokens on top of semantic tokens
- Use theme tokens for variations

### 2. Naming
- Use kebab-case for all token names
- Be descriptive but concise
- Follow the established naming patterns

### 3. Values
- Use rem units for typography and spacing
- Use hex values for colors
- Use descriptive values for special cases (e.g., `9999px` for pill radius)

### 4. Organization
- Keep related tokens together
- Use clear section comments
- Maintain consistent ordering

### 5. Documentation
- Document complex token relationships
- Explain the purpose of each token category
- Provide usage examples

## Migration Guide

### From Legacy SCSS Variables
1. Replace `$variable-name` with `var(--token-name)`
2. Update import statements to use CSS files
3. Replace SCSS functions with CSS custom properties
4. Update theme switching logic

### From Direct Values
1. Identify the semantic purpose of the value
2. Find or create the appropriate token
3. Replace the direct value with the token reference
4. Update any related values to maintain consistency

## Maintenance

### Adding New Tokens
1. Determine the appropriate layer (global, semantic, component, theme)
2. Follow the naming conventions
3. Add to the appropriate file
4. Update documentation if needed

### Modifying Existing Tokens
1. Consider the impact on all dependent tokens
2. Test across all themes
3. Update component styles if necessary
4. Document the change

### Removing Tokens
1. Check for usage across the codebase
2. Remove from all dependent tokens
3. Update component styles
4. Clean up documentation

## Troubleshooting

### Common Issues

1. **Token Not Found**: Check the import order and file paths
2. **Theme Not Applying**: Verify the theme class or data attribute is set
3. **Inconsistent Values**: Ensure tokens are properly cascading
4. **Performance Issues**: Check for circular dependencies

### Debugging
```css
/* Add this to debug token values */
* {
  outline: 1px solid red;
}

/* Or use browser dev tools to inspect computed values */
```

## Resources

- [Design Token Manifesto](https://www.designtokens.org/)
- [CSS Custom Properties Guide](https://developer.mozilla.org/en-US/docs/Web/CSS/Using_CSS_custom_properties)
- [Design System Best Practices](https://www.designsystem.digital.gov/)

---

*This documentation is maintained as part of the Play+ Design System. For questions or contributions, please refer to the project guidelines.* 