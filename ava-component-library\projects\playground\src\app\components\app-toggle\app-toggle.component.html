<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Toggle Component</h1>
        <p class="description">
          A flexible toggle component for creating on/off switches with configurable options.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ToggleComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <ava-toggle
                size="medium"
                title="Enable Notifications"
                position="left"
               >
              </ava-toggle>
            </ng-container>

            <ng-container *ngSwitchCase="'Toggle Sizes'">
              <ava-toggle
                size="small"
                title="Small Toggle"
                position="left"
                [checked]="true"
                [animation]="true">
              </ava-toggle>

              <ava-toggle
                size="medium"
                title="Medium Toggle"
                position="left"
                [checked]="mediumToggleEnabled"
                [animation]="true"
                (checkedChange)="onMediumToggle($event)">
              </ava-toggle>

              <ava-toggle
                size="large"
                title="Large Toggle"
                position="left"
                [checked]="false"
                [animation]="true">
              </ava-toggle>
            </ng-container>

            <ng-container *ngSwitchCase="'Disabled State'">
              <ava-toggle
                size="medium"
                title="Disabled Toggle"
                position="left"
                [checked]="false"
                [disabled]="true"
                [animation]="true">
              </ava-toggle>
            </ng-container>

           

            <ng-container *ngSwitchCase="'Title positions in Toggle'">
              <ava-toggle
                size="medium"
                title="Enable Notifications"
                position="left"
                [checked]="notificationsEnabled"
                [animation]="true"
                (checkedChange)="onNotificationToggle($event)">
              </ava-toggle>

              <ava-toggle
                size="medium"
                title="Dark Mode"
                position="right"
                [checked]="darkModeEnabled"
                [animation]="true"
                (checkedChange)="onDarkModeToggle($event)">
              </ava-toggle>
            </ng-container>

            <ng-container *ngSwitchCase="'Events Usage'">
              <ava-toggle
                size="medium"
                title="Toggle with Event"
                position="left"
                [checked]="eventToggleEnabled"
                [animation]="true"
                (checkedChange)="onEventToggle($event)">
              </ava-toggle>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- Icon for copy button -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td><code>{{ event.name }}</code></td>
          <td><code>{{ event.type }}</code></td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
