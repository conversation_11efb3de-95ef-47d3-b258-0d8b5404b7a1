<!-- TYPE: basic -->
<ng-container *ngIf="type === 'basic'">
  <div class="pagination-container">
    <button (click)="prevPage()" [disabled]="currentPage === 1" class="nav-btn">
      ← Previous
    </button>

    <ng-container *ngFor="let page of pages">
      <button
        *ngIf="page !== '...'; else dots"
        (click)="goToPage(page)"
        [ngClass]="{ active: page === currentPage }"
        class="page-btn"
      >
        {{ page }}
      </button>
      <ng-template #dots>
        <span class="dots">...</span>
      </ng-template>
    </ng-container>

    <button
      (click)="nextPage()"
      [disabled]="currentPage === totalPages"
      class="nav-btn"
    >
      Next →
    </button>
  </div>
</ng-container>

<!-- TYPE: extended -->
<ng-container *ngIf="type === 'extended'">
  <div class="pagination-container">
    <button (click)="prevPage()" [disabled]="currentPage === 1" class="nav-btn">
      ← Previous
    </button>

    <ng-container *ngFor="let page of pages; let i = index">
      <ng-container *ngIf="shouldShow(page); else maybeDots">
        <button
          (click)="goToPage(page)"
          [ngClass]="{ active: page === currentPage }"
          class="page-btn"
        >
          {{ page }}
        </button>
      </ng-container>
      <ng-template #maybeDots>
        <ng-container *ngIf="shouldInsertDots(i)">
          <span class="dots">...</span>
        </ng-container>
      </ng-template>
    </ng-container>

    <button
      (click)="nextPage()"
      [disabled]="currentPage === totalPages"
      class="nav-btn"
    >
      Next →
    </button>
  </div>
</ng-container>

<!-- TYPE: standard -->
<ng-container *ngIf="type === 'standard'">
  <div class="pagination-container standard">
    <!-- Left: Previous -->
    <div class="nav-left">
      <button
        (click)="prevPage()"
        [disabled]="currentPage === 1"
        class="nav-btn"
      >
        ← Previous
      </button>
    </div>
    <!-- Center: Page numbers -->
    <div class="pages">
      <ng-container *ngFor="let page of pages">
        <button
          *ngIf="page !== '...'; else dots"
          (click)="goToPage(page)"
          [ngClass]="{ active: page === currentPage }"
          class="page-btn"
        >
          {{ page }}
        </button>
        <ng-template #dots>
          <span class="dots">...</span>
        </ng-template>
      </ng-container>
    </div>

    <!-- Right: Next -->
    <div class="nav-right">
      <button
        (click)="nextPage()"
        [disabled]="currentPage === totalPages"
        class="nav-btn"
      >
        Next →
      </button>
    </div>
  </div>
</ng-container>

<!-- TYPE: pageinfo -->
<ng-container *ngIf="type === 'pageinfo'">
  <div class="pagination-container page">
    <button class="nav-btn" (click)="prevPage()" [disabled]="currentPage === 1">
      ← Previous
    </button>

    <div class="page-label">Page {{ currentPage }} of {{ totalPages }}</div>

    <button
      class="nav-btn"
      (click)="nextPage()"
      [disabled]="currentPage === totalPages"
    >
      Next →
    </button>
  </div>
</ng-container>

<!-- TYPE: simplepageinfo -->
<ng-container *ngIf="type === 'simplepageinfo'">
  <div class="pagination-container simplepage">
    <button
      class="icon-btn"
      (click)="prevPage()"
      [disabled]="currentPage === 1"
      aria-label="Previous page"
    >
      ←
    </button>

    <div class="page-label">Page {{ currentPage }} of {{ totalPages }}</div>

    <button
      class="icon-btn"
      (click)="nextPage()"
      [disabled]="currentPage === totalPages"
      aria-label="Next page"
    >
      →
    </button>
  </div>
</ng-container>
