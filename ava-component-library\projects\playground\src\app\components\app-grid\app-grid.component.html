<div class="documentation">
  <!-- Header -->
  <header class="doc-header">
    <h1>Grid Component</h1>
    <p class="description">
      A 12-column grid system for creating responsive layouts
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} GridComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">

            <!-- Container -->
            <ng-container *ngSwitchCase="'Container'">
              <div class="container demo-container">
                <div class="demo-content">Fixed-width container</div>
            </div>
            </ng-container>

            <!--Container Fluid -->
            <ng-container *ngSwitchCase="'Container Fluid'">
                <div class="container-fluid demo-container">
                    <div class="demo-content">Full-width container</div>
                </div>
            </ng-container>

            <!-- Column System -->
            <ng-container *ngSwitchCase="'Column System'">
            <h4>Full Width (col-12)</h4>
            <div class="container">
                <div class="row">
                    <div class="col-12 demo-col">col-12</div>
                </div>
            </div>

            <h4>Two Equal Columns (col-6)</h4>
            <div class="container">
                <div class="row">
                    <div class="col-6 demo-col">col-6</div>
                    <div class="col-6 demo-col">col-6</div>
                </div>
            </div>

            <h4>Three Equal Columns (col-4)</h4>
            <div class="container">
                <div class="row">
                    <div class="col-4 demo-col">col-4</div>
                    <div class="col-4 demo-col">col-4</div>
                    <div class="col-4 demo-col">col-4</div>
                </div>
            </div>

            <h4>Four Equal Columns (col-3)</h4>
            <div class="container">
                <div class="row">
                    <div class="col-3 demo-col">col-3</div>
                    <div class="col-3 demo-col">col-3</div>
                    <div class="col-3 demo-col">col-3</div>
                    <div class="col-3 demo-col">col-3</div>
                </div>
            </div>
            </ng-container>
            
            <!--Responsive Grid Classes -->
            <ng-container *ngSwitchCase="'Responsive Grid Classes'">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-6 demo-col">
                        Full width on mobile
                        <br>Half width on small
                        <br>One-third on medium
                    </div>
                    <div class="col-12 col-sm-12 col-md-6 demo-col">
                        Full width on mobile and small
                        <br>One-third on medium
                    </div>
                </div>
            </div>
            </ng-container>

            <!-- Column Offsets -->
            <ng-container *ngSwitchCase="'Column Offsets'">
              <div class="container">
                <div class="row">
                    <div class="col-5 offset-4 demo-col">col-5 offset-4</div>
                </div>
                <div class="row mt-3">
                    <div class="col-3 offset-3 demo-col">col-3 offset-3</div>
                    <div class="col-3 offset-1 demo-col">col-3 offset-3</div>
                </div>
            </div>
            </ng-container>

            <!-- No Gutters -->
            <ng-container *ngSwitchCase="'No Gutters'">
            <h4>With Gutters (Default)</h4>
            <div class="container">
                <div class="row">
                    <div class="col-6 demo-col">With gutter</div>
                    <div class="col-6 demo-col">With gutter</div>
                </div>
            </div>

            <h4>Without Gutters</h4>
            <div class="container">
                <div class="row no-gutters">
                    <div class="col-6 demo-col">No gutter</div>
                    <div class="col-6 demo-col">No gutter</div>
                </div>
            </div>
            </ng-container>

            <!-- Flexbox Utilities -->
            <ng-container *ngSwitchCase="'Flexbox Utilities'">
            <h4>Flex Direction Column</h4>
            <div class="container">
                <div class="row flex-column">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction Row</h4>
            <div class="container">
                <div class="row flex-row">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction flex-row-reverse</h4>
            <div class="container">
                <div class="row flex-row-reverse" >
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Flex Direction flex-column-reverse</h4>
            <div class="container">
                <div class="row flex-column-reverse">
                    <div class="col demo-col">Column 1</div>
                    <div class="col demo-col">Column 2</div>
                    <div class="col demo-col">Column 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-start</h4>
            <div class="container">
                <div class="row justify-content-start">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-end</h4>
            <div class="container">
                <div class="row justify-content-end">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-center</h4>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-around</h4>
            <div class="container">
                <div class="row justify-content-around">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Justify Content justify-content-between</h4>
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-2 demo-col">Item 1</div>
                    <div class="col-2 demo-col">Item 2</div>
                    <div class="col-2 demo-col">Item 3</div>
                </div>
            </div>

            <h4>Flex Wrap</h4>
            <div class="container">
                <div class="row flex-wrap">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                    
                </div>
            </div>

            <h4>No-Wrap</h4>
            <div class="container">
                <div class="row flex-nowrap">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                </div>
            </div>

            <h4> Flex Wrap reverse</h4>
            <div class="container">
                <div class="row flex-wrap-reverse">
                    <div class="col-3 demo-col">Item 1</div>
                    <div class="col-3 demo-col">Item 2 </div>
                    <div class="col-3 demo-col">Item 3</div>
                    <div class="col-3 demo-col">Item 4 </div>
                    <div class="col-3 demo-col">Item 5</div>
                    <div class="col-3 demo-col">Item 6 </div>
                    <div class="col-3 demo-col">Item 7</div>
                    <div class="col-3 demo-col">Item 8</div>
                </div>
            </div>

            <h4>Align Item Start</h4>
            <div class="container content-border">
                <div class="row align-items-start" >
                    <div class="col-4 demo-col-flex ">Item 1</div>
                    <div class="col-4 demo-col-flex ">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>
            <h4>Align Item Center</h4>
            <div class="container content-border">
                <div class="row align-items-center" >
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item End</h4>
            <div class="container content-border">
                <div class="row align-items-end" >
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item Baseline</h4>
            <div class="container content-border">
                <div class="row align-items-baseline">
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Item Stretch</h4>
            <div class="container">
                <div class="row align-items-stretch">
                    <div class="col-4 demo-col-flex">Item 1</div>
                    <div class="col-4 demo-col-flex">Item 2 </div>
                    <div class="col-4 demo-col-flex">Item 3</div>
                </div>
            </div>

            <h4>Align Content Example</h4>
            <div class="container">
                <div class="row" >
                    <div class="col-4 demo-col-flex align-content-start">align-content-start</div>
                    <div class="col-4 demo-col-flex align-content-center">align-content-center </div>
                    <div class="col-4 demo-col-flex align-content-end">align-content-end</div>
                </div>
            </div>

            <h4>Align Self Example</h4>
            <div class="container content-border">
                <div class="row self-align" >
                    <div class="col demo-col align-self-start">Align Self Start Item</div>
                    <div class="col demo-col align-self-center">Align Self Center Item</div>
                    <div class="col demo-col align-self-end">Align Self End Item</div>
                </div>                
            </div>

            </ng-container>

            <!-- Ordering Classes -->
            <ng-container *ngSwitchCase="'Ordering Classes'">
              <div class="container">
                <div class="row">
                    <div class="col order-3 demo-col">First in code, but third in display</div>
                    <div class="col order-1 demo-col">Second in code, but first in display</div>
                    <div class="col order-2 demo-col">Third in code, but second in display</div>
                </div>
            </div>
            </ng-container>

            <!-- Push and Pull Classes -->
            <ng-container *ngSwitchCase="'Push and Pull Classes'">
                <div class="container">
                    <div class="row">
                        <div class="col-9 push-3 demo-col">Column 1 (pushed right by 3)</div>
                        <div class="col-3 pull-9 demo-col">Column 2 (pulled left by 9)</div>
                    </div>
                </div>
            </ng-container>
          
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>
        
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

</div>