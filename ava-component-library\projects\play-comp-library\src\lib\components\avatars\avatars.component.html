<div class="avatar-container">
  <div class="avatar-wrapper">
    <div
      [class]="avatarClasses"
      [style.background-image]="imageUrl ? 'url(' + imageUrl + ')' : 'none'"
    >
      <ava-badges
        *ngIf="hasBadge"
        [state]="badgeState!"
        [size]="badgeSize!"
        [count]="badgeCount"
        class="avatar-badge"
      >
      </ava-badges>
    </div>
    <!-- Text labels - can have both status and profile -->
    <div *ngIf="hasAnyText" class="avatar-text-container">
      <div *ngIf="hasStatusText" class="avatar-text avatar-text--status">
        {{ statusText }}
      </div>
      <div *ngIf="hasProfileText" class="avatar-text avatar-text--profile">
        {{ profileText }}
      </div>
    </div>
  </div>
</div>
