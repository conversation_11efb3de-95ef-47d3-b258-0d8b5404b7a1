import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '../../components/icon/icon.component';
import { ButtonComponent } from '../../components/button/button.component';

@Component({
  selector: 'ava-popup',
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent, ButtonComponent],
  templateUrl: './popup.component.html',
  styleUrl: './popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PopupComponent {
  // Controls popup visibility
  @Input() show = false;

  // Message ALignment 
  @Input() messageAlignment: 'left' | 'center' | 'right' = 'center';

  // Popup Position
  @Input() position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top' | 'bottom' = 'center';

  // Title and message content
  @Input() title = 'SUCCESS';
  @Input() message = 'Action completed successfully.';
  @Input() showTitle = true;

  // Header icon configuration
  @Input() showHeaderIcon = true;
  @Input() headerIconName = 'circle-check';
  @Input() iconColor = 'green';
  @Input() iconSize = 70;

  // Close button (top-right corner) configuration
  @Input() showClose = true;
  @Input() closeIconName = 'x';
  @Input() closeIconColor = '#a2a2a2';
  @Input() closeIconSize = 24;

  // Inline message (icon + text row below title)
  @Input() showInlineMessage = false;
  @Input() inlineIconName = 'badge-check';
  @Input() inlineIconSize = 40;
  @Input() inlineIconColor = 'green';
  @Input() inlineMessage = '';

  // Popup width input — accepts string or number (converted to px)
  private _popupWidth: string = '400px';

  @Input()
  set popupWidth(value: string | number) {
    this._popupWidth = typeof value === 'number' ? `${value}px` : value;
  }
  get popupWidth(): string {
    return this._popupWidth;
  }

  // Button visibility controls
  @Input() showConfirm = false;
  @Input() showCancel = false;

  // Cancel button configuration
  @Input() cancelButtonLabel = 'Cancel';
  @Input() cancelButtonSize: 'small' | 'medium' | 'large' = 'small';
  @Input() cancelButtonVariant: 'primary' | 'secondary' = 'secondary';
  @Input() cancelButtonBackground = '';

  // Confirm button configuration
  @Input() confirmButtonLabel = 'Confirm';
  @Input() confirmButtonSize: 'small' | 'medium' | 'large' = 'small';
  @Input() confirmButtonVariant: 'primary' | 'secondary' = 'primary';
  @Input() confirmButtonBackground = '';

  // Output events
  @Output() confirm = new EventEmitter<void>(); // Emit on confirm
  @Output() cancel = new EventEmitter<void>();  // Emit on cancel
  @Output() closed = new EventEmitter<void>();  // Emit when popup is closed

  // Called when confirm button is clicked
  onConfirm(): void {
    this.confirm.emit();
    this.closePopup();
  }

  // Called when cancel button is clicked
  onCancel(): void {
    this.cancel.emit();
    this.closePopup();
  }

  // Closes the popup and emits the closed event
  closePopup(): void {
    this.closed.emit();
  }

  /**
   * Splits multiline message content using <br> tags and trims each line.
   * Used in the template to support line breaks.
   */
  getMessageLines(): string[] {
    return this.message.split(/<br\s*\/?>/i).map(line => line.trim());
  }
}
