import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { TimePickerComponent } from "../../../../../play-comp-library/src/lib/components/time-picker/time-picker.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-app-timepicker',
  imports: [TimePickerComponent, IconsComponent, CommonModule],
  templateUrl: './app-timepicker.component.html',
  styleUrls: ['./app-timepicker.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppTimepickerComponent {
  @ViewChild('codeBlock') codeBlock!: ElementRef;

  sections = [
    {
      title: 'Basic Usage',
      description: 'Basic usage of the time picker component.',
      showCode: false,
    },
    {
      title: 'Custom Icon',
      description: 'Customize the icon of the time picker.',
      showCode: false,
    },
    {
      title: 'Disabled Input',
      description: 'Disable the input fields of the time picker.',
      showCode: false,
    },
    {
      title: 'Disabled Icon',
      description: 'Disable the icon of the time picker.',
      showCode: false,
    },
    {
      title: 'Present Time',
      description: 'Set the current time as the default time.',
      showCode: false,
    },
    {
      title: 'Apply and Click',
      description: 'Show OK and Cancel buttons and prevent the dropdown from closing on time selection.',
      showCode: false,
    },
    {
      title: 'Error Message',
      description: 'Set a custom error message for the time picker.',
      showCode: false,
    }
  ];

  apiProps = [
    { name: 'time', type: 'string', default: '--', description: 'The selected time in hh:mm AM/PM format.' },

    { name: 'iconName', type: 'string', default: "'awe_timer'", description: 'The name of the icon to be displayed.' },
    { name: 'inputDisabled', type: 'boolean', default: 'false', description: 'Disables the input fields if set to true.' },
    { name: 'iconDisabled', type: 'boolean', default: 'false', description: 'Disables the icon if set to true.' },
    { name: 'presentDate', type: 'boolean', default: 'false', description: 'Sets the current time as the default time if set to true.' },
    { name: 'apply', type: 'boolean', default: 'false', description: 'Shows the OK and Cancel buttons if set to true.' },
    { name: 'click', type: 'boolean', default: 'true', description: 'Closes the dropdown on time selection if set to true.' },
    { name: 'errorMessage', type: 'string', default: "''", description: 'The error message to be displayed when the input is invalid.' }
  ];

  toggleSection(index: number): void {
    this.sections.forEach((section, i) => {
      section.showCode = (i === index) ? !section.showCode : false;
    });
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }


  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (!this.codeBlock?.nativeElement) return;
    const clickedElement = event.target as HTMLElement;
    const isClickOnSectionHeader = clickedElement.closest('.section-header');
    const clickedInside = this.codeBlock.nativeElement.contains(clickedElement) || isClickOnSectionHeader;
    if (!clickedInside) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  onTimeSelected(time: string) {
    console.log('Selected time:', time);
    // Handle the selected time as needed
  }
  
  getTimePickerCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'Basic Usage': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-timepicker-example',
  standalone: true,
  imports: [TimePickerComponent],
  template: \`
    <awe-time-picker (timeSelected)="onTimeSelected($event)"></awe-time-picker>
  \`
})
export class TimepickerExampleComponent {
  onTimeSelected(time: string) {
    console.log('Selected time:', time);
    // Handle the selected time as needed
  }
}`,
      'Custom Icon': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-timepicker-example',
  standalone: true,
  imports: [TimePickerComponent],
  template: \`
<awe-time-picker [iconName]="'awe_tick'" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
  \`
})
export class TimepickerExampleComponent {
  onTimeSelected(time: string) {
    console.log('Selected time:', time);
    // Handle the selected time as needed
  }
}`,
      'Disabled Input': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker [inputDisabled]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`,
      'Disabled Icon': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker [iconDisabled]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`,
      'Present Time': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker [presentDate]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`,
      'Apply and Click': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker [apply]="true" [click]="false" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`,
      'Click to Close': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker [click]="true" (timeSelected)="onTimeSelected($event)"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`,
      'Error Message': `
import { Component } from '@angular/core';
import { TimePickerComponent } from '@awe/play-comp-library';
      
      @Component({
        selector: 'app-timepicker-example',
        standalone: true,
        imports: [TimePickerComponent],
        template: \`
          <awe-time-picker  [click]="true"  (timeSelected)="onTimeSelected($event)"
            [errorMessage]="'Invalid format.'"></awe-time-picker>
        \`
      })
      export class TimepickerExampleComponent {
        onTimeSelected(time: string) {
          console.log('Selected time:', time);
          // Handle the selected time as needed
        }
      }`
    };

    return examples[sectionTitle] || '';
  }
  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getTimePickerCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
 
}
