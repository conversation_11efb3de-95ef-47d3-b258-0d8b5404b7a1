import * as i1 from '@angular/common';
import { CommonModule, NgIf, NgFor } from '@angular/common';
import * as i0 from '@angular/core';
import { EventEmitter, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, ViewChildren, ViewChild, HostBinding, forwardRef, CUSTOM_ELEMENTS_SCHEMA, HostListener } from '@angular/core';
import * as i2 from 'lucide-angular';
import { LucideAngularModule } from 'lucide-angular';
import * as i2$1 from '@angular/forms';
import { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';
import { trigger, transition, style, animate } from '@angular/animations';

class IconComponent {
    iconName = '';
    color = '';
    disabled = false;
    iconColor = 'currentColor';
    iconSize = 24;
    cursor = false;
    userClick = new EventEmitter();
    get computedColor() {
        if (this.disabled)
            return 'var(--button-icon-color-disabled)';
        return this.iconColor;
    }
    handleClick(event) {
        if (this.disabled || !this.cursor) {
            event.preventDefault();
            return;
        }
        this.userClick.emit(event);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: IconComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: IconComponent, isStandalone: true, selector: "ava-icon", inputs: { iconName: "iconName", color: "color", disabled: "disabled", iconColor: "iconColor", iconSize: "iconSize", cursor: "cursor" }, outputs: { userClick: "userClick" }, host: { properties: { "style.height.px": "iconSize" }, styleAttribute: "display: inline-flex; align-items: center; justify-content: center; vertical-align: middle;" }, ngImport: i0, template: "<button  class=\"ava-icon-container\" [ngClass] =\"{'disabled':disabled, 'cursor':cursor}\"  (click)=\"handleClick($event)\">\r\n    <lucide-icon [name]=\"iconName\" [size] = \"iconSize\"  [color] = \"computedColor\"></lucide-icon>\r\n</button> ", styles: [".ava-icon-container{border:none;background:transparent;padding:0;margin:0}.ava-icon-container svg{vertical-align:middle}.ava-icon-container lucide-icon{align-items:center;display:flex}.ava-icon-container.cursor{cursor:pointer}.ava-icon-container.disabled{cursor:not-allowed}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "ngmodule", type: LucideAngularModule }, { kind: "component", type: i2.LucideAngularComponent, selector: "lucide-angular, lucide-icon, i-lucide, span-lucide", inputs: ["class", "name", "img", "color", "absoluteStrokeWidth", "size", "strokeWidth"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: IconComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-icon', imports: [CommonModule, LucideAngularModule], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {
                        '[style.height.px]': 'iconSize',
                        'style': 'display: inline-flex; align-items: center; justify-content: center; vertical-align: middle;'
                    }, template: "<button  class=\"ava-icon-container\" [ngClass] =\"{'disabled':disabled, 'cursor':cursor}\"  (click)=\"handleClick($event)\">\r\n    <lucide-icon [name]=\"iconName\" [size] = \"iconSize\"  [color] = \"computedColor\"></lucide-icon>\r\n</button> ", styles: [".ava-icon-container{border:none;background:transparent;padding:0;margin:0}.ava-icon-container svg{vertical-align:middle}.ava-icon-container lucide-icon{align-items:center;display:flex}.ava-icon-container.cursor{cursor:pointer}.ava-icon-container.disabled{cursor:not-allowed}\n"] }]
        }], propDecorators: { iconName: [{
                type: Input
            }], color: [{
                type: Input
            }], disabled: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], iconSize: [{
                type: Input
            }], cursor: [{
                type: Input
            }], userClick: [{
                type: Output
            }] } });

class ButtonComponent {
    label = '';
    variant = 'primary';
    size = 'normal';
    state = 'default';
    visual = 'normal';
    pill = false;
    disabled = false;
    width;
    height;
    gradient;
    background;
    color;
    dropdown = false;
    iconName = '';
    iconColor = '';
    iconSize = 20;
    iconPosition = 'left';
    userClick = new EventEmitter();
    isActive = false;
    timeoutRef;
    basicOrAdvanced = 'basic';
    ngOnInit() {
        if (this.visual === 'neo') {
            this.basicOrAdvanced = 'ava-button-advanced';
        }
        else {
            this.basicOrAdvanced = 'ava-button-basic';
        }
        this.isActive = this.state === 'active' ? true : false;
    }
    handleClick(event) {
        if (this.disabled) {
            event.preventDefault();
            return;
        }
        this.setActiveState();
        this.userClick.emit(event);
    }
    onKeydown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            if (!this.disabled) {
                this.setActiveState();
                this.userClick.emit(event);
            }
        }
    }
    setActiveState() {
        this.isActive = true;
        this.timeoutRef = setTimeout(() => {
            this.isActive = false;
        }, 200);
    }
    get hasIcon() {
        return !!this.iconName;
    }
    get computedIconColor() {
        if (this.disabled)
            return 'var(--button-icon-color-disabled)';
        if (this.iconColor && this.isValidColor(this.iconColor))
            return this.iconColor;
        if (this.variant === 'primary')
            return 'var(--button-primary-text)';
        return 'var(--button-secondary-text)';
    }
    isValidColor(value) {
        const s = new Option().style;
        s.color = value;
        return s.color !== '';
    }
    ngOnDestroy() {
        if (this.timeoutRef) {
            clearTimeout(this.timeoutRef);
        }
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ButtonComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: ButtonComponent, isStandalone: true, selector: "ava-button", inputs: { label: "label", variant: "variant", size: "size", state: "state", visual: "visual", pill: "pill", disabled: "disabled", width: "width", height: "height", gradient: "gradient", background: "background", color: "color", dropdown: "dropdown", iconName: "iconName", iconColor: "iconColor", iconSize: "iconSize", iconPosition: "iconPosition" }, outputs: { userClick: "userClick" }, ngImport: i0, template: "<button class=\"ava-button\" [class]=\"\r\n    [\r\n      basicOrAdvanced,\r\n      variant,\r\n      size,\r\n      state,\r\n      pill === true ? 'pill' : '',\r\n      state === 'active' ? 'active' : '',\r\n      isActive ? 'active-anim' : ''\r\n    ].join(' ')\r\n  \" [style.width]=\"width ? width : null\" [style.height.px]=\"height ? height : null\"\r\n  [style.background]=\"gradient || background || null\" [style.color]=\"color ? color : null\" [disabled]=\"disabled\"\r\n  [ngClass]=\"[\r\n    visual,\r\n    iconPosition === 'only' ? 'icon-only-ava-button' : '',\r\n    iconPosition === 'left' || iconPosition === 'right'\r\n      ? 'icon-lr-ava-button'\r\n      : '',\r\n    gradient ? 'gradient' : ''\r\n  ]\" (click)=\"handleClick($event)\" (keydown)=\"onKeydown($event)\">\r\n  <ng-container *ngIf=\"(iconName && iconPosition === 'left') || iconPosition === 'only'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n  <span *ngIf=\"iconPosition !== 'only'\" class=\"ava-button__label\">{{\r\n    label\r\n    }}</span>\r\n  <ng-container *ngIf=\"iconName && iconPosition === 'right'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n\r\n  <ava-icon class=\"b-dropdown\" *ngIf=\"dropdown\" iconName=\"chevron-down\" [iconColor]=\"computedIconColor\"\r\n    [iconSize]=\"30\"></ava-icon>\r\n</button>", styles: [".ava-button{display:inline-flex;position:relative;padding:var(--global-spacing-4) var(--global-spacing-5);font-size:var(--font-body-2);gap:8px;border:none;justify-content:center;align-items:center;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border-radius:var(--button-border-radius, 12px);cursor:pointer;overflow:hidden;transition:background .3s ease,transform .3s ease;z-index:0}.ava-button.primary{background:var(--button-primary-background);color:var(--button-primary-text)}.ava-button.primary.active{background:var(--button-primary-background-active)}.ava-button.primary:disabled{cursor:not-allowed;opacity:.6;pointer-events:none;color:var(--button-primary-text-disabled);background:var(--button-primary-background-disabled)}.ava-button.primary:disabled:after{display:none}.ava-button.primary:disabled:hover{transform:none}.ava-button.primary.danger{color:var(--button-danger-text);background:var(--button-danger-background)}.ava-button.primary.danger:hover{background:var(--button-danger-background-hover)}.ava-button.primary.danger.active,.ava-button.primary.danger:active{background:var(--button-danger-background-active)}.ava-button.primary.warning{color:var(--button-warning-text);background:var(--button-warning-background)}.ava-button.primary.warning:hover{background:var(--button-warning-background-hover)}.ava-button.primary.warning.active,.ava-button.primary.warning:active{background:var(--button-warning-background-active)}.ava-button.primary.pill{border-radius:50px}.ava-button.primary.glass{background:#ffffff14;backdrop-filter:var(--button-glassmorphic-blur-30);-webkit-backdrop-filter:var(--button-glassmorphic-blur-30);box-shadow:var(--button-glassmorphic-light-90);border:1px solid rgba(255,255,255,.1)}.ava-button.primary.neo{color:var(--button-primary-text);transition:all .2s ease;box-shadow:-6px -6px 12px #ffffff1a,6px 6px 12px #0000004d}.ava-button.primary.neo:hover{box-shadow:-4px -4px 8px #ffffff1f,4px 4px 8px #00000040;transform:translateY(-1px)}.ava-button.primary.neo.active,.ava-button.primary.neo:active{box-shadow:inset -4px -4px 8px #ffffff1f,inset 4px 4px 8px #0000004d;transform:translateY(1px)}.ava-button.primary.neo:focus{outline:none}.ava-button.secondary{background:var(--button-secondary-background);color:var(--button-secondary-text);border:var(--button-secondary-border)}.ava-button.secondary:hover{background:var(--button-secondary-background-hover);color:var(--button-primary-text);border:var(--button-secondary-border-hover)}.ava-button.secondary.active{background:var(--button-secondary-background-active);color:var(--button-secondary-text-active)}.ava-button.small{padding:var(--button-size-sm-padding);font-size:var(--button-size-sm-font);height:var(--button-size-sm-height);min-width:var(--button-size-sm-min-width)}.ava-button.medium{padding:var(--button-size-md-padding);font-size:var(--button-size-md-font);height:var(--button-size-md-height);min-width:var(--button-size-md-min-width)}.ava-button.large{font-size:var(--button-size-lg-font);height:var(--button-size-lg-height);min-width:var(--button-size-lg-min-width)}.ava-button.icon-only-ava-button{padding:var(--button-icon-margin)}.ava-button.gradient{border:1px solid var(--button-gradient-border)}.ava-button>span{position:relative;z-index:2}.ava-button.ava-button-basic:after{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:#fff3;backdrop-filter:blur(10px) brightness(1.8);-webkit-backdrop-filter:blur(10px) brightness(1.8);box-shadow:0 0 20px #ffffff26;pointer-events:none;z-index:3;opacity:0;transition:opacity .2s;border-radius:50px}.ava-button.ava-button-basic:hover:after{opacity:1;animation:sweep-in .4s ease-out forwards}.ava-button.ava-button-basic:not(:hover):after{animation:sweep-out .4s ease-out forwards}@keyframes sweep-in{0%{left:-100%;opacity:.6;border-radius:0 50px 50px 0}to{left:0%;opacity:.3;border-radius:0}}@keyframes sweep-out{0%{left:0%;opacity:.6;border-radius:50px 0 0 50px}to{left:-100%;opacity:0;border-radius:0 50px 50px 0}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "ngmodule", type: LucideAngularModule }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ButtonComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-button', standalone: true, imports: [CommonModule, LucideAngularModule, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: "<button class=\"ava-button\" [class]=\"\r\n    [\r\n      basicOrAdvanced,\r\n      variant,\r\n      size,\r\n      state,\r\n      pill === true ? 'pill' : '',\r\n      state === 'active' ? 'active' : '',\r\n      isActive ? 'active-anim' : ''\r\n    ].join(' ')\r\n  \" [style.width]=\"width ? width : null\" [style.height.px]=\"height ? height : null\"\r\n  [style.background]=\"gradient || background || null\" [style.color]=\"color ? color : null\" [disabled]=\"disabled\"\r\n  [ngClass]=\"[\r\n    visual,\r\n    iconPosition === 'only' ? 'icon-only-ava-button' : '',\r\n    iconPosition === 'left' || iconPosition === 'right'\r\n      ? 'icon-lr-ava-button'\r\n      : '',\r\n    gradient ? 'gradient' : ''\r\n  ]\" (click)=\"handleClick($event)\" (keydown)=\"onKeydown($event)\">\r\n  <ng-container *ngIf=\"(iconName && iconPosition === 'left') || iconPosition === 'only'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n  <span *ngIf=\"iconPosition !== 'only'\" class=\"ava-button__label\">{{\r\n    label\r\n    }}</span>\r\n  <ng-container *ngIf=\"iconName && iconPosition === 'right'\">\r\n    <ava-icon [iconName]=\"iconName\" [iconColor]=\"computedIconColor\" [iconSize]=\"iconSize\"\r\n      [disabled]=\"disabled\"></ava-icon>\r\n  </ng-container>\r\n\r\n  <ava-icon class=\"b-dropdown\" *ngIf=\"dropdown\" iconName=\"chevron-down\" [iconColor]=\"computedIconColor\"\r\n    [iconSize]=\"30\"></ava-icon>\r\n</button>", styles: [".ava-button{display:inline-flex;position:relative;padding:var(--global-spacing-4) var(--global-spacing-5);font-size:var(--font-body-2);gap:8px;border:none;justify-content:center;align-items:center;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border-radius:var(--button-border-radius, 12px);cursor:pointer;overflow:hidden;transition:background .3s ease,transform .3s ease;z-index:0}.ava-button.primary{background:var(--button-primary-background);color:var(--button-primary-text)}.ava-button.primary.active{background:var(--button-primary-background-active)}.ava-button.primary:disabled{cursor:not-allowed;opacity:.6;pointer-events:none;color:var(--button-primary-text-disabled);background:var(--button-primary-background-disabled)}.ava-button.primary:disabled:after{display:none}.ava-button.primary:disabled:hover{transform:none}.ava-button.primary.danger{color:var(--button-danger-text);background:var(--button-danger-background)}.ava-button.primary.danger:hover{background:var(--button-danger-background-hover)}.ava-button.primary.danger.active,.ava-button.primary.danger:active{background:var(--button-danger-background-active)}.ava-button.primary.warning{color:var(--button-warning-text);background:var(--button-warning-background)}.ava-button.primary.warning:hover{background:var(--button-warning-background-hover)}.ava-button.primary.warning.active,.ava-button.primary.warning:active{background:var(--button-warning-background-active)}.ava-button.primary.pill{border-radius:50px}.ava-button.primary.glass{background:#ffffff14;backdrop-filter:var(--button-glassmorphic-blur-30);-webkit-backdrop-filter:var(--button-glassmorphic-blur-30);box-shadow:var(--button-glassmorphic-light-90);border:1px solid rgba(255,255,255,.1)}.ava-button.primary.neo{color:var(--button-primary-text);transition:all .2s ease;box-shadow:-6px -6px 12px #ffffff1a,6px 6px 12px #0000004d}.ava-button.primary.neo:hover{box-shadow:-4px -4px 8px #ffffff1f,4px 4px 8px #00000040;transform:translateY(-1px)}.ava-button.primary.neo.active,.ava-button.primary.neo:active{box-shadow:inset -4px -4px 8px #ffffff1f,inset 4px 4px 8px #0000004d;transform:translateY(1px)}.ava-button.primary.neo:focus{outline:none}.ava-button.secondary{background:var(--button-secondary-background);color:var(--button-secondary-text);border:var(--button-secondary-border)}.ava-button.secondary:hover{background:var(--button-secondary-background-hover);color:var(--button-primary-text);border:var(--button-secondary-border-hover)}.ava-button.secondary.active{background:var(--button-secondary-background-active);color:var(--button-secondary-text-active)}.ava-button.small{padding:var(--button-size-sm-padding);font-size:var(--button-size-sm-font);height:var(--button-size-sm-height);min-width:var(--button-size-sm-min-width)}.ava-button.medium{padding:var(--button-size-md-padding);font-size:var(--button-size-md-font);height:var(--button-size-md-height);min-width:var(--button-size-md-min-width)}.ava-button.large{font-size:var(--button-size-lg-font);height:var(--button-size-lg-height);min-width:var(--button-size-lg-min-width)}.ava-button.icon-only-ava-button{padding:var(--button-icon-margin)}.ava-button.gradient{border:1px solid var(--button-gradient-border)}.ava-button>span{position:relative;z-index:2}.ava-button.ava-button-basic:after{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:#fff3;backdrop-filter:blur(10px) brightness(1.8);-webkit-backdrop-filter:blur(10px) brightness(1.8);box-shadow:0 0 20px #ffffff26;pointer-events:none;z-index:3;opacity:0;transition:opacity .2s;border-radius:50px}.ava-button.ava-button-basic:hover:after{opacity:1;animation:sweep-in .4s ease-out forwards}.ava-button.ava-button-basic:not(:hover):after{animation:sweep-out .4s ease-out forwards}@keyframes sweep-in{0%{left:-100%;opacity:.6;border-radius:0 50px 50px 0}to{left:0%;opacity:.3;border-radius:0}}@keyframes sweep-out{0%{left:0%;opacity:.6;border-radius:50px 0 0 50px}to{left:-100%;opacity:0;border-radius:0 50px 50px 0}}\n"] }]
        }], propDecorators: { label: [{
                type: Input
            }], variant: [{
                type: Input
            }], size: [{
                type: Input
            }], state: [{
                type: Input
            }], visual: [{
                type: Input
            }], pill: [{
                type: Input
            }], disabled: [{
                type: Input
            }], width: [{
                type: Input
            }], height: [{
                type: Input
            }], gradient: [{
                type: Input
            }], background: [{
                type: Input
            }], color: [{
                type: Input
            }], dropdown: [{
                type: Input
            }], iconName: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], iconSize: [{
                type: Input
            }], iconPosition: [{
                type: Input
            }], userClick: [{
                type: Output
            }] } });

class CheckboxComponent {
    variant = 'default';
    size = 'medium';
    label = '';
    isChecked = false;
    indeterminate = false;
    disable = false;
    isCheckedChange = new EventEmitter();
    isAnimating = false;
    isUnchecking = false;
    // Getter for container classes
    get containerClasses() {
        return {
            'with-bg': this.variant === 'with-bg',
            'animated': this.variant === 'animated',
            'small': this.size === 'small',
            'medium': this.size === 'medium',
            'large': this.size === 'large',
            'disabled': this.disable,
        };
    }
    // Getter for checkbox classes
    get checkboxClasses() {
        return {
            'checked': this.isChecked && !this.isUnchecking,
            'indeterminate': this.indeterminate,
            'checking': this.isAnimating && this.isChecked,
            'unchecking': this.isUnchecking
        };
    }
    // Getter for showing icon
    get showIcon() {
        return this.isChecked || this.indeterminate || this.isUnchecking;
    }
    // Getter for showing checkmark
    get showCheckmark() {
        return (this.isChecked || this.isUnchecking) && !this.indeterminate;
    }
    toggleCheckbox() {
        if (this.disable)
            return;
        if (this.indeterminate) {
            this.isChecked = true;
            this.indeterminate = false;
            this.isCheckedChange.emit(this.isChecked);
            return;
        }
        if (this.variant === 'animated') {
            if (this.isChecked) {
                this.handleUnchecking();
            }
            else {
                this.handleChecking();
            }
        }
        else if (this.variant === 'with-bg') {
            if (this.isChecked) {
                this.handleWithBgUnchecking();
            }
            else {
                this.isChecked = true;
                this.isCheckedChange.emit(this.isChecked);
            }
        }
        else {
            // Default variant
            if (this.isChecked) {
                this.isUnchecking = true;
                setTimeout(() => {
                    this.isChecked = false;
                    this.isUnchecking = false;
                    this.isCheckedChange.emit(this.isChecked);
                }, 300); // Wait for erase animation (300ms)
            }
            else {
                this.isChecked = true;
                this.isCheckedChange.emit(this.isChecked);
            }
        }
    }
    // Keyboard accessibility
    onKeyDown(event) {
        // Handle Space and Enter keys to toggle checkbox
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault(); // Prevent default scrolling behavior for Space
            this.toggleCheckbox();
        }
    }
    handleChecking() {
        this.isAnimating = true;
        this.isChecked = true;
        setTimeout(() => {
            this.isAnimating = false;
            this.isCheckedChange.emit(this.isChecked);
        }, 600); // Background fill (300ms) + checkmark draw (150ms) + delay (300ms)
    }
    handleUnchecking() {
        this.isUnchecking = true;
        setTimeout(() => {
            this.isChecked = false;
            this.isUnchecking = false;
            this.isCheckedChange.emit(this.isChecked);
        }, 300); // Both background empty and checkmark erase (300ms)
    }
    handleWithBgUnchecking() {
        this.isUnchecking = true;
        setTimeout(() => {
            this.isChecked = false;
            this.isUnchecking = false;
            this.isCheckedChange.emit(this.isChecked);
        }, 150); // Both background transition and checkmark erase (150ms)
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: CheckboxComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "17.0.0", version: "19.2.14", type: CheckboxComponent, isStandalone: true, selector: "ava-checkbox", inputs: { variant: "variant", size: "size", label: "label", isChecked: "isChecked", indeterminate: "indeterminate", disable: "disable" }, outputs: { isCheckedChange: "isCheckedChange" }, ngImport: i0, template: "<div class=\"ava-checkbox\"\r\n     [ngClass]=\"containerClasses\"\r\n     (click)=\"toggleCheckbox()\"\r\n     (keydown)=\"onKeyDown($event)\"\r\n     [tabindex]=\"disable ? -1 : 0\"\r\n     [attr.role]=\"'checkbox'\"\r\n     [attr.aria-checked]=\"indeterminate ? 'mixed' : isChecked\"\r\n     [attr.aria-disabled]=\"disable\"\r\n     [attr.aria-label]=\"label || 'Checkbox'\">\r\n  <div class=\"checkbox\" [ngClass]=\"checkboxClasses\">\r\n    @if (showIcon) {\r\n    <svg class=\"checkbox-icon\" viewBox=\"0 0 24 24\">\r\n      @if (showCheckmark) {\r\n      <path class=\"checkmark-path\" [class.unchecking]=\"isUnchecking\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n        stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5 12l5 5L20 7\" />\r\n      }\r\n      @if (indeterminate) {\r\n      <rect class=\"indeterminate-rect\" fill=\"currentColor\" x=\"6\" y=\"10\" width=\"12\" height=\"4\" rx=\"2\" />\r\n      }\r\n    </svg>\r\n    }\r\n  </div>\r\n  @if (label) {\r\n  <span class=\"checkbox-label\">{{ label }}</span>\r\n  }\r\n</div>", styles: [".ava-checkbox{display:flex;align-items:center;cursor:pointer;-webkit-user-select:none;user-select:none}.ava-checkbox.disabled{cursor:var(--checkbox-cursor-disabled)}.ava-checkbox .checkbox{display:flex;width:24px;height:24px;justify-content:center;align-items:center;flex-shrink:0;aspect-ratio:1/1;background-color:var(--checkbox-box-background);border:var(--checkbox-box-checked-border);border-radius:4px;margin-right:8px;transition:border-color .3s ease}.ava-checkbox .checkbox.checked,.ava-checkbox .checkbox.indeterminate{border-color:var(--checkbox-box-checked-background)}.ava-checkbox .checkbox-icon{width:20px;height:20px;color:var(--checkbox-box-checked-background)}.ava-checkbox .checkbox-icon .checkmark-path{stroke-dasharray:22;stroke-dashoffset:22;stroke-linecap:round;stroke-linejoin:round;animation:drawCheckmark-default .25s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox .checkbox-icon .checkmark-path.unchecking{stroke-dashoffset:0;animation:eraseCheckmark-default .3s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox .checkbox-icon .indeterminate-rect{transform-origin:center;animation:scaleIn .15s cubic-bezier(0,0,.65,1) forwards;transform:scale(0);border-radius:0}.ava-checkbox .checkbox-label{font-size:var(--checkbox-label-font);color:var(--checkbox-label-color);cursor:var(--checkbox-label-cursor)}.ava-checkbox.small .checkbox,.ava-checkbox.small .checkbox-icon{width:16px;height:16px}.ava-checkbox.small .indeterminate-rect{width:12px;height:2px}.ava-checkbox.medium .checkbox,.ava-checkbox.medium .checkbox-icon{width:20px;height:20px}.ava-checkbox.medium .indeterminate-rect{width:12px;height:3px}.ava-checkbox.large .checkbox,.ava-checkbox.large .checkbox-icon{width:24px;height:24px}.ava-checkbox.large .indeterminate-rect{width:12px;height:4px}.ava-checkbox.disabled .checkbox-label{color:var(--checkbox-label-color-disabled);cursor:var(--checkbox-label-cursor-disabled)}.ava-checkbox.disabled .checkbox,.ava-checkbox.disabled .checkbox.checked,.ava-checkbox.disabled .checkbox.indeterminate{border-color:var(--checkbox-box-border-disabled);background-color:var(--checkbox-box-background-disabled)}.ava-checkbox.disabled .checkbox-icon,.ava-checkbox.disabled .checkbox-icon .checkmark-path,.ava-checkbox.disabled .checkbox-icon .indeterminate-rect{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.with-bg .checkbox.checked,.ava-checkbox.disabled.with-bg .checkbox.indeterminate{background-color:var(--checkbox-box-background-disabled);border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.with-bg .checkbox.checked .checkbox-icon,.ava-checkbox.disabled.with-bg .checkbox.indeterminate .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.with-bg .checkbox.unchecking{background-color:var(--checkbox-box-background-disabled);border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.with-bg .checkbox.unchecking .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.animated .checkbox.checked,.ava-checkbox.disabled.animated .checkbox.checking,.ava-checkbox.disabled.animated .checkbox.unchecking{border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.animated .checkbox.checked:before,.ava-checkbox.disabled.animated .checkbox.checking:before,.ava-checkbox.disabled.animated .checkbox.unchecking:before{background-color:var(--checkbox-box-background-disabled)}.ava-checkbox.disabled.animated .checkbox.checked .checkbox-icon,.ava-checkbox.disabled.animated .checkbox.checking .checkbox-icon,.ava-checkbox.disabled.animated .checkbox.unchecking .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.with-bg .checkbox{transition:none}.ava-checkbox.with-bg .checkbox.checked{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background);animation:fillBg-withbg .15s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg .checkbox.checked .checkbox-icon{color:var(--checkbox-box-checked-color)}.ava-checkbox.with-bg .checkbox.unchecking{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background);animation:emptyBg-withbg .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.with-bg .checkbox.unchecking .checkbox-icon{color:var(--checkbox-box-checked-color)}.ava-checkbox.with-bg .checkbox-icon .checkmark-path{animation:drawCheckmark-withbg .3s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg .checkbox-icon .checkmark-path.unchecking{animation:eraseCheckmark-withbg .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.with-bg.disabled .checkbox.checked{animation:fillBg-disabled .15s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg.disabled .checkbox.unchecking{animation:emptyBg-disabled .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.animated .checkbox{overflow:hidden;position:relative}.ava-checkbox.animated .checkbox.checked{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.checked:before{content:\"\";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:4px;transform:scale(0);transform-origin:bottom left;animation:fillBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.checked .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox.checking{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.checking:before{content:\"\";position:absolute;top:-2px;left:-6px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:10px;transform:scale(0);transform-origin:bottom left;animation:fillBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.checking .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox.unchecking{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.unchecking:before{content:\"\";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:10px;transform:scale(1.5);transform-origin:bottom left;animation:emptyBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.unchecking .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox-icon .checkmark-path{animation:drawCheckmark-animated .3s cubic-bezier(.25,.46,.45,.94) forwards;animation-delay:.3s}.ava-checkbox.animated .checkbox-icon .checkmark-path.unchecking{animation:eraseCheckmark-animated .15s cubic-bezier(.55,.06,.68,.19) forwards;animation-delay:0s}@keyframes drawCheckmark-default{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-default{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes drawCheckmark-withbg{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-withbg{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes drawCheckmark-animated{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-animated{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes fillBackground{0%{transform:scale(0)}to{transform:scale(1.5)}}@keyframes emptyBackground{0%{transform:scale(1.5)}to{transform:scale(0)}}@keyframes fillBg-withbg{0%{background-color:transparent;border-color:var(--checkbox-box-checked-background)}to{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background)}}@keyframes emptyBg-withbg{0%{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background)}to{background-color:transparent;border-color:var(--checkbox-box-checked-background)}}@keyframes scaleIn{0%{transform:scale(0)}to{transform:scale(1)}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: CheckboxComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-checkbox', imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-checkbox\"\r\n     [ngClass]=\"containerClasses\"\r\n     (click)=\"toggleCheckbox()\"\r\n     (keydown)=\"onKeyDown($event)\"\r\n     [tabindex]=\"disable ? -1 : 0\"\r\n     [attr.role]=\"'checkbox'\"\r\n     [attr.aria-checked]=\"indeterminate ? 'mixed' : isChecked\"\r\n     [attr.aria-disabled]=\"disable\"\r\n     [attr.aria-label]=\"label || 'Checkbox'\">\r\n  <div class=\"checkbox\" [ngClass]=\"checkboxClasses\">\r\n    @if (showIcon) {\r\n    <svg class=\"checkbox-icon\" viewBox=\"0 0 24 24\">\r\n      @if (showCheckmark) {\r\n      <path class=\"checkmark-path\" [class.unchecking]=\"isUnchecking\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"\r\n        stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M5 12l5 5L20 7\" />\r\n      }\r\n      @if (indeterminate) {\r\n      <rect class=\"indeterminate-rect\" fill=\"currentColor\" x=\"6\" y=\"10\" width=\"12\" height=\"4\" rx=\"2\" />\r\n      }\r\n    </svg>\r\n    }\r\n  </div>\r\n  @if (label) {\r\n  <span class=\"checkbox-label\">{{ label }}</span>\r\n  }\r\n</div>", styles: [".ava-checkbox{display:flex;align-items:center;cursor:pointer;-webkit-user-select:none;user-select:none}.ava-checkbox.disabled{cursor:var(--checkbox-cursor-disabled)}.ava-checkbox .checkbox{display:flex;width:24px;height:24px;justify-content:center;align-items:center;flex-shrink:0;aspect-ratio:1/1;background-color:var(--checkbox-box-background);border:var(--checkbox-box-checked-border);border-radius:4px;margin-right:8px;transition:border-color .3s ease}.ava-checkbox .checkbox.checked,.ava-checkbox .checkbox.indeterminate{border-color:var(--checkbox-box-checked-background)}.ava-checkbox .checkbox-icon{width:20px;height:20px;color:var(--checkbox-box-checked-background)}.ava-checkbox .checkbox-icon .checkmark-path{stroke-dasharray:22;stroke-dashoffset:22;stroke-linecap:round;stroke-linejoin:round;animation:drawCheckmark-default .25s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox .checkbox-icon .checkmark-path.unchecking{stroke-dashoffset:0;animation:eraseCheckmark-default .3s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox .checkbox-icon .indeterminate-rect{transform-origin:center;animation:scaleIn .15s cubic-bezier(0,0,.65,1) forwards;transform:scale(0);border-radius:0}.ava-checkbox .checkbox-label{font-size:var(--checkbox-label-font);color:var(--checkbox-label-color);cursor:var(--checkbox-label-cursor)}.ava-checkbox.small .checkbox,.ava-checkbox.small .checkbox-icon{width:16px;height:16px}.ava-checkbox.small .indeterminate-rect{width:12px;height:2px}.ava-checkbox.medium .checkbox,.ava-checkbox.medium .checkbox-icon{width:20px;height:20px}.ava-checkbox.medium .indeterminate-rect{width:12px;height:3px}.ava-checkbox.large .checkbox,.ava-checkbox.large .checkbox-icon{width:24px;height:24px}.ava-checkbox.large .indeterminate-rect{width:12px;height:4px}.ava-checkbox.disabled .checkbox-label{color:var(--checkbox-label-color-disabled);cursor:var(--checkbox-label-cursor-disabled)}.ava-checkbox.disabled .checkbox,.ava-checkbox.disabled .checkbox.checked,.ava-checkbox.disabled .checkbox.indeterminate{border-color:var(--checkbox-box-border-disabled);background-color:var(--checkbox-box-background-disabled)}.ava-checkbox.disabled .checkbox-icon,.ava-checkbox.disabled .checkbox-icon .checkmark-path,.ava-checkbox.disabled .checkbox-icon .indeterminate-rect{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.with-bg .checkbox.checked,.ava-checkbox.disabled.with-bg .checkbox.indeterminate{background-color:var(--checkbox-box-background-disabled);border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.with-bg .checkbox.checked .checkbox-icon,.ava-checkbox.disabled.with-bg .checkbox.indeterminate .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.with-bg .checkbox.unchecking{background-color:var(--checkbox-box-background-disabled);border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.with-bg .checkbox.unchecking .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.disabled.animated .checkbox.checked,.ava-checkbox.disabled.animated .checkbox.checking,.ava-checkbox.disabled.animated .checkbox.unchecking{border-color:var(--checkbox-box-border-disabled)}.ava-checkbox.disabled.animated .checkbox.checked:before,.ava-checkbox.disabled.animated .checkbox.checking:before,.ava-checkbox.disabled.animated .checkbox.unchecking:before{background-color:var(--checkbox-box-background-disabled)}.ava-checkbox.disabled.animated .checkbox.checked .checkbox-icon,.ava-checkbox.disabled.animated .checkbox.checking .checkbox-icon,.ava-checkbox.disabled.animated .checkbox.unchecking .checkbox-icon{color:var(--checkbox-icon-color-disabled)}.ava-checkbox.with-bg .checkbox{transition:none}.ava-checkbox.with-bg .checkbox.checked{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background);animation:fillBg-withbg .15s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg .checkbox.checked .checkbox-icon{color:var(--checkbox-box-checked-color)}.ava-checkbox.with-bg .checkbox.unchecking{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background);animation:emptyBg-withbg .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.with-bg .checkbox.unchecking .checkbox-icon{color:var(--checkbox-box-checked-color)}.ava-checkbox.with-bg .checkbox-icon .checkmark-path{animation:drawCheckmark-withbg .3s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg .checkbox-icon .checkmark-path.unchecking{animation:eraseCheckmark-withbg .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.with-bg.disabled .checkbox.checked{animation:fillBg-disabled .15s cubic-bezier(.25,.46,.45,.94) forwards}.ava-checkbox.with-bg.disabled .checkbox.unchecking{animation:emptyBg-disabled .15s cubic-bezier(.55,.06,.68,.19) forwards}.ava-checkbox.animated .checkbox{overflow:hidden;position:relative}.ava-checkbox.animated .checkbox.checked{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.checked:before{content:\"\";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:4px;transform:scale(0);transform-origin:bottom left;animation:fillBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.checked .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox.checking{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.checking:before{content:\"\";position:absolute;top:-2px;left:-6px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:10px;transform:scale(0);transform-origin:bottom left;animation:fillBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.checking .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox.unchecking{border-color:var(--checkbox-box-checked-background)}.ava-checkbox.animated .checkbox.unchecking:before{content:\"\";position:absolute;top:-2px;left:-2px;width:calc(100% + 4px);height:calc(100% + 4px);background-color:var(--checkbox-box-checked-background);border-radius:10px;transform:scale(1.5);transform-origin:bottom left;animation:emptyBackground .3s ease-out forwards;z-index:1}.ava-checkbox.animated .checkbox.unchecking .checkbox-icon{color:var(--checkbox-box-checked-color);position:relative;z-index:2}.ava-checkbox.animated .checkbox-icon .checkmark-path{animation:drawCheckmark-animated .3s cubic-bezier(.25,.46,.45,.94) forwards;animation-delay:.3s}.ava-checkbox.animated .checkbox-icon .checkmark-path.unchecking{animation:eraseCheckmark-animated .15s cubic-bezier(.55,.06,.68,.19) forwards;animation-delay:0s}@keyframes drawCheckmark-default{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-default{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes drawCheckmark-withbg{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-withbg{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes drawCheckmark-animated{0%{stroke-dashoffset:22}to{stroke-dashoffset:0}}@keyframes eraseCheckmark-animated{0%{stroke-dashoffset:0}to{stroke-dashoffset:22}}@keyframes fillBackground{0%{transform:scale(0)}to{transform:scale(1.5)}}@keyframes emptyBackground{0%{transform:scale(1.5)}to{transform:scale(0)}}@keyframes fillBg-withbg{0%{background-color:transparent;border-color:var(--checkbox-box-checked-background)}to{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background)}}@keyframes emptyBg-withbg{0%{background-color:var(--checkbox-box-checked-background);border-color:var(--checkbox-box-checked-background)}to{background-color:transparent;border-color:var(--checkbox-box-checked-background)}}@keyframes scaleIn{0%{transform:scale(0)}to{transform:scale(1)}}\n"] }]
        }], propDecorators: { variant: [{
                type: Input
            }], size: [{
                type: Input
            }], label: [{
                type: Input
            }], isChecked: [{
                type: Input
            }], indeterminate: [{
                type: Input
            }], disable: [{
                type: Input
            }], isCheckedChange: [{
                type: Output
            }] } });

// toggle.component.ts
class ToggleComponent {
    size = 'medium';
    title = '';
    position = 'left';
    disabled = false;
    checked = false;
    animation = true;
    checkedChange = new EventEmitter();
    onToggle() {
        if (this.disabled)
            return;
        this.checked = !this.checked;
        this.checkedChange.emit(this.checked);
    }
    onKeyDown(event) {
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault();
            this.onToggle();
        }
    }
    get titleName() {
        return this.title
            ? `toggle-title-${this.title.replace(/\s+/g, '-').toLowerCase()}`
            : null;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ToggleComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: ToggleComponent, isStandalone: true, selector: "ava-toggle", inputs: { size: "size", title: "title", position: "position", disabled: "disabled", checked: "checked", animation: "animation" }, outputs: { checkedChange: "checkedChange" }, ngImport: i0, template: "<!-- toggle.component.html -->\r\n<div class=\"ava-toggle-container\"\r\n     [class.toggle-left]=\"position === 'left'\"\r\n     [class.toggle-right]=\"position === 'right'\"\r\n     [class.disabled]=\"disabled\">\r\n  \r\n  <!-- Title - always show when it exists, let CSS handle positioning -->\r\n  <span *ngIf=\"title\"\r\n        class=\"toggle-title\"\r\n        [class.disabled]=\"disabled\"\r\n        [id]=\"titleName\">\r\n    {{ title }}\r\n  </span>\r\n  \r\n  <!-- Toggle Switch -->\r\n  <div class=\"toggle-wrapper\"\r\n       [class.toggle-small]=\"size === 'small'\"\r\n       [class.toggle-medium]=\"size === 'medium'\"\r\n       [class.toggle-large]=\"size === 'large'\"\r\n       [class.checked]=\"checked\"\r\n       [class.disabled]=\"disabled\"\r\n       [class.animated]=\"animation\"\r\n       [tabindex]=\"disabled ? -1 : 0\"\r\n       [attr.role]=\"'switch'\"\r\n       [attr.aria-checked]=\"checked\"\r\n       [attr.aria-disabled]=\"disabled\"\r\n       [attr.aria-labelledby]=\"titleName\"\r\n       [attr.aria-label]=\"!title ? 'Toggle switch' : null\"\r\n       (click)=\"onToggle()\"\r\n       (keydown)=\"onKeyDown($event)\">\r\n    \r\n    <div class=\"toggle-slider\"></div>\r\n    \r\n    <!-- Screen reader support -->\r\n    <span class=\"sr-only\">\r\n      {{ title || 'Toggle switch' }} {{ checked ? 'enabled' : 'disabled' }}\r\n    </span>\r\n  </div>\r\n</div>", styles: [".ava-toggle-container{display:flex;align-items:center;gap:12px;-webkit-user-select:none;user-select:none}.ava-toggle-container.toggle-left{flex-direction:row}.ava-toggle-container.toggle-right{flex-direction:row-reverse}.ava-toggle-container.disabled{opacity:.6}.toggle-title{font-size:var(--toggle-label-font);color:var(--toggle-label-text);white-space:nowrap}.toggle-title.disabled{color:var(--toggle-label-disabled-text)}.toggle-wrapper{position:relative;display:flex;align-items:center;border-radius:var(--toggle-border-radius);background:var(--toggle-track-background);cursor:pointer;flex-shrink:0;outline:none;padding:2px}.toggle-wrapper.disabled{cursor:not-allowed}.toggle-wrapper.checked{background:var(--toggle-track-checked-background)}.toggle-wrapper.toggle-small{width:var(--toggle-size-sm-width);height:var(--toggle-size-sm-height)}.toggle-wrapper.toggle-small .toggle-slider{width:16px;height:16px;transform:translate(1px)}.toggle-wrapper.toggle-small.checked .toggle-slider{transform:translate(19px)}.toggle-wrapper.toggle-small:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-small:not(.disabled):active.checked .toggle-slider{transform:translate(15px) scale(.95)}.toggle-wrapper.toggle-medium{width:var(--toggle-size-md-width);height:var(--toggle-size-md-height)}.toggle-wrapper.toggle-medium .toggle-slider{width:20px;height:20px;transform:translate(1px)}.toggle-wrapper.toggle-medium.checked .toggle-slider{transform:translate(23px)}.toggle-wrapper.toggle-medium:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-medium:not(.disabled):active.checked .toggle-slider{transform:translate(19px) scale(.95)}.toggle-wrapper.toggle-large{width:var(--toggle-size-lg-width);height:var(--toggle-size-lg-height)}.toggle-wrapper.toggle-large .toggle-slider{width:24px;height:24px;transform:translate(1px)}.toggle-wrapper.toggle-large.checked .toggle-slider{transform:translate(27px)}.toggle-wrapper.toggle-large:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-large:not(.disabled):active.checked .toggle-slider{transform:translate(19px) scale(.95)}.toggle-wrapper:focus-visible{outline:2px solid var(--toggle-track-checked-background);outline-offset:2px;box-shadow:0 0 0 4px #9c27b01a}.toggle-wrapper:focus:not(:focus-visible){outline:none;box-shadow:none}.toggle-wrapper:not(.disabled):hover .toggle-slider{box-shadow:0 2px 4px #2727271a}.toggle-wrapper:not(.disabled):active .toggle-slider{box-shadow:none}.toggle-wrapper.animated{transition:background-color .3s ease}.toggle-wrapper.animated .toggle-slider{transition:transform .6s cubic-bezier(.68,-.55,.265,1.55),box-shadow .2s ease}.toggle-wrapper:not(.animated) .toggle-slider{transition:box-shadow .2s ease}.toggle-slider{position:relative;border-radius:var(--toggle-border-radius);background:var(--toggle-thumb-background);box-shadow:0 2px 4px #2727271a;flex-shrink:0}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}@media (prefers-contrast: high){.toggle-wrapper{border:1px solid transparent}.toggle-wrapper:focus-visible{border-color:currentColor;outline:2px solid}.toggle-wrapper:focus:not(:focus-visible){border:none;outline:none}.toggle-slider{border:1px solid rgba(0,0,0,.2)}}@media (prefers-reduced-motion: reduce){.toggle-wrapper.animated{transition:background-color .15s ease!important}.toggle-wrapper.animated .toggle-slider{transition:transform .15s ease,box-shadow .15s ease!important}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ToggleComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-toggle', standalone: true, imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<!-- toggle.component.html -->\r\n<div class=\"ava-toggle-container\"\r\n     [class.toggle-left]=\"position === 'left'\"\r\n     [class.toggle-right]=\"position === 'right'\"\r\n     [class.disabled]=\"disabled\">\r\n  \r\n  <!-- Title - always show when it exists, let CSS handle positioning -->\r\n  <span *ngIf=\"title\"\r\n        class=\"toggle-title\"\r\n        [class.disabled]=\"disabled\"\r\n        [id]=\"titleName\">\r\n    {{ title }}\r\n  </span>\r\n  \r\n  <!-- Toggle Switch -->\r\n  <div class=\"toggle-wrapper\"\r\n       [class.toggle-small]=\"size === 'small'\"\r\n       [class.toggle-medium]=\"size === 'medium'\"\r\n       [class.toggle-large]=\"size === 'large'\"\r\n       [class.checked]=\"checked\"\r\n       [class.disabled]=\"disabled\"\r\n       [class.animated]=\"animation\"\r\n       [tabindex]=\"disabled ? -1 : 0\"\r\n       [attr.role]=\"'switch'\"\r\n       [attr.aria-checked]=\"checked\"\r\n       [attr.aria-disabled]=\"disabled\"\r\n       [attr.aria-labelledby]=\"titleName\"\r\n       [attr.aria-label]=\"!title ? 'Toggle switch' : null\"\r\n       (click)=\"onToggle()\"\r\n       (keydown)=\"onKeyDown($event)\">\r\n    \r\n    <div class=\"toggle-slider\"></div>\r\n    \r\n    <!-- Screen reader support -->\r\n    <span class=\"sr-only\">\r\n      {{ title || 'Toggle switch' }} {{ checked ? 'enabled' : 'disabled' }}\r\n    </span>\r\n  </div>\r\n</div>", styles: [".ava-toggle-container{display:flex;align-items:center;gap:12px;-webkit-user-select:none;user-select:none}.ava-toggle-container.toggle-left{flex-direction:row}.ava-toggle-container.toggle-right{flex-direction:row-reverse}.ava-toggle-container.disabled{opacity:.6}.toggle-title{font-size:var(--toggle-label-font);color:var(--toggle-label-text);white-space:nowrap}.toggle-title.disabled{color:var(--toggle-label-disabled-text)}.toggle-wrapper{position:relative;display:flex;align-items:center;border-radius:var(--toggle-border-radius);background:var(--toggle-track-background);cursor:pointer;flex-shrink:0;outline:none;padding:2px}.toggle-wrapper.disabled{cursor:not-allowed}.toggle-wrapper.checked{background:var(--toggle-track-checked-background)}.toggle-wrapper.toggle-small{width:var(--toggle-size-sm-width);height:var(--toggle-size-sm-height)}.toggle-wrapper.toggle-small .toggle-slider{width:16px;height:16px;transform:translate(1px)}.toggle-wrapper.toggle-small.checked .toggle-slider{transform:translate(19px)}.toggle-wrapper.toggle-small:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-small:not(.disabled):active.checked .toggle-slider{transform:translate(15px) scale(.95)}.toggle-wrapper.toggle-medium{width:var(--toggle-size-md-width);height:var(--toggle-size-md-height)}.toggle-wrapper.toggle-medium .toggle-slider{width:20px;height:20px;transform:translate(1px)}.toggle-wrapper.toggle-medium.checked .toggle-slider{transform:translate(23px)}.toggle-wrapper.toggle-medium:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-medium:not(.disabled):active.checked .toggle-slider{transform:translate(19px) scale(.95)}.toggle-wrapper.toggle-large{width:var(--toggle-size-lg-width);height:var(--toggle-size-lg-height)}.toggle-wrapper.toggle-large .toggle-slider{width:24px;height:24px;transform:translate(1px)}.toggle-wrapper.toggle-large.checked .toggle-slider{transform:translate(27px)}.toggle-wrapper.toggle-large:not(.disabled):active:not(.checked) .toggle-slider{transform:translate(1px) scale(.95)}.toggle-wrapper.toggle-large:not(.disabled):active.checked .toggle-slider{transform:translate(19px) scale(.95)}.toggle-wrapper:focus-visible{outline:2px solid var(--toggle-track-checked-background);outline-offset:2px;box-shadow:0 0 0 4px #9c27b01a}.toggle-wrapper:focus:not(:focus-visible){outline:none;box-shadow:none}.toggle-wrapper:not(.disabled):hover .toggle-slider{box-shadow:0 2px 4px #2727271a}.toggle-wrapper:not(.disabled):active .toggle-slider{box-shadow:none}.toggle-wrapper.animated{transition:background-color .3s ease}.toggle-wrapper.animated .toggle-slider{transition:transform .6s cubic-bezier(.68,-.55,.265,1.55),box-shadow .2s ease}.toggle-wrapper:not(.animated) .toggle-slider{transition:box-shadow .2s ease}.toggle-slider{position:relative;border-radius:var(--toggle-border-radius);background:var(--toggle-thumb-background);box-shadow:0 2px 4px #2727271a;flex-shrink:0}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}@media (prefers-contrast: high){.toggle-wrapper{border:1px solid transparent}.toggle-wrapper:focus-visible{border-color:currentColor;outline:2px solid}.toggle-wrapper:focus:not(:focus-visible){border:none;outline:none}.toggle-slider{border:1px solid rgba(0,0,0,.2)}}@media (prefers-reduced-motion: reduce){.toggle-wrapper.animated{transition:background-color .15s ease!important}.toggle-wrapper.animated .toggle-slider{transition:transform .15s ease,box-shadow .15s ease!important}}\n"] }]
        }], propDecorators: { size: [{
                type: Input
            }], title: [{
                type: Input
            }], position: [{
                type: Input
            }], disabled: [{
                type: Input
            }], checked: [{
                type: Input
            }], animation: [{
                type: Input
            }], checkedChange: [{
                type: Output
            }] } });

class TabsComponent {
    cdr;
    tabs = [];
    value = null;
    valueChange = new EventEmitter();
    highlightActiveText = true;
    maxWidth;
    showChevrons = false;
    ariaLabel;
    variant = 'default';
    style;
    iconColor;
    container = false;
    containerStyle;
    dropdownSelect = new EventEmitter();
    tabClick = new EventEmitter();
    tabHover = new EventEmitter();
    tabFocus = new EventEmitter();
    tabBlur = new EventEmitter();
    dropdownItemClick = new EventEmitter();
    dropdownItemHover = new EventEmitter();
    dropdownItemFocus = new EventEmitter();
    dropdownItemBlur = new EventEmitter();
    /**
     * Props to pass to ava-button when using the 'button' variant. All ava-button @Inputs are supported.
     * Defaults to { variant: 'secondary' } for design system consistency.
     */
    buttonProps = { variant: 'secondary' };
    /**
     * Style object for the tab list (nav.ava-tabs__list). Useful for glassmorphic, neomorphic, or custom backgrounds.
     */
    listStyle;
    /**
     * Style object for the wrapper (div.awe-tabs__container). Useful for custom backgrounds or effects when container is false.
     */
    wrapperStyle;
    /**
     * Optional style object for the dropdown menu (ava-tabs__dropdown-menu). Allows full customization.
     */
    dropdownMenuStyle;
    get tabsCount() {
        return this.tabs.length;
    }
    tabList;
    tabButtons;
    underline = { width: 0, left: 0 };
    showScrollButtons = false;
    disableScrollLeft = true;
    disableScrollRight = false;
    openDropdownIndex = null;
    isTabHovered = null;
    isDropdownHovered = null;
    dropdownPosition = null;
    resizeObserver;
    lastTabsSnapshot = '';
    lastValue = null;
    initialized = false;
    dropdownCloseTimeout = null;
    tabButtonRefs = [];
    dropdownMenuRef = null;
    justOpenedDropdown = false;
    constructor(cdr) {
        this.cdr = cdr;
    }
    ngOnInit() {
        if (this.tabs.length && (this.value === null || !this.tabs.some(tab => tab.value === this.value))) {
            this.value = this.tabs[0]?.value;
        }
    }
    ngAfterViewInit() {
        // Initial microtask deferral for DOM paint
        Promise.resolve().then(() => {
            this.initializeComponent();
            this.initialized = true;
        });
        // Track tab button refs for outside click detection
        setTimeout(() => {
            this.tabButtonRefs = this.tabButtons.map(ref => ref.nativeElement);
        });
    }
    ngAfterViewChecked() {
        // Only recalculate if tabs or value changed
        const tabsSnapshot = JSON.stringify(this.tabs.map(t => ({ label: t.label, value: t.value, icon: t.icon, iconPosition: t.iconPosition, disabled: t.disabled })));
        if (tabsSnapshot !== this.lastTabsSnapshot || this.value !== this.lastValue) {
            this.lastTabsSnapshot = tabsSnapshot;
            this.lastValue = this.value;
            Promise.resolve().then(() => {
                this.initializeComponent();
            });
        }
    }
    ngOnDestroy() {
        this.resizeObserver?.disconnect();
        this.removeDocumentClickListener();
    }
    initializeComponent() {
        this.checkForOverflow();
        this.updateUnderlinePosition();
        this.resizeObserver = new ResizeObserver(() => {
            this.checkForOverflow();
            this.updateUnderlinePosition();
        });
        this.resizeObserver.observe(this.tabList.nativeElement);
    }
    onTabClick(tab, event) {
        const idx = this.tabs.findIndex(t => t.value === tab.value);
        if (tab.dropdown) {
            if (this.openDropdownIndex === idx) {
                this.openDropdownIndex = null;
                this.dropdownPosition = null;
                this.isTabHovered = null;
                this.isDropdownHovered = null;
                this.removeDocumentClickListener();
                return;
            }
            else {
                this.openDropdownIndex = idx;
                if (event && event.currentTarget) {
                    const rect = event.currentTarget.getBoundingClientRect();
                    const scrollY = window.scrollY || window.pageYOffset;
                    this.dropdownPosition = {
                        left: rect.left + rect.width / 2,
                        top: rect.bottom + scrollY
                    };
                }
                else {
                    this.dropdownPosition = { left: 0, top: 48 };
                }
                this.justOpenedDropdown = true;
                this.addDocumentClickListener();
                return;
            }
        }
        if (!tab.disabled && tab.value !== this.value) {
            this.value = tab.value;
            this.updateUnderlinePosition();
            this.valueChange.emit(tab.value);
        }
        this.tabClick.emit(tab);
    }
    onTabHover(tab) {
        this.tabHover.emit(tab);
    }
    onTabFocus(tab) {
        this.tabFocus.emit(tab);
    }
    onTabBlur(tab) {
        this.tabBlur.emit(tab);
    }
    scroll(direction) {
        const scrollAmount = direction === 'left' ? -200 : 200;
        this.tabList.nativeElement.scrollBy({ left: scrollAmount, behavior: 'smooth' });
        setTimeout(() => {
            this.updateScrollButtonState();
        }, 300);
    }
    checkForOverflow() {
        if (!this.tabList)
            return;
        const el = this.tabList.nativeElement;
        const hasOverflow = el.scrollWidth > el.clientWidth;
        this.showScrollButtons = this.showChevrons && hasOverflow;
        this.updateScrollButtonState();
        this.cdr.detectChanges();
    }
    updateScrollButtonState() {
        if (!this.tabList)
            return;
        const el = this.tabList.nativeElement;
        this.disableScrollLeft = el.scrollLeft === 0;
        this.disableScrollRight = el.scrollLeft + el.clientWidth >= el.scrollWidth - 1;
        this.cdr.detectChanges();
    }
    updateUnderlinePosition() {
        if (!this.tabButtons || this.tabButtons.length === 0 || !this.tabList)
            return;
        const idx = this.tabs.findIndex(tab => tab.value === this.value);
        if (idx === -1)
            return;
        const tabElement = this.tabButtons.get(idx)?.nativeElement;
        if (tabElement) {
            const navRect = this.tabList.nativeElement.getBoundingClientRect();
            const tabRect = tabElement.getBoundingClientRect();
            this.underline.width = tabRect.width;
            this.underline.left = tabRect.left - navRect.left + this.tabList.nativeElement.scrollLeft;
            this.cdr.detectChanges();
        }
    }
    get activeTab() {
        return this.tabs.find(tab => tab.value === this.value);
    }
    onDropdownItemClick(tab, item) {
        this.value = tab.value;
        this.valueChange.emit(tab.value);
        this.dropdownSelect.emit({ parent: tab, item });
        this.dropdownItemClick.emit({ parent: tab, item });
        this.openDropdownIndex = null;
    }
    onDropdownItemHover(tab, item) {
        this.dropdownItemHover.emit({ parent: tab, item });
    }
    onDropdownItemFocus(tab, item) {
        this.dropdownItemFocus.emit({ parent: tab, item });
    }
    onDropdownItemBlur(tab, item) {
        this.dropdownItemBlur.emit({ parent: tab, item });
    }
    onTabDropdownEnter(i, tabButton) {
        this.isTabHovered = i;
        this.openDropdownIndex = i;
        if (this.dropdownCloseTimeout) {
            clearTimeout(this.dropdownCloseTimeout);
            this.dropdownCloseTimeout = null;
        }
        if (tabButton) {
            const rect = tabButton.getBoundingClientRect();
            const scrollY = window.scrollY || window.pageYOffset;
            this.dropdownPosition = {
                left: rect.left + rect.width / 2,
                top: rect.bottom + scrollY
            };
        }
        this.addDocumentClickListener();
    }
    onTabDropdownLeave(i) {
        this.isTabHovered = null;
        if (this.dropdownCloseTimeout) {
            clearTimeout(this.dropdownCloseTimeout);
            this.dropdownCloseTimeout = null;
        }
        this.dropdownCloseTimeout = setTimeout(() => {
            if (this.isTabHovered !== i && this.isDropdownHovered !== i) {
                this.openDropdownIndex = null;
                this.dropdownPosition = null;
            }
        }, 180);
    }
    onDropdownMenuEnter(i, ref) {
        this.isDropdownHovered = i;
        this.openDropdownIndex = i;
        if (this.dropdownCloseTimeout) {
            clearTimeout(this.dropdownCloseTimeout);
            this.dropdownCloseTimeout = null;
        }
        if (ref) {
            this.dropdownMenuRef = ref;
        }
    }
    onDropdownMenuLeave(i) {
        this.isDropdownHovered = null;
        if (this.dropdownCloseTimeout) {
            clearTimeout(this.dropdownCloseTimeout);
            this.dropdownCloseTimeout = null;
        }
        this.dropdownCloseTimeout = setTimeout(() => {
            if (this.isTabHovered !== i && this.isDropdownHovered !== i) {
                this.openDropdownIndex = null;
                this.dropdownPosition = null;
            }
        }, 180);
    }
    addDocumentClickListener() {
        document.addEventListener('mousedown', this.handleDocumentClick, true);
    }
    removeDocumentClickListener() {
        document.removeEventListener('mousedown', this.handleDocumentClick, true);
    }
    handleDocumentClick = (event) => {
        if (this.justOpenedDropdown) {
            this.justOpenedDropdown = false;
            return;
        }
        if (!this.dropdownMenuRef && this.openDropdownIndex !== null)
            return;
        const dropdownMenu = this.dropdownMenuRef;
        const tabButton = this.tabButtonRefs[this.openDropdownIndex];
        if (dropdownMenu &&
            !dropdownMenu.contains(event.target) &&
            tabButton &&
            !tabButton.contains(event.target)) {
            this.openDropdownIndex = null;
            this.dropdownPosition = null;
            this.isTabHovered = null;
            this.isDropdownHovered = null;
            this.removeDocumentClickListener();
        }
    };
    get customStyle() {
        return {
            ...(this.style || {}),
            ...(this.maxWidth ? { 'max-width': this.maxWidth, width: '100%' } : {})
        };
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: TabsComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: TabsComponent, isStandalone: true, selector: "ava-tabs", inputs: { tabs: "tabs", value: "value", highlightActiveText: "highlightActiveText", maxWidth: "maxWidth", showChevrons: "showChevrons", ariaLabel: "ariaLabel", variant: "variant", style: "style", iconColor: "iconColor", container: "container", containerStyle: "containerStyle", buttonProps: "buttonProps", listStyle: "listStyle", wrapperStyle: "wrapperStyle", dropdownMenuStyle: "dropdownMenuStyle" }, outputs: { valueChange: "valueChange", dropdownSelect: "dropdownSelect", tabClick: "tabClick", tabHover: "tabHover", tabFocus: "tabFocus", tabBlur: "tabBlur", dropdownItemClick: "dropdownItemClick", dropdownItemHover: "dropdownItemHover", dropdownItemFocus: "dropdownItemFocus", dropdownItemBlur: "dropdownItemBlur" }, host: { properties: { "style.--tabs-count": "this.tabsCount" } }, viewQueries: [{ propertyName: "tabList", first: true, predicate: ["tabList"], descendants: true }, { propertyName: "tabButtons", predicate: ["tabButton"], descendants: true }], ngImport: i0, template: "<ng-container *ngIf=\"container; else noContainer\">\r\n  <div class=\"ava-tabs__container-wrapper\" [ngStyle]=\"containerStyle\">\r\n    <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n      <div class=\"awe-tabs__scroll-area\">\r\n        <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n          [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n          (scroll)=\"updateScrollButtonState()\">\r\n          <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n            <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'ava-tabs__tab--active': tab.value === value,\r\n                'ava-tabs__tab--disabled': !!tab.disabled,\r\n                'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n              }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n                tab.iconPosition === 'end'\r\n                  ? 'right'\r\n                  : tab.iconPosition === 'start'\r\n                  ? 'left'\r\n                  : 'left'\r\n              \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n              [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n              [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n                tab.value === value ? 'active' : buttonProps.state || 'default'\r\n              \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n              [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n              [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n              [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n              [iconSize]=\"buttonProps.iconSize ?? 18\">\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">\u25BC</span>\r\n              </ng-container>\r\n            </ava-button>\r\n          </ng-container>\r\n          <ng-template #notButtonVariant>\r\n            <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'icon-top': tab.iconPosition === 'top',\r\n                'icon-bottom': tab.iconPosition === 'bottom',\r\n                'ava-tabs__tab--has-dropdown': tab.dropdown\r\n              }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n              [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n              [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n              [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" type=\"button\" role=\"tab\">\r\n              <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n                <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                  [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n              </ng-container>\r\n              <ng-template #normalTabContent>\r\n                <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                  <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"ava-tabs__icon-top\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"ava-tabs__icon-bottom\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'start'\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'end'\">\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"\r\n                      class=\"ava-tabs__icon\"></ava-icon>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchDefault>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                </ng-container>\r\n                <ng-container *ngIf=\"tab.dropdown\">\r\n                  <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">\u25BC</span>\r\n                </ng-container>\r\n              </ng-template>\r\n            </button>\r\n          </ng-template>\r\n          <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n            [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n        </nav>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\"\r\n          (click)=\"scroll('left')\" [disabled]=\"disableScrollLeft\">\r\n          <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n          (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n          <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n      {{ activeTab?.content }}\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n<ng-template #noContainer>\r\n  <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n    <div class=\"awe-tabs__scroll-area\">\r\n      <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n        [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n        (scroll)=\"updateScrollButtonState()\">\r\n        <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n          <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'ava-tabs__tab--active': tab.value === value,\r\n              'ava-tabs__tab--disabled': !!tab.disabled,\r\n              'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n            }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n              tab.iconPosition === 'end'\r\n                ? 'right'\r\n                : tab.iconPosition === 'start'\r\n                ? 'left'\r\n                : 'left'\r\n            \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n            [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n            [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n              tab.value === value ? 'active' : buttonProps.state || 'default'\r\n            \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n            [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n            [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n            [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n            [iconSize]=\"buttonProps.iconSize ?? 18\" [dropdown]=\"!!tab.dropdown\">\r\n            <ng-container *ngIf=\"tab.dropdown\">\r\n              <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">\u25BC</span>\r\n            </ng-container>\r\n          </ava-button>\r\n        </ng-container>\r\n        <ng-template #notButtonVariant>\r\n          <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'icon-top': tab.iconPosition === 'top',\r\n              'icon-bottom': tab.iconPosition === 'bottom',\r\n              'ava-tabs__tab--has-dropdown': tab.dropdown\r\n            }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n            [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n            [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n            [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" type=\"button\" role=\"tab\">\r\n            <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n              <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n            </ng-container>\r\n            <ng-template #normalTabContent>\r\n              <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                  <div class=\"ava-tabs__icon-top\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                  <div class=\"ava-tabs__icon-bottom\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'start'\">\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'end'\">\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\" class=\"ava-tabs__icon\"></ava-icon>\r\n                </ng-container>\r\n                <ng-container *ngSwitchDefault>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">\u25BC</span>\r\n              </ng-container>\r\n            </ng-template>\r\n          </button>\r\n        </ng-template>\r\n        <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n          [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n      </nav>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\" (click)=\"scroll('left')\"\r\n        [disabled]=\"disableScrollLeft\">\r\n        <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n        (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n        <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n  <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n    {{ activeTab?.content }}\r\n  </div>\r\n</ng-template>\r\n<div *ngIf=\"openDropdownIndex !== null\" #dropdownMenu class=\"ava-tabs__dropdown-menu ava-tabs__dropdown-menu--portal\"\r\n  [ngStyle]=\"dropdownMenuStyle\" [style.left.px]=\"dropdownPosition?.left\" [style.top.px]=\"dropdownPosition?.top\"\r\n  (mouseenter)=\"onDropdownMenuEnter(openDropdownIndex, dropdownMenu)\"\r\n  (mouseleave)=\"onDropdownMenuLeave(openDropdownIndex)\" (focusout)=\"onDropdownMenuLeave(openDropdownIndex)\">\r\n  <button *ngFor=\"let item of tabs[openDropdownIndex]?.dropdown?.items\" class=\"ava-tabs__dropdown-item\" type=\"button\"\r\n    (click)=\"onDropdownItemClick(tabs[openDropdownIndex], item)\"\r\n    (mouseenter)=\"onDropdownItemHover(tabs[openDropdownIndex], item)\"\r\n    (focusin)=\"onDropdownItemFocus(tabs[openDropdownIndex], item)\"\r\n    (focusout)=\"onDropdownItemBlur(tabs[openDropdownIndex], item)\">\r\n    <ava-icon *ngIf=\"item.icon\" [iconName]=\"item.icon\" [iconSize]=\"18\"\r\n      [iconColor]=\"item.iconColor || 'var(--tab-dropdown-item-color)'\" [cursor]=\"false\" [disabled]=\"false\"></ava-icon>\r\n    <span class=\"ava-tabs__dropdown-label-group\">\r\n      <span class=\"ava-tabs__dropdown-label\">{{ item.label }}</span>\r\n      <span *ngIf=\"item.subtitle\" class=\"ava-tabs__dropdown-subtitle\">{{\r\n        item.subtitle\r\n        }}</span>\r\n    </span>\r\n  </button>\r\n</div>", styles: [".ava-tabs__list{display:flex;align-items:center;gap:var(--tabs-gap, 2rem);overflow-x:auto;overflow-y:visible;position:relative;background:var(--tabs-background, transparent);padding:var(--tabs-list-padding, 1rem 0);min-width:0;width:100%;max-width:100%;scrollbar-width:none;padding-left:32px;padding-right:32px}.ava-tabs__list::-webkit-scrollbar{display:none}.ava-tabs__tab{--tab-icon-color: var(--tabs-tab-color, #333);background:none;border:none;outline:none;cursor:pointer;color:var(--tabs-tab-color, #333);font:var(--tabs-tab-font, var(--font-body-1));padding:var(--tabs-tab-padding, .5rem 1.5rem);position:relative;transition:color .2s;display:flex;align-items:center;gap:var(--tabs-icon-gap, .5rem);white-space:nowrap;flex-shrink:0}.ava-tabs__tab--active{--tab-icon-color: var(--tabs-tab-active-color, #4A5568);color:var(--tabs-tab-active-color, #4A5568);font-weight:var(--tabs-tab-active-font-weight, 600)}.ava-tabs__tab--disabled{color:var(--tabs-tab-disabled-color, #aaa);opacity:.5;cursor:not-allowed}.ava-tabs__tab.icon-top{flex-direction:column;align-items:center;gap:.25rem}.ava-tabs__tab.icon-bottom{flex-direction:column-reverse;align-items:center;gap:.25rem}.ava-tabs__list.awe-tabs--highlight-active .ava-tabs__tab--active{color:var(--tabs-tab-active-color, #4A5568);font-weight:var(--tabs-tab-active-font-weight, 600)}.ava-tabs__icon{font-size:var(--tabs-icon-size, 1.5rem);display:inline-flex;align-items:center}.ava-tabs__underline{position:absolute;left:0;bottom:0;height:var(--tabs-underline-height, 2px);background:var(--tabs-underline-color, #4A5568);border-radius:1px;transition:transform .3s cubic-bezier(.4,0,.2,1),width .3s cubic-bezier(.4,0,.2,1);will-change:transform,width;opacity:1}.ava-tabs__content{margin-top:var(--tabs-content-margin, 2rem);font:var(--tabs-content-font, var(--font-body-1));color:var(--tabs-content-color, var(--color-text-primary))}.awe-tabs__container{position:relative;display:flex;align-items:center;width:100%}.awe-tabs__scroll-area{position:relative;width:100%}.awe-tabs__scroll-btn{position:absolute;top:50%;transform:translateY(-50%);z-index:1000;background:var(--tabs-scroll-btn-bg, rgba(255, 255, 255, .85));border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:var(--tabs-scroll-btn-shadow, 0 2px 8px rgba(0, 0, 0, .1));transition:background .2s,box-shadow .2s,opacity .2s;opacity:.85}.awe-tabs__scroll-btn:hover:not(:disabled){background:var(--tabs-scroll-btn-hover-bg, #f1f5f9);box-shadow:var(--tabs-scroll-btn-hover-shadow, 0 4px 12px rgba(0, 0, 0, .14));opacity:1}.awe-tabs__scroll-btn:disabled{opacity:.3;cursor:not-allowed;background:var(--tabs-scroll-btn-disabled-bg, rgba(255, 255, 255, .7))}.awe-tabs__scroll-btn--left{left:-22px}.awe-tabs__scroll-btn--right{right:-22px}.awe-tabs__scroll-btn lucide-icon{width:24px;height:24px;color:var(--tabs-scroll-btn-icon-color, #64748b)}.ava-tabs__icon-top,.ava-tabs__icon-bottom{display:flex;justify-content:center;width:100%}.ava-tabs__tab--button-variant{border-radius:var(--tabs-button-radius, 6px);border:1.5px solid var(--tabs-button-border, transparent);background:var(--tabs-button-bg, transparent);color:var(--tabs-tab-color, #333);font-weight:500;transition:background .2s,color .2s,border .2s,box-shadow .2s;box-shadow:var(--tabs-button-shadow, none);margin:0 2px;padding:.5rem 1.5rem}.ava-tabs__tab--button-variant.ava-tabs__tab--active{background:var(--tabs-button-active-bg, #fff);color:var(--tabs-button-active-color, #222);border:1.5px solid var(--tabs-button-active-border, #ff2d55);box-shadow:var(--tabs-button-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, .08));font-weight:600;z-index:1}.ava-tabs__tab--button-variant:not(.ava-tabs__tab--active){background:var(--tabs-button-bg, transparent);color:var(--tabs-button-inactive-color, #222);border:1.5px solid var(--tabs-button-border, transparent);box-shadow:var(--tabs-button-shadow, none);font-weight:500;opacity:.85}.ava-tabs__tab--button-variant:hover:not(:disabled):not(.ava-tabs__tab--active){background:var(--tabs-button-hover-bg, #f8f9fb);border:1.5px solid var(--tabs-button-hover-border, #e5e7eb);color:var(--tabs-button-hover-color, #222);opacity:1}.ava-tabs__tab--button-variant.ava-tabs__tab--disabled{opacity:.5;cursor:not-allowed;background:var(--tabs-button-disabled-bg, transparent);border:1.5px solid var(--tabs-button-disabled-border, #e5e7eb);color:var(--tabs-button-disabled-color, #aaa)}.ava-tabs__tab--button-variant.icon-top,.ava-tabs__tab--button-variant.icon-bottom{flex-direction:column;align-items:center;gap:.25rem}.ava-tabs__tab--icon-variant{width:40px;height:40px;min-width:40px;min-height:40px;max-width:40px;max-height:40px;padding:0;display:flex;align-items:center;justify-content:center;border-radius:var(--tabs-icon-radius, 8px);background:var(--tabs-icon-bg, transparent);border:1.5px solid var(--tabs-icon-border, transparent);transition:background .2s,border .2s,box-shadow .2s;box-shadow:var(--tabs-icon-shadow, none);font-size:1.5rem;margin:0 2px}.ava-tabs__tab--icon-variant.ava-tabs__tab--active{background:var(--tabs-icon-active-bg, #fff);border:1.5px solid var(--tabs-icon-active-border, #ff2d55);box-shadow:var(--tabs-icon-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, .08))}.ava-tabs__tab--icon-variant:not(.ava-tabs__tab--active){background:var(--tabs-icon-bg, transparent);border:1.5px solid var(--tabs-icon-border, transparent);opacity:.85}.ava-tabs__tab--icon-variant:hover:not(:disabled):not(.ava-tabs__tab--active){background:var(--tabs-icon-hover-bg, #f8f9fb);border:1.5px solid var(--tabs-icon-hover-border, #e5e7eb);opacity:1}.ava-tabs__tab--icon-variant.ava-tabs__tab--disabled{opacity:.5;cursor:not-allowed;background:var(--tabs-icon-disabled-bg, transparent);border:1.5px solid var(--tabs-icon-disabled-border, #e5e7eb)}.ava-tabs__tab--icon-variant .ava-tabs__label{display:none!important}.ava-tabs__tab--has-dropdown{position:relative}.ava-tabs__dropdown-arrow{margin-left:.25em;font-size:.85em;vertical-align:middle;color:var(--tabs-dropdown-arrow-color, #888);transition:color .2s}.ava-tabs__tab--active .ava-tabs__dropdown-arrow{color:var(--tabs-dropdown-arrow-active-color, #ff2d55)}.ava-tabs__dropdown-menu{position:absolute;left:50%;top:100%;transform:translate(-50%) translateY(8px);min-width:160px;background:var(--tabs-dropdown-bg, #fff);border-radius:var(--tabs-dropdown-radius, 8px);box-shadow:var(--tabs-dropdown-shadow, 0 8px 24px 0 rgba(0, 0, 0, .1));padding:.5rem 0;z-index:9999;opacity:1;pointer-events:auto;animation:dropdown-fade-in .18s cubic-bezier(.4,0,.2,1)}@keyframes dropdown-fade-in{0%{opacity:0;transform:translate(-50%) translateY(0)}to{opacity:1;transform:translate(-50%) translateY(8px)}}.ava-tabs__dropdown-item{width:100%;background:var(--tabs-dropdown-item-bg, none);border:none;outline:none;color:var(--tabs-dropdown-item-color, #222);font:inherit;padding:.5rem 1.25rem;display:flex;align-items:center;gap:.5rem;cursor:pointer;transition:background .18s,color .18s;border-radius:var(--tabs-dropdown-item-radius, 4px)}.ava-tabs__dropdown-item:hover,.ava-tabs__dropdown-item:focus{background:var(--tabs-dropdown-item-hover-bg, #f8f9fb);color:var(--tabs-dropdown-item-hover-color, #ff2d55)}.ava-tabs__container-wrapper{display:block;width:100%}.ava-tabs__dropdown-chevron{display:inline-block;margin-left:.5em;font-size:1em;vertical-align:middle;color:inherit;opacity:.7;transition:transform .2s}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgForOf, selector: "[ngFor][ngForOf]", inputs: ["ngForOf", "ngForTrackBy", "ngForTemplate"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }, { kind: "directive", type: i1.NgSwitch, selector: "[ngSwitch]", inputs: ["ngSwitch"] }, { kind: "directive", type: i1.NgSwitchCase, selector: "[ngSwitchCase]", inputs: ["ngSwitchCase"] }, { kind: "directive", type: i1.NgSwitchDefault, selector: "[ngSwitchDefault]" }, { kind: "ngmodule", type: LucideAngularModule }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }, { kind: "component", type: ButtonComponent, selector: "ava-button", inputs: ["label", "variant", "size", "state", "visual", "pill", "disabled", "width", "height", "gradient", "background", "color", "dropdown", "iconName", "iconColor", "iconSize", "iconPosition"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: TabsComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-tabs', standalone: true, imports: [CommonModule, LucideAngularModule, IconComponent, ButtonComponent], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: "<ng-container *ngIf=\"container; else noContainer\">\r\n  <div class=\"ava-tabs__container-wrapper\" [ngStyle]=\"containerStyle\">\r\n    <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n      <div class=\"awe-tabs__scroll-area\">\r\n        <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n          [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n          (scroll)=\"updateScrollButtonState()\">\r\n          <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n            <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'ava-tabs__tab--active': tab.value === value,\r\n                'ava-tabs__tab--disabled': !!tab.disabled,\r\n                'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n              }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n                tab.iconPosition === 'end'\r\n                  ? 'right'\r\n                  : tab.iconPosition === 'start'\r\n                  ? 'left'\r\n                  : 'left'\r\n              \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n              [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n              [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n                tab.value === value ? 'active' : buttonProps.state || 'default'\r\n              \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n              [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n              [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n              [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n              [iconSize]=\"buttonProps.iconSize ?? 18\">\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">\u25BC</span>\r\n              </ng-container>\r\n            </ava-button>\r\n          </ng-container>\r\n          <ng-template #notButtonVariant>\r\n            <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n                'icon-top': tab.iconPosition === 'top',\r\n                'icon-bottom': tab.iconPosition === 'bottom',\r\n                'ava-tabs__tab--has-dropdown': tab.dropdown\r\n              }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n              [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n              [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n              [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabHover(tab)\r\n              \" (mouseleave)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" (focusin)=\"\r\n                tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n                onTabFocus(tab)\r\n              \" (focusout)=\"\r\n                tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n              \" type=\"button\" role=\"tab\">\r\n              <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n                <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                  [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n              </ng-container>\r\n              <ng-template #normalTabContent>\r\n                <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                  <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"ava-tabs__icon-top\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"ava-tabs__icon-bottom\">\r\n                      <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                        [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    </div>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'start'\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchCase=\"'end'\">\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"\r\n                      class=\"ava-tabs__icon\"></ava-icon>\r\n                  </ng-container>\r\n                  <ng-container *ngSwitchDefault>\r\n                    <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  </ng-container>\r\n                </ng-container>\r\n                <ng-container *ngIf=\"tab.dropdown\">\r\n                  <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">\u25BC</span>\r\n                </ng-container>\r\n              </ng-template>\r\n            </button>\r\n          </ng-template>\r\n          <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n            [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n        </nav>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\"\r\n          (click)=\"scroll('left')\" [disabled]=\"disableScrollLeft\">\r\n          <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n        <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n          (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n          <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n      {{ activeTab?.content }}\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n<ng-template #noContainer>\r\n  <div class=\"awe-tabs__container\" [ngStyle]=\"wrapperStyle ? wrapperStyle : customStyle\">\r\n    <div class=\"awe-tabs__scroll-area\">\r\n      <nav #tabList class=\"ava-tabs__list\" [ngStyle]=\"listStyle\"\r\n        [class.awe-tabs--highlight-active]=\"highlightActiveText\" role=\"tablist\" [attr.aria-label]=\"ariaLabel\"\r\n        (scroll)=\"updateScrollButtonState()\">\r\n        <ng-container *ngIf=\"variant === 'button'; else notButtonVariant\">\r\n          <ava-button *ngFor=\"let tab of tabs; let i = index\" class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'ava-tabs__tab--active': tab.value === value,\r\n              'ava-tabs__tab--disabled': !!tab.disabled,\r\n              'ava-tabs__tab--has-dropdown': !!tab.dropdown\r\n            }\" [label]=\"tab.label\" [iconName]=\"tab.icon || ''\" [iconPosition]=\"\r\n              tab.iconPosition === 'end'\r\n                ? 'right'\r\n                : tab.iconPosition === 'start'\r\n                ? 'left'\r\n                : 'left'\r\n            \" [disabled]=\"!!tab.disabled\" [attr.aria-selected]=\"tab.value === value\"\r\n            [attr.aria-disabled]=\"tab.disabled\" [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"tab.label\"\r\n            [attr.title]=\"tab.label\" (userClick)=\"onTabClick(tab, $event)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i) : null; onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" [variant]=\"buttonProps.variant || 'secondary'\" [size]=\"buttonProps.size || 'normal'\" [state]=\"\r\n              tab.value === value ? 'active' : buttonProps.state || 'default'\r\n            \" [visual]=\"buttonProps.visual || 'normal'\" [pill]=\"buttonProps.pill ?? false\"\r\n            [width]=\"buttonProps.width || ''\" [height]=\"buttonProps.height || ''\"\r\n            [gradient]=\"buttonProps.gradient || ''\" [background]=\"buttonProps.background || ''\"\r\n            [color]=\"buttonProps.color || ''\" [iconColor]=\"buttonProps.iconColor || ''\"\r\n            [iconSize]=\"buttonProps.iconSize ?? 18\" [dropdown]=\"!!tab.dropdown\">\r\n            <ng-container *ngIf=\"tab.dropdown\">\r\n              <span class=\"ava-tabs__dropdown-chevron\" aria-hidden=\"true\">\u25BC</span>\r\n            </ng-container>\r\n          </ava-button>\r\n        </ng-container>\r\n        <ng-template #notButtonVariant>\r\n          <button *ngFor=\"let tab of tabs; let i = index\" #tabButton class=\"ava-tabs__tab\" [ngClass]=\"{\r\n              'icon-top': tab.iconPosition === 'top',\r\n              'icon-bottom': tab.iconPosition === 'bottom',\r\n              'ava-tabs__tab--has-dropdown': tab.dropdown\r\n            }\" [class.ava-tabs__tab--active]=\"tab.value === value\" [class.ava-tabs__tab--disabled]=\"tab.disabled\"\r\n            [attr.aria-selected]=\"tab.value === value\" [attr.aria-disabled]=\"tab.disabled\"\r\n            [attr.tabindex]=\"tab.disabled ? -1 : 0\" [attr.aria-label]=\"variant === 'icon' ? tab.label : null\"\r\n            [attr.title]=\"variant === 'icon' ? tab.label : null\" (click)=\"onTabClick(tab)\" (mouseenter)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabHover(tab)\r\n            \" (mouseleave)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" (focusin)=\"\r\n              tab.dropdown ? onTabDropdownEnter(i, tabButton) : null;\r\n              onTabFocus(tab)\r\n            \" (focusout)=\"\r\n              tab.dropdown ? onTabDropdownLeave(i) : null; onTabBlur(tab)\r\n            \" type=\"button\" role=\"tab\">\r\n            <ng-container *ngIf=\"variant === 'icon'; else normalTabContent\">\r\n              <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n            </ng-container>\r\n            <ng-template #normalTabContent>\r\n              <ng-container [ngSwitch]=\"tab.iconPosition\">\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                  <div class=\"ava-tabs__icon-top\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                  <div class=\"ava-tabs__icon-bottom\">\r\n                    <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\"\r\n                      [iconColor]=\"'var(--tab-icon-color)'\" [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  </div>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'start'\">\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\"></ava-icon>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"'end'\">\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                  <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" [iconSize]=\"18\" [iconColor]=\"'var(--tab-icon-color)'\"\r\n                    [cursor]=\"false\" [disabled]=\"!!tab.disabled\" class=\"ava-tabs__icon\"></ava-icon>\r\n                </ng-container>\r\n                <ng-container *ngSwitchDefault>\r\n                  <span class=\"ava-tabs__label\">{{ tab.label }}</span>\r\n                </ng-container>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"tab.dropdown\">\r\n                <span class=\"ava-tabs__dropdown-arrow\" aria-hidden=\"true\">\u25BC</span>\r\n              </ng-container>\r\n            </ng-template>\r\n          </button>\r\n        </ng-template>\r\n        <div *ngIf=\"variant !== 'button' && variant !== 'icon'\" class=\"ava-tabs__underline\"\r\n          [style.width.px]=\"underline.width\" [style.transform]=\"'translateX(' + underline.left + 'px)'\"></div>\r\n      </nav>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--left\" (click)=\"scroll('left')\"\r\n        [disabled]=\"disableScrollLeft\">\r\n        <ava-icon iconName=\"chevron-left\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n      <button *ngIf=\"showScrollButtons\" class=\"awe-tabs__scroll-btn awe-tabs__scroll-btn--right\"\r\n        (click)=\"scroll('right')\" [disabled]=\"disableScrollRight\">\r\n        <ava-icon iconName=\"chevron-right\" [iconSize]=\"18\" [iconColor]=\"'grey'\" [cursor]=\"false\"></ava-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n  <div class=\"ava-tabs__content\" *ngIf=\"activeTab?.content\">\r\n    {{ activeTab?.content }}\r\n  </div>\r\n</ng-template>\r\n<div *ngIf=\"openDropdownIndex !== null\" #dropdownMenu class=\"ava-tabs__dropdown-menu ava-tabs__dropdown-menu--portal\"\r\n  [ngStyle]=\"dropdownMenuStyle\" [style.left.px]=\"dropdownPosition?.left\" [style.top.px]=\"dropdownPosition?.top\"\r\n  (mouseenter)=\"onDropdownMenuEnter(openDropdownIndex, dropdownMenu)\"\r\n  (mouseleave)=\"onDropdownMenuLeave(openDropdownIndex)\" (focusout)=\"onDropdownMenuLeave(openDropdownIndex)\">\r\n  <button *ngFor=\"let item of tabs[openDropdownIndex]?.dropdown?.items\" class=\"ava-tabs__dropdown-item\" type=\"button\"\r\n    (click)=\"onDropdownItemClick(tabs[openDropdownIndex], item)\"\r\n    (mouseenter)=\"onDropdownItemHover(tabs[openDropdownIndex], item)\"\r\n    (focusin)=\"onDropdownItemFocus(tabs[openDropdownIndex], item)\"\r\n    (focusout)=\"onDropdownItemBlur(tabs[openDropdownIndex], item)\">\r\n    <ava-icon *ngIf=\"item.icon\" [iconName]=\"item.icon\" [iconSize]=\"18\"\r\n      [iconColor]=\"item.iconColor || 'var(--tab-dropdown-item-color)'\" [cursor]=\"false\" [disabled]=\"false\"></ava-icon>\r\n    <span class=\"ava-tabs__dropdown-label-group\">\r\n      <span class=\"ava-tabs__dropdown-label\">{{ item.label }}</span>\r\n      <span *ngIf=\"item.subtitle\" class=\"ava-tabs__dropdown-subtitle\">{{\r\n        item.subtitle\r\n        }}</span>\r\n    </span>\r\n  </button>\r\n</div>", styles: [".ava-tabs__list{display:flex;align-items:center;gap:var(--tabs-gap, 2rem);overflow-x:auto;overflow-y:visible;position:relative;background:var(--tabs-background, transparent);padding:var(--tabs-list-padding, 1rem 0);min-width:0;width:100%;max-width:100%;scrollbar-width:none;padding-left:32px;padding-right:32px}.ava-tabs__list::-webkit-scrollbar{display:none}.ava-tabs__tab{--tab-icon-color: var(--tabs-tab-color, #333);background:none;border:none;outline:none;cursor:pointer;color:var(--tabs-tab-color, #333);font:var(--tabs-tab-font, var(--font-body-1));padding:var(--tabs-tab-padding, .5rem 1.5rem);position:relative;transition:color .2s;display:flex;align-items:center;gap:var(--tabs-icon-gap, .5rem);white-space:nowrap;flex-shrink:0}.ava-tabs__tab--active{--tab-icon-color: var(--tabs-tab-active-color, #4A5568);color:var(--tabs-tab-active-color, #4A5568);font-weight:var(--tabs-tab-active-font-weight, 600)}.ava-tabs__tab--disabled{color:var(--tabs-tab-disabled-color, #aaa);opacity:.5;cursor:not-allowed}.ava-tabs__tab.icon-top{flex-direction:column;align-items:center;gap:.25rem}.ava-tabs__tab.icon-bottom{flex-direction:column-reverse;align-items:center;gap:.25rem}.ava-tabs__list.awe-tabs--highlight-active .ava-tabs__tab--active{color:var(--tabs-tab-active-color, #4A5568);font-weight:var(--tabs-tab-active-font-weight, 600)}.ava-tabs__icon{font-size:var(--tabs-icon-size, 1.5rem);display:inline-flex;align-items:center}.ava-tabs__underline{position:absolute;left:0;bottom:0;height:var(--tabs-underline-height, 2px);background:var(--tabs-underline-color, #4A5568);border-radius:1px;transition:transform .3s cubic-bezier(.4,0,.2,1),width .3s cubic-bezier(.4,0,.2,1);will-change:transform,width;opacity:1}.ava-tabs__content{margin-top:var(--tabs-content-margin, 2rem);font:var(--tabs-content-font, var(--font-body-1));color:var(--tabs-content-color, var(--color-text-primary))}.awe-tabs__container{position:relative;display:flex;align-items:center;width:100%}.awe-tabs__scroll-area{position:relative;width:100%}.awe-tabs__scroll-btn{position:absolute;top:50%;transform:translateY(-50%);z-index:1000;background:var(--tabs-scroll-btn-bg, rgba(255, 255, 255, .85));border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:var(--tabs-scroll-btn-shadow, 0 2px 8px rgba(0, 0, 0, .1));transition:background .2s,box-shadow .2s,opacity .2s;opacity:.85}.awe-tabs__scroll-btn:hover:not(:disabled){background:var(--tabs-scroll-btn-hover-bg, #f1f5f9);box-shadow:var(--tabs-scroll-btn-hover-shadow, 0 4px 12px rgba(0, 0, 0, .14));opacity:1}.awe-tabs__scroll-btn:disabled{opacity:.3;cursor:not-allowed;background:var(--tabs-scroll-btn-disabled-bg, rgba(255, 255, 255, .7))}.awe-tabs__scroll-btn--left{left:-22px}.awe-tabs__scroll-btn--right{right:-22px}.awe-tabs__scroll-btn lucide-icon{width:24px;height:24px;color:var(--tabs-scroll-btn-icon-color, #64748b)}.ava-tabs__icon-top,.ava-tabs__icon-bottom{display:flex;justify-content:center;width:100%}.ava-tabs__tab--button-variant{border-radius:var(--tabs-button-radius, 6px);border:1.5px solid var(--tabs-button-border, transparent);background:var(--tabs-button-bg, transparent);color:var(--tabs-tab-color, #333);font-weight:500;transition:background .2s,color .2s,border .2s,box-shadow .2s;box-shadow:var(--tabs-button-shadow, none);margin:0 2px;padding:.5rem 1.5rem}.ava-tabs__tab--button-variant.ava-tabs__tab--active{background:var(--tabs-button-active-bg, #fff);color:var(--tabs-button-active-color, #222);border:1.5px solid var(--tabs-button-active-border, #ff2d55);box-shadow:var(--tabs-button-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, .08));font-weight:600;z-index:1}.ava-tabs__tab--button-variant:not(.ava-tabs__tab--active){background:var(--tabs-button-bg, transparent);color:var(--tabs-button-inactive-color, #222);border:1.5px solid var(--tabs-button-border, transparent);box-shadow:var(--tabs-button-shadow, none);font-weight:500;opacity:.85}.ava-tabs__tab--button-variant:hover:not(:disabled):not(.ava-tabs__tab--active){background:var(--tabs-button-hover-bg, #f8f9fb);border:1.5px solid var(--tabs-button-hover-border, #e5e7eb);color:var(--tabs-button-hover-color, #222);opacity:1}.ava-tabs__tab--button-variant.ava-tabs__tab--disabled{opacity:.5;cursor:not-allowed;background:var(--tabs-button-disabled-bg, transparent);border:1.5px solid var(--tabs-button-disabled-border, #e5e7eb);color:var(--tabs-button-disabled-color, #aaa)}.ava-tabs__tab--button-variant.icon-top,.ava-tabs__tab--button-variant.icon-bottom{flex-direction:column;align-items:center;gap:.25rem}.ava-tabs__tab--icon-variant{width:40px;height:40px;min-width:40px;min-height:40px;max-width:40px;max-height:40px;padding:0;display:flex;align-items:center;justify-content:center;border-radius:var(--tabs-icon-radius, 8px);background:var(--tabs-icon-bg, transparent);border:1.5px solid var(--tabs-icon-border, transparent);transition:background .2s,border .2s,box-shadow .2s;box-shadow:var(--tabs-icon-shadow, none);font-size:1.5rem;margin:0 2px}.ava-tabs__tab--icon-variant.ava-tabs__tab--active{background:var(--tabs-icon-active-bg, #fff);border:1.5px solid var(--tabs-icon-active-border, #ff2d55);box-shadow:var(--tabs-icon-active-shadow, 0 2px 8px 0 rgba(255, 45, 85, .08))}.ava-tabs__tab--icon-variant:not(.ava-tabs__tab--active){background:var(--tabs-icon-bg, transparent);border:1.5px solid var(--tabs-icon-border, transparent);opacity:.85}.ava-tabs__tab--icon-variant:hover:not(:disabled):not(.ava-tabs__tab--active){background:var(--tabs-icon-hover-bg, #f8f9fb);border:1.5px solid var(--tabs-icon-hover-border, #e5e7eb);opacity:1}.ava-tabs__tab--icon-variant.ava-tabs__tab--disabled{opacity:.5;cursor:not-allowed;background:var(--tabs-icon-disabled-bg, transparent);border:1.5px solid var(--tabs-icon-disabled-border, #e5e7eb)}.ava-tabs__tab--icon-variant .ava-tabs__label{display:none!important}.ava-tabs__tab--has-dropdown{position:relative}.ava-tabs__dropdown-arrow{margin-left:.25em;font-size:.85em;vertical-align:middle;color:var(--tabs-dropdown-arrow-color, #888);transition:color .2s}.ava-tabs__tab--active .ava-tabs__dropdown-arrow{color:var(--tabs-dropdown-arrow-active-color, #ff2d55)}.ava-tabs__dropdown-menu{position:absolute;left:50%;top:100%;transform:translate(-50%) translateY(8px);min-width:160px;background:var(--tabs-dropdown-bg, #fff);border-radius:var(--tabs-dropdown-radius, 8px);box-shadow:var(--tabs-dropdown-shadow, 0 8px 24px 0 rgba(0, 0, 0, .1));padding:.5rem 0;z-index:9999;opacity:1;pointer-events:auto;animation:dropdown-fade-in .18s cubic-bezier(.4,0,.2,1)}@keyframes dropdown-fade-in{0%{opacity:0;transform:translate(-50%) translateY(0)}to{opacity:1;transform:translate(-50%) translateY(8px)}}.ava-tabs__dropdown-item{width:100%;background:var(--tabs-dropdown-item-bg, none);border:none;outline:none;color:var(--tabs-dropdown-item-color, #222);font:inherit;padding:.5rem 1.25rem;display:flex;align-items:center;gap:.5rem;cursor:pointer;transition:background .18s,color .18s;border-radius:var(--tabs-dropdown-item-radius, 4px)}.ava-tabs__dropdown-item:hover,.ava-tabs__dropdown-item:focus{background:var(--tabs-dropdown-item-hover-bg, #f8f9fb);color:var(--tabs-dropdown-item-hover-color, #ff2d55)}.ava-tabs__container-wrapper{display:block;width:100%}.ava-tabs__dropdown-chevron{display:inline-block;margin-left:.5em;font-size:1em;vertical-align:middle;color:inherit;opacity:.7;transition:transform .2s}\n"] }]
        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { tabs: [{
                type: Input
            }], value: [{
                type: Input
            }], valueChange: [{
                type: Output
            }], highlightActiveText: [{
                type: Input
            }], maxWidth: [{
                type: Input
            }], showChevrons: [{
                type: Input
            }], ariaLabel: [{
                type: Input
            }], variant: [{
                type: Input
            }], style: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], container: [{
                type: Input
            }], containerStyle: [{
                type: Input
            }], dropdownSelect: [{
                type: Output
            }], tabClick: [{
                type: Output
            }], tabHover: [{
                type: Output
            }], tabFocus: [{
                type: Output
            }], tabBlur: [{
                type: Output
            }], dropdownItemClick: [{
                type: Output
            }], dropdownItemHover: [{
                type: Output
            }], dropdownItemFocus: [{
                type: Output
            }], dropdownItemBlur: [{
                type: Output
            }], buttonProps: [{
                type: Input
            }], listStyle: [{
                type: Input
            }], wrapperStyle: [{
                type: Input
            }], dropdownMenuStyle: [{
                type: Input
            }], tabsCount: [{
                type: HostBinding,
                args: ['style.--tabs-count']
            }], tabList: [{
                type: ViewChild,
                args: ['tabList']
            }], tabButtons: [{
                type: ViewChildren,
                args: ['tabButton']
            }] } });

class PaginationControlsComponent {
    currentPage = 1;
    totalPages = 10;
    type = 'basic';
    pageChange = new EventEmitter();
    goToPage(page) {
        if (typeof page === 'number' && page !== this.currentPage) {
            this.pageChange.emit(page);
        }
    }
    // Return extended or basic pages depending on type
    get pages() {
        switch (this.type) {
            case 'basic':
                return this.getBasicPages();
            case 'extended':
                return this.getExtendedPages();
            case 'standard':
                return this.getStandardPages();
            default:
                return [];
        }
    }
    getBasicPages() {
        const pages = [];
        const { currentPage, totalPages } = this;
        if (totalPages <= 7) {
            for (let i = 1; i <= totalPages; i++)
                pages.push(i);
        }
        else {
            pages.push(1);
            if (currentPage > 3)
                pages.push('...');
            const start = Math.max(2, currentPage - 1);
            const end = Math.min(totalPages - 1, currentPage + 1);
            for (let i = start; i <= end; i++)
                pages.push(i);
            if (currentPage < totalPages - 2)
                pages.push('...');
            pages.push(totalPages);
        }
        return pages;
    }
    getExtendedPages() {
        return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }
    getStandardPages() {
        const { currentPage, totalPages } = this;
        const pages = [];
        const firstPages = [1, 2, 3];
        const lastPages = [totalPages - 2, totalPages - 1, totalPages];
        // Add first 3 always
        firstPages.forEach((p) => {
            if (p <= totalPages)
                pages.push(p);
        });
        // Case: currentPage <= 3 → show only beginning
        if (currentPage <= 3) {
            pages.push('...');
            lastPages.forEach((p) => {
                if (!pages.includes(p) && p > 3)
                    pages.push(p);
            });
            return pages;
        }
        // Case: currentPage == 4 → slight expansion
        if (currentPage === 4) {
            pages.push(4);
            pages.push('...');
            lastPages.forEach((p) => {
                if (!pages.includes(p))
                    pages.push(p);
            });
            return pages;
        }
        // Case: currentPage in middle
        if (currentPage > 4 && currentPage < totalPages - 3) {
            pages.push('...');
            pages.push(currentPage);
            pages.push('...');
            lastPages.forEach((p) => {
                if (!pages.includes(p))
                    pages.push(p);
            });
            return pages;
        }
        // Case: currentPage is near the end (≥ totalPages - 3)
        if (currentPage >= totalPages - 3) {
            pages.push('...');
            for (let i = totalPages - 3; i <= totalPages; i++) {
                if (!pages.includes(i) && i > 3)
                    pages.push(i);
            }
            return pages;
        }
        return pages;
    }
    shouldShow(page) {
        if (typeof page !== 'number') {
            return false;
        }
        const { currentPage, totalPages } = this;
        const firstPages = [1, 2, 3];
        const lastPages = [totalPages - 1, totalPages];
        return (firstPages.includes(page) ||
            lastPages.includes(page) ||
            Math.abs(currentPage - page) <= 1);
    }
    shouldInsertDots(index) {
        const pages = this.pages;
        const curr = pages[index];
        const next = pages[index + 1];
        return (typeof curr === 'number' &&
            typeof next === 'number' &&
            this.shouldShow(next) &&
            !this.shouldShow(curr));
    }
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.pageChange.emit(this.currentPage + 1);
        }
    }
    prevPage() {
        if (this.currentPage > 1) {
            this.pageChange.emit(this.currentPage - 1);
        }
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: PaginationControlsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: PaginationControlsComponent, isStandalone: true, selector: "ava-pagination-controls", inputs: { currentPage: "currentPage", totalPages: "totalPages", type: "type" }, outputs: { pageChange: "pageChange" }, ngImport: i0, template: "<!-- TYPE: basic -->\r\n<ng-container *ngIf=\"type === 'basic'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages\">\r\n      <button\r\n        *ngIf=\"page !== '...'; else dots\"\r\n        (click)=\"goToPage(page)\"\r\n        [ngClass]=\"{ active: page === currentPage }\"\r\n        class=\"page-btn\"\r\n      >\r\n        {{ page }}\r\n      </button>\r\n      <ng-template #dots>\r\n        <span class=\"dots\">...</span>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: extended -->\r\n<ng-container *ngIf=\"type === 'extended'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages; let i = index\">\r\n      <ng-container *ngIf=\"shouldShow(page); else maybeDots\">\r\n        <button\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n      </ng-container>\r\n      <ng-template #maybeDots>\r\n        <ng-container *ngIf=\"shouldInsertDots(i)\">\r\n          <span class=\"dots\">...</span>\r\n        </ng-container>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: standard -->\r\n<ng-container *ngIf=\"type === 'standard'\">\r\n  <div class=\"pagination-container standard\">\r\n    <!-- Left: Previous -->\r\n    <div class=\"nav-left\">\r\n      <button\r\n        (click)=\"prevPage()\"\r\n        [disabled]=\"currentPage === 1\"\r\n        class=\"nav-btn\"\r\n      >\r\n        \u2190 Previous\r\n      </button>\r\n    </div>\r\n    <!-- Center: Page numbers -->\r\n    <div class=\"pages\">\r\n      <ng-container *ngFor=\"let page of pages\">\r\n        <button\r\n          *ngIf=\"page !== '...'; else dots\"\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n        <ng-template #dots>\r\n          <span class=\"dots\">...</span>\r\n        </ng-template>\r\n      </ng-container>\r\n    </div>\r\n\r\n    <!-- Right: Next -->\r\n    <div class=\"nav-right\">\r\n      <button\r\n        (click)=\"nextPage()\"\r\n        [disabled]=\"currentPage === totalPages\"\r\n        class=\"nav-btn\"\r\n      >\r\n        Next \u2192\r\n      </button>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: pageinfo -->\r\n<ng-container *ngIf=\"type === 'pageinfo'\">\r\n  <div class=\"pagination-container page\">\r\n    <button class=\"nav-btn\" (click)=\"prevPage()\" [disabled]=\"currentPage === 1\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"nav-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: simplepageinfo -->\r\n<ng-container *ngIf=\"type === 'simplepageinfo'\">\r\n  <div class=\"pagination-container simplepage\">\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"prevPage()\"\r\n      [disabled]=\"currentPage === 1\"\r\n      aria-label=\"Previous page\"\r\n    >\r\n      \u2190\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      aria-label=\"Next page\"\r\n    >\r\n      \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n", styles: [".pagination-container{display:flex;gap:.5rem;align-items:center}.pagination-container .nav-btn,.pagination-container .page-btn{border:var(--pagination-border);border-radius:.5rem;padding:.5rem 1rem;cursor:pointer;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);transition:all .2s ease}.pagination-container .nav-btn:disabled,.pagination-container .page-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container .nav-btn.active,.pagination-container .page-btn.active{background:#e0bce7;color:var(--pagination-item-text)}.pagination-container .dots{padding:.5rem;color:var(--pagination-item-text)}.pagination-container.standard{display:flex;align-items:center;justify-content:space-between;width:100%;padding:1rem 0}.pagination-container.standard .nav-left,.pagination-container.standard .nav-right{width:7.5rem;display:flex;justify-content:center}.pagination-container.standard .pages{display:flex;gap:.5rem;justify-content:center;flex:1}.pagination-container.standard .nav-btn{background:none;border:none;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);font-size:.875rem;cursor:pointer}.pagination-container.standard .nav-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.standard .page-btn{background:none;border:none;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);font-size:.875rem;padding:.5rem 1rem;cursor:pointer;transition:background .2s}.pagination-container.standard .page-btn:hover{background-color:var(--pagination-background)}.pagination-container.standard .page-btn.active{background-color:#e0bce7;color:var(--pagination-dark-text);font-weight:600}.pagination-container.standard .dots{padding:.5rem;color:var(--pagination-item-disabled-tex)}.pagination-container.page{display:flex;justify-content:space-between;align-items:center;width:100%;padding:1rem 0}.pagination-container.page .nav-btn{padding:.5rem 1rem;border:var(--pagination-border);border-radius:.5rem;background:var(--color-background-primary);color:var(--pagination-item-text);font-weight:var(--pagination-font-weight-md);cursor:pointer;transition:all .2s ease}.pagination-container.page .nav-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.page .page-label{font-weight:var(--pagination-font-weight-md);color:var(--pagination-item-text);font-size:.875rem;text-align:center;flex:1}.pagination-container.simplepage{display:flex;justify-content:center;align-items:center;gap:1.5rem}.pagination-container.simplepage .icon-btn{background:transparent;border:none;color:var(--pagination-item-text);font-size:1.2rem;cursor:pointer}.pagination-container.simplepage .icon-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.simplepage .page-label{font-weight:var(--pagination-font-weight-md);font-size:.875rem;color:var(--pagination-item-text)}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgForOf, selector: "[ngFor][ngForOf]", inputs: ["ngForOf", "ngForTrackBy", "ngForTemplate"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: PaginationControlsComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-pagination-controls', standalone: true, imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<!-- TYPE: basic -->\r\n<ng-container *ngIf=\"type === 'basic'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages\">\r\n      <button\r\n        *ngIf=\"page !== '...'; else dots\"\r\n        (click)=\"goToPage(page)\"\r\n        [ngClass]=\"{ active: page === currentPage }\"\r\n        class=\"page-btn\"\r\n      >\r\n        {{ page }}\r\n      </button>\r\n      <ng-template #dots>\r\n        <span class=\"dots\">...</span>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: extended -->\r\n<ng-container *ngIf=\"type === 'extended'\">\r\n  <div class=\"pagination-container\">\r\n    <button (click)=\"prevPage()\" [disabled]=\"currentPage === 1\" class=\"nav-btn\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <ng-container *ngFor=\"let page of pages; let i = index\">\r\n      <ng-container *ngIf=\"shouldShow(page); else maybeDots\">\r\n        <button\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n      </ng-container>\r\n      <ng-template #maybeDots>\r\n        <ng-container *ngIf=\"shouldInsertDots(i)\">\r\n          <span class=\"dots\">...</span>\r\n        </ng-container>\r\n      </ng-template>\r\n    </ng-container>\r\n\r\n    <button\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      class=\"nav-btn\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: standard -->\r\n<ng-container *ngIf=\"type === 'standard'\">\r\n  <div class=\"pagination-container standard\">\r\n    <!-- Left: Previous -->\r\n    <div class=\"nav-left\">\r\n      <button\r\n        (click)=\"prevPage()\"\r\n        [disabled]=\"currentPage === 1\"\r\n        class=\"nav-btn\"\r\n      >\r\n        \u2190 Previous\r\n      </button>\r\n    </div>\r\n    <!-- Center: Page numbers -->\r\n    <div class=\"pages\">\r\n      <ng-container *ngFor=\"let page of pages\">\r\n        <button\r\n          *ngIf=\"page !== '...'; else dots\"\r\n          (click)=\"goToPage(page)\"\r\n          [ngClass]=\"{ active: page === currentPage }\"\r\n          class=\"page-btn\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n        <ng-template #dots>\r\n          <span class=\"dots\">...</span>\r\n        </ng-template>\r\n      </ng-container>\r\n    </div>\r\n\r\n    <!-- Right: Next -->\r\n    <div class=\"nav-right\">\r\n      <button\r\n        (click)=\"nextPage()\"\r\n        [disabled]=\"currentPage === totalPages\"\r\n        class=\"nav-btn\"\r\n      >\r\n        Next \u2192\r\n      </button>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: pageinfo -->\r\n<ng-container *ngIf=\"type === 'pageinfo'\">\r\n  <div class=\"pagination-container page\">\r\n    <button class=\"nav-btn\" (click)=\"prevPage()\" [disabled]=\"currentPage === 1\">\r\n      \u2190 Previous\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"nav-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n    >\r\n      Next \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- TYPE: simplepageinfo -->\r\n<ng-container *ngIf=\"type === 'simplepageinfo'\">\r\n  <div class=\"pagination-container simplepage\">\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"prevPage()\"\r\n      [disabled]=\"currentPage === 1\"\r\n      aria-label=\"Previous page\"\r\n    >\r\n      \u2190\r\n    </button>\r\n\r\n    <div class=\"page-label\">Page {{ currentPage }} of {{ totalPages }}</div>\r\n\r\n    <button\r\n      class=\"icon-btn\"\r\n      (click)=\"nextPage()\"\r\n      [disabled]=\"currentPage === totalPages\"\r\n      aria-label=\"Next page\"\r\n    >\r\n      \u2192\r\n    </button>\r\n  </div>\r\n</ng-container>\r\n", styles: [".pagination-container{display:flex;gap:.5rem;align-items:center}.pagination-container .nav-btn,.pagination-container .page-btn{border:var(--pagination-border);border-radius:.5rem;padding:.5rem 1rem;cursor:pointer;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);transition:all .2s ease}.pagination-container .nav-btn:disabled,.pagination-container .page-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container .nav-btn.active,.pagination-container .page-btn.active{background:#e0bce7;color:var(--pagination-item-text)}.pagination-container .dots{padding:.5rem;color:var(--pagination-item-text)}.pagination-container.standard{display:flex;align-items:center;justify-content:space-between;width:100%;padding:1rem 0}.pagination-container.standard .nav-left,.pagination-container.standard .nav-right{width:7.5rem;display:flex;justify-content:center}.pagination-container.standard .pages{display:flex;gap:.5rem;justify-content:center;flex:1}.pagination-container.standard .nav-btn{background:none;border:none;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);font-size:.875rem;cursor:pointer}.pagination-container.standard .nav-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.standard .page-btn{background:none;border:none;background:var(--pagination-background);color:var(--pagination-item-text);font-weight:var(-pagination-font-weight-md);font-size:.875rem;padding:.5rem 1rem;cursor:pointer;transition:background .2s}.pagination-container.standard .page-btn:hover{background-color:var(--pagination-background)}.pagination-container.standard .page-btn.active{background-color:#e0bce7;color:var(--pagination-dark-text);font-weight:600}.pagination-container.standard .dots{padding:.5rem;color:var(--pagination-item-disabled-tex)}.pagination-container.page{display:flex;justify-content:space-between;align-items:center;width:100%;padding:1rem 0}.pagination-container.page .nav-btn{padding:.5rem 1rem;border:var(--pagination-border);border-radius:.5rem;background:var(--color-background-primary);color:var(--pagination-item-text);font-weight:var(--pagination-font-weight-md);cursor:pointer;transition:all .2s ease}.pagination-container.page .nav-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.page .page-label{font-weight:var(--pagination-font-weight-md);color:var(--pagination-item-text);font-size:.875rem;text-align:center;flex:1}.pagination-container.simplepage{display:flex;justify-content:center;align-items:center;gap:1.5rem}.pagination-container.simplepage .icon-btn{background:transparent;border:none;color:var(--pagination-item-text);font-size:1.2rem;cursor:pointer}.pagination-container.simplepage .icon-btn:disabled{opacity:.4;cursor:not-allowed}.pagination-container.simplepage .page-label{font-weight:var(--pagination-font-weight-md);font-size:.875rem;color:var(--pagination-item-text)}\n"] }]
        }], propDecorators: { currentPage: [{
                type: Input
            }], totalPages: [{
                type: Input
            }], type: [{
                type: Input
            }], pageChange: [{
                type: Output
            }] } });

class AccordionComponent {
    expanded = false;
    animation = false;
    controlled = false;
    iconClosed = '';
    iconOpen = '';
    titleIcon = '';
    iconPosition = 'left';
    type = 'default';
    get accordionClasses() {
        return {
            animated: this.animation,
            expanded: this.expanded,
        };
    }
    onAccordionKeydown(event) {
        if (event.key === 'Enter' || event.key === ' ') {
            this.toggleExpand();
            event.preventDefault();
        }
    }
    toggleExpand() {
        if (!this.controlled) {
            this.expanded = !this.expanded;
        }
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AccordionComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AccordionComponent, isStandalone: true, selector: "ava-accordion", inputs: { expanded: "expanded", animation: "animation", controlled: "controlled", iconClosed: "iconClosed", iconOpen: "iconOpen", titleIcon: "titleIcon", iconPosition: "iconPosition", type: "type" }, ngImport: i0, template: "<div class=\"accordion-container\" [ngClass]=\"accordionClasses\">\r\n  <div\r\n    class=\"accordion-header\"\r\n    (click)=\"toggleExpand()\"\r\n    tabindex=\"0\"\r\n    role=\"button\"\r\n    [attr.aria-expanded]=\"expanded\"\r\n    (keydown.enter)=\"toggleExpand()\"\r\n    (keydown.space)=\"toggleExpand()\"\r\n  >\r\n    <div class=\"header-row\">\r\n      <!-- TYPE: titleIcon -->\r\n      <ng-container *ngIf=\"type === 'titleIcon'\">\r\n        <!-- LEFT: Static Title Icon -->\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"titleIcon\"\r\n            class=\"accordion-title-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- DEFAULT ICON LEFT -->\r\n      <ng-container *ngIf=\"type !== 'titleIcon' && iconPosition === 'left'\">\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- TITLE: Always Rendered -->\r\n      <span class=\"accordion-title\">\r\n        <span class=\"accordion-title-highlight\">\r\n          <ng-content select=\"[header]\"></ng-content>\r\n        </span>\r\n      </span>\r\n\r\n      <!-- ICON RIGHT -->\r\n      <ng-container\r\n        *ngIf=\"\r\n          (type !== 'titleIcon' && iconPosition === 'right') ||\r\n          type === 'titleIcon'\r\n        \"\r\n      >\r\n        <span class=\"icon right-aligned-icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"expanded\">\r\n    <div\r\n      class=\"accordion-body\"\r\n      [ngClass]=\"{ 'animated-content': animation, show: expanded }\"\r\n    >\r\n      <ng-content select=\"[content]\"></ng-content>\r\n    </div>\r\n  </div>\r\n</div>\r\n", styles: [".accordion-container{width:100%;max-width:40rem;border-radius:.5rem;overflow:hidden;margin-bottom:1.25rem;background:var(--accordion-light-background);box-shadow:0 .125rem .25rem .0625rem #0000001f}.accordion-container .accordion-header{display:flex;align-items:center;justify-content:space-between;width:40rem;min-height:4.5rem;padding:1.5rem 1.25rem;cursor:pointer;box-sizing:border-box;color:var(--accordion-dark-header-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:500;line-height:var(--accordion-content-font)}.accordion-container .accordion-body{display:flex;width:40rem;padding:0rem 1.25rem 1.5rem 1.5rem;flex-direction:column;justify-content:center;align-items:flex-start;gap:1.5rem;border-radius:.5rem;background:var(--accordion-light-background);color:var(--accordion-dark-content-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:var(--accordion-font-weight);line-height:1.5rem}.accordion-container .accordion-title-highlight{color:var(--accordion-dark-header-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:500;line-height:var(--accordion-content-font)}.accordion-container .header-row{display:flex;align-items:center;gap:.5rem;width:100%}.accordion-container .header-row .accordion-title{flex:1;display:flex;align-items:center}.accordion-container .header-row .icon{display:flex;align-items:center;justify-content:center}.accordion-container .header-row .right-aligned-icon{margin-left:auto}.accordion-container .icon{display:flex;align-items:center;justify-content:center;width:1.5rem;height:1.5rem}.accordion-container .accordion-content.show{animation:slideDown .5s forwards}.accordion-container .accordion-content:not(.show){animation:slideUp .5s forwards}.accordion-icon{width:1.5rem;height:1.5rem}.accordion-container.accordion-dark,.accordion-container.accordion-dark .accordion-title{background:var(--accordion-dark-content-text)}.accordion-container.accordion-dark .accordion-title-highlight,.accordion-container.accordion-dark .accordion-body{background:var(--accordion-dark-content-text);color:var(--accordion-background)}.header-row{display:flex;align-items:center;width:100%}.header-row .accordion-title{flex:1;display:flex;align-items:center}.header-row .icon{display:flex;align-items:center;justify-content:center}.header-row .right-aligned-icon{margin-left:auto}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "ngmodule", type: LucideAngularModule }, { kind: "component", type: i2.LucideAngularComponent, selector: "lucide-angular, lucide-icon, i-lucide, span-lucide", inputs: ["class", "name", "img", "color", "absoluteStrokeWidth", "size", "strokeWidth"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AccordionComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-accordion', imports: [CommonModule, LucideAngularModule], standalone: true, changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"accordion-container\" [ngClass]=\"accordionClasses\">\r\n  <div\r\n    class=\"accordion-header\"\r\n    (click)=\"toggleExpand()\"\r\n    tabindex=\"0\"\r\n    role=\"button\"\r\n    [attr.aria-expanded]=\"expanded\"\r\n    (keydown.enter)=\"toggleExpand()\"\r\n    (keydown.space)=\"toggleExpand()\"\r\n  >\r\n    <div class=\"header-row\">\r\n      <!-- TYPE: titleIcon -->\r\n      <ng-container *ngIf=\"type === 'titleIcon'\">\r\n        <!-- LEFT: Static Title Icon -->\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"titleIcon\"\r\n            class=\"accordion-title-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- DEFAULT ICON LEFT -->\r\n      <ng-container *ngIf=\"type !== 'titleIcon' && iconPosition === 'left'\">\r\n        <span class=\"icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n\r\n      <!-- TITLE: Always Rendered -->\r\n      <span class=\"accordion-title\">\r\n        <span class=\"accordion-title-highlight\">\r\n          <ng-content select=\"[header]\"></ng-content>\r\n        </span>\r\n      </span>\r\n\r\n      <!-- ICON RIGHT -->\r\n      <ng-container\r\n        *ngIf=\"\r\n          (type !== 'titleIcon' && iconPosition === 'right') ||\r\n          type === 'titleIcon'\r\n        \"\r\n      >\r\n        <span class=\"icon right-aligned-icon\">\r\n          <lucide-icon\r\n            [name]=\"expanded ? iconOpen : iconClosed\"\r\n            class=\"accordion-icon\"\r\n            [attr.aria-hidden]=\"true\"\r\n          ></lucide-icon>\r\n        </span>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"expanded\">\r\n    <div\r\n      class=\"accordion-body\"\r\n      [ngClass]=\"{ 'animated-content': animation, show: expanded }\"\r\n    >\r\n      <ng-content select=\"[content]\"></ng-content>\r\n    </div>\r\n  </div>\r\n</div>\r\n", styles: [".accordion-container{width:100%;max-width:40rem;border-radius:.5rem;overflow:hidden;margin-bottom:1.25rem;background:var(--accordion-light-background);box-shadow:0 .125rem .25rem .0625rem #0000001f}.accordion-container .accordion-header{display:flex;align-items:center;justify-content:space-between;width:40rem;min-height:4.5rem;padding:1.5rem 1.25rem;cursor:pointer;box-sizing:border-box;color:var(--accordion-dark-header-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:500;line-height:var(--accordion-content-font)}.accordion-container .accordion-body{display:flex;width:40rem;padding:0rem 1.25rem 1.5rem 1.5rem;flex-direction:column;justify-content:center;align-items:flex-start;gap:1.5rem;border-radius:.5rem;background:var(--accordion-light-background);color:var(--accordion-dark-content-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:var(--accordion-font-weight);line-height:1.5rem}.accordion-container .accordion-title-highlight{color:var(--accordion-dark-header-background);font-family:var(--accordion-font-family);font-size:var(--accordion-content-font);font-style:normal;font-weight:500;line-height:var(--accordion-content-font)}.accordion-container .header-row{display:flex;align-items:center;gap:.5rem;width:100%}.accordion-container .header-row .accordion-title{flex:1;display:flex;align-items:center}.accordion-container .header-row .icon{display:flex;align-items:center;justify-content:center}.accordion-container .header-row .right-aligned-icon{margin-left:auto}.accordion-container .icon{display:flex;align-items:center;justify-content:center;width:1.5rem;height:1.5rem}.accordion-container .accordion-content.show{animation:slideDown .5s forwards}.accordion-container .accordion-content:not(.show){animation:slideUp .5s forwards}.accordion-icon{width:1.5rem;height:1.5rem}.accordion-container.accordion-dark,.accordion-container.accordion-dark .accordion-title{background:var(--accordion-dark-content-text)}.accordion-container.accordion-dark .accordion-title-highlight,.accordion-container.accordion-dark .accordion-body{background:var(--accordion-dark-content-text);color:var(--accordion-background)}.header-row{display:flex;align-items:center;width:100%}.header-row .accordion-title{flex:1;display:flex;align-items:center}.header-row .icon{display:flex;align-items:center;justify-content:center}.header-row .right-aligned-icon{margin-left:auto}\n"] }]
        }], propDecorators: { expanded: [{
                type: Input
            }], animation: [{
                type: Input
            }], controlled: [{
                type: Input
            }], iconClosed: [{
                type: Input
            }], iconOpen: [{
                type: Input
            }], titleIcon: [{
                type: Input
            }], iconPosition: [{
                type: Input
            }], type: [{
                type: Input
            }] } });

class AvaTextboxComponent {
    cdr;
    label = '';
    placeholder = '';
    variant = 'default';
    size = 'md';
    disabled = false;
    readonly = false;
    error = '';
    helper = '';
    icon = '';
    iconPosition = 'start';
    iconColor = 'var(--textbox-icon-color)';
    id = '';
    name = '';
    autocomplete = '';
    type = 'text';
    maxlength;
    minlength;
    required = false;
    fullWidth = false;
    style;
    metaphor = '';
    textboxBlur = new EventEmitter();
    textboxFocus = new EventEmitter();
    textboxInput = new EventEmitter();
    textboxChange = new EventEmitter();
    iconStartClick = new EventEmitter();
    iconEndClick = new EventEmitter();
    value = '';
    isFocused = false;
    onChange = () => { };
    onTouched = () => { };
    constructor(cdr) {
        this.cdr = cdr;
    }
    // ControlValueAccessor implementation
    writeValue(value) {
        this.value = value || '';
        this.cdr.markForCheck();
    }
    registerOnChange(fn) {
        this.onChange = fn;
    }
    registerOnTouched(fn) {
        this.onTouched = fn;
    }
    setDisabledState(isDisabled) {
        this.disabled = isDisabled;
        this.cdr.markForCheck();
    }
    // Event handlers
    onInput(event) {
        const target = event.target;
        this.value = target.value;
        this.onChange(this.value);
        this.textboxInput.emit(event);
    }
    onFocus(event) {
        this.isFocused = true;
        this.textboxFocus.emit(event);
    }
    onBlur(event) {
        this.isFocused = false;
        this.onTouched();
        this.textboxBlur.emit(event);
    }
    onChange_(event) {
        this.textboxChange.emit(event);
    }
    // Icon click handlers
    onIconStartClick(event) {
        if (this.disabled || this.readonly)
            return;
        event.stopPropagation();
        this.iconStartClick.emit(event);
    }
    onIconEndClick(event) {
        if (this.disabled || this.readonly)
            return;
        event.stopPropagation();
        this.iconEndClick.emit(event);
    }
    // Keyboard accessibility for icons
    onIconKeydown(event, position) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            if (position === 'start')
                this.onIconStartClick(event);
            else
                this.onIconEndClick(event);
        }
    }
    // Computed properties
    get hasError() {
        return !!this.error;
    }
    get hasHelper() {
        return !!this.helper && !this.hasError;
    }
    get hasIcon() {
        return !!this.icon;
    }
    hasPrefix = true; // Will be determined by ng-content projection
    hasSuffix = true; // Will be determined by ng-content projection
    get inputId() {
        return this.id || `ava-textbox-${Math.random().toString(36).substr(2, 9)}`;
    }
    get errorId() {
        return `${this.inputId}-error`;
    }
    get helperId() {
        return `${this.inputId}-helper`;
    }
    get ariaDescribedBy() {
        const ids = [];
        if (this.hasError)
            ids.push(this.errorId);
        if (this.hasHelper)
            ids.push(this.helperId);
        return ids.join(' ') || '';
    }
    get inputClasses() {
        const classes = ['ava-textbox__input'];
        if (this.size)
            classes.push(`ava-textbox__input--${this.size}`);
        if (this.variant)
            classes.push(`ava-textbox__input--${this.variant}`);
        if (this.hasError)
            classes.push('ava-textbox__input--error');
        if (this.disabled)
            classes.push('ava-textbox__input--disabled');
        if (this.readonly)
            classes.push('ava-textbox__input--readonly');
        if (this.isFocused)
            classes.push('ava-textbox__input--focused');
        if (this.hasIcon)
            classes.push(`ava-textbox__input--icon-${this.iconPosition}`);
        if (this.fullWidth)
            classes.push('ava-textbox__input--full-width');
        return classes.join(' ');
    }
    get wrapperClasses() {
        const classes = ['ava-textbox'];
        if (this.size)
            classes.push(`ava-textbox--${this.size}`);
        if (this.variant)
            classes.push(`ava-textbox--${this.variant}`);
        if (this.hasError)
            classes.push('ava-textbox--error');
        if (this.disabled)
            classes.push('ava-textbox--disabled');
        if (this.readonly)
            classes.push('ava-textbox--readonly');
        if (this.isFocused)
            classes.push('ava-textbox--focused');
        if (this.fullWidth)
            classes.push('ava-textbox--full-width');
        if (this.metaphor) {
            if (Array.isArray(this.metaphor)) {
                this.metaphor.forEach(m => classes.push(`ava-textbox--${m}`));
            }
            else {
                classes.push(`ava-textbox--${this.metaphor}`);
            }
        }
        return classes.join(' ');
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTextboxComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AvaTextboxComponent, isStandalone: true, selector: "ava-textbox", inputs: { label: "label", placeholder: "placeholder", variant: "variant", size: "size", disabled: "disabled", readonly: "readonly", error: "error", helper: "helper", icon: "icon", iconPosition: "iconPosition", iconColor: "iconColor", id: "id", name: "name", autocomplete: "autocomplete", type: "type", maxlength: "maxlength", minlength: "minlength", required: "required", fullWidth: "fullWidth", style: "style", metaphor: "metaphor" }, outputs: { textboxBlur: "textboxBlur", textboxFocus: "textboxFocus", textboxInput: "textboxInput", textboxChange: "textboxChange", iconStartClick: "iconStartClick", iconEndClick: "iconEndClick" }, providers: [
            {
                provide: NG_VALUE_ACCESSOR,
                useExisting: forwardRef(() => AvaTextboxComponent),
                multi: true,
            },
        ], ngImport: i0, template: "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textbox__label\"\r\n    [class.ava-textbox__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textbox__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textbox__container\" [ngStyle]=\"style\">\r\n    <!-- Prefix Slot -->\r\n    <div class=\"ava-textbox__prefix\" *ngIf=\"hasPrefix\">\r\n      <ng-content select=\"[slot=prefix]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Start Projected Icons (before input/textarea) -->\r\n    <div class=\"ava-textbox__icons ava-textbox__icons--start\">\r\n      <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Input Field -->\r\n    <input\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [type]=\"type\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [autocomplete]=\"autocomplete\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    />\r\n\r\n    <!-- End Projected Icons (before input/textarea) -->\r\n    <div class=\"ava-textbox__icons ava-textbox__icons--end\">\r\n      <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Suffix Slot -->\r\n    <div class=\"ava-textbox__suffix\" *ngIf=\"hasSuffix\">\r\n      <ng-content select=\"[slot=suffix]\"></ng-content>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textbox__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textbox__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", styles: [".ava-textbox{display:flex;flex-direction:column;gap:var(--textbox-gap);width:100%}.ava-textbox--full-width{width:100%}.ava-textbox--sm{gap:var(--textbox-gap-sm)}.ava-textbox--lg{gap:var(--textbox-gap-lg)}.ava-textbox__label{display:block;font:var(--textbox-label-font, 500 1rem/1.5 \"Inter\", sans-serif);color:var(--textbox-label-color, var(--color-text-primary))}.ava-textbox__label--required:after{content:\"\"}.ava-textbox__required{color:var(--textbox-required-color, var(--color-text-error));margin-left:.25rem}.ava-textbox__container{position:relative;display:flex;align-items:center;background:var(--textbox-background);border:var(--textbox-border-width) solid var(--textbox-border-color);border-radius:var(--textbox-border-radius);transition:border-color var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30)),box-shadow var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30))}.ava-textbox__container:focus-within{border-color:var(--textbox-focus-border-color);box-shadow:var(--textbox-focus-shadow)}.ava-textbox__container:hover:not(:focus-within){border-color:var(--textbox-hover-border-color)}.ava-textbox--disabled .ava-textbox__container{background:var(--textbox-background-disabled);border-color:var(--textbox-border-disabled-color);cursor:not-allowed}.ava-textbox--readonly .ava-textbox__container{background:var(--textbox-background-readonly);border-color:var(--textbox-border-readonly-color)}.ava-textbox--primary .ava-textbox__container{border-color:var(--textbox-border-primary-color)}.ava-textbox--success .ava-textbox__container{border-color:var(--textbox-border-success-color)}.ava-textbox--warning .ava-textbox__container{border-color:var(--textbox-border-warning-color)}.ava-textbox--info .ava-textbox__container{border-color:var(--textbox-border-info-color)}.ava-textbox__input{flex:1;border:none;outline:none;background:transparent;font-family:var(--textbox-input-font-family, \"Inter\", sans-serif);font-size:var(--textbox-input-font-size);color:var(--textbox-input-color);padding:var(--textbox-input-padding);min-height:var(--textbox-input-min-height);line-height:1.5;resize:none;font-weight:var(--textbox-label-weight);height:40px;box-sizing:border-box}.ava-textbox__input::placeholder{color:var(--textbox-placeholder-color);opacity:1}.ava-textbox__input:disabled{color:var(--textbox-input-disabled-color);cursor:not-allowed}.ava-textbox__input:read-only{color:var(--textbox-input-readonly-color);cursor:default}.ava-textbox__input--sm{padding:var(--textbox-input-padding-sm);min-height:var(--textbox-input-min-height-sm);font-size:var(--textbox-input-font-size-sm)}.ava-textbox__input--lg{padding:var(--textbox-input-padding-lg);min-height:var(--textbox-input-min-height-lg);font-size:var(--textbox-input-font-size-lg)}.ava-textbox__input--icon-start{padding-left:var(--textbox-input-icon-padding-start)}.ava-textbox__input--icon-end{padding-right:var(--textbox-input-icon-padding-end)}.ava-textbox__input--full-width{width:100%}.ava-textbox__icon{position:absolute;display:flex;align-items:center;justify-content:center;color:var(--textbox-icon-color);z-index:1;cursor:pointer;transition:color .2s;height:100%;top:0}.ava-textbox__icon:hover,.ava-textbox__icon:focus{color:var(--textbox-icon-focus-color);outline:none}.ava-textbox--disabled .ava-textbox__icon,.ava-textbox--readonly .ava-textbox__icon{color:var(--textbox-icon-disabled-color);cursor:not-allowed}.ava-textbox__icon--start{start:var(--textbox-icon-position-start)}.ava-textbox__icon--end{end:var(--textbox-icon-position-end)}.ava-textbox__icon .ava-icon-container{display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0;vertical-align:middle}.ava-textbox__prefix,.ava-textbox__suffix{display:flex;align-items:center;padding:var(--textbox-affix-padding);color:var(--textbox-affix-color);font-size:var(--textbox-affix-font-size);background:var(--textbox-affix-background);border-radius:var(--textbox-affix-border-radius)}.ava-textbox--disabled .ava-textbox__prefix,.ava-textbox--disabled .ava-textbox__suffix{color:var(--textbox-affix-disabled-color);background:var(--textbox-affix-disabled-background)}.ava-textbox__prefix{border-top-left-radius:var(--textbox-border-radius);border-bottom-left-radius:var(--textbox-border-radius)}.ava-textbox__suffix{border-top-right-radius:var(--textbox-border-radius);border-bottom-right-radius:var(--textbox-border-radius)}.ava-textbox__error{display:flex;align-items:flex-start;gap:var(--textbox-error-gap);color:var(--textbox-error-color);font-size:var(--textbox-error-font-size);line-height:1.4}.ava-textbox__error-icon{flex-shrink:0;margin-top:.125rem}.ava-textbox__error-text{flex:1}.ava-textbox__helper{display:flex;align-items:flex-start;gap:var(--textbox-helper-gap);color:var(--textbox-helper-color);font-size:var(--textbox-helper-font-size);line-height:1.4}.ava-textbox__helper-icon{flex-shrink:0;margin-top:.125rem}.ava-textbox__helper-text{flex:1}.ava-textbox__icons{display:flex;align-items:center;gap:.5rem;height:100%}.ava-textbox__icons--start{margin-right:.5rem}.ava-textbox__icons--end{margin-left:.5rem;gap:.5rem}.ava-textbox__icons ava-icon{cursor:pointer;transition:color .18s}.ava-textbox__icons ava-icon:active{opacity:.7}.ava-textbox__container.multiline .ava-textbox__icons--start{position:absolute;left:.75rem;bottom:.5rem;z-index:2;background:transparent;align-items:flex-end}.ava-textbox__container.multiline .ava-textbox__icons--end{position:absolute;right:.75rem;bottom:.5rem;z-index:2;background:transparent;align-items:flex-end}.ava-textbox--surface-0 .ava-textbox__container{--textbox-background: var(--textbox-surface-0);-webkit-backdrop-filter:var(--textbox-surface-blur-0);backdrop-filter:var(--textbox-surface-blur-0)}.ava-textbox--light-0 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-0)}.ava-textbox--motion-0 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-0) var(--textbox-motion-0),border-color var(--textbox-motion-duration-0) var(--textbox-motion-0)}.ava-textbox--surface-10 .ava-textbox__container{--textbox-background: var(--textbox-surface-10);-webkit-backdrop-filter:var(--textbox-surface-blur-10);backdrop-filter:var(--textbox-surface-blur-10)}.ava-textbox--light-10 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-10)}.ava-textbox--motion-10 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-10) var(--textbox-motion-10),border-color var(--textbox-motion-duration-10) var(--textbox-motion-10)}.ava-textbox--surface-20 .ava-textbox__container{--textbox-background: var(--textbox-surface-20);-webkit-backdrop-filter:var(--textbox-surface-blur-20);backdrop-filter:var(--textbox-surface-blur-20)}.ava-textbox--light-20 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-20)}.ava-textbox--motion-20 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-20) var(--textbox-motion-20),border-color var(--textbox-motion-duration-20) var(--textbox-motion-20)}.ava-textbox--surface-30 .ava-textbox__container{--textbox-background: var(--textbox-surface-30);-webkit-backdrop-filter:var(--textbox-surface-blur-30);backdrop-filter:var(--textbox-surface-blur-30)}.ava-textbox--light-30 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-30)}.ava-textbox--motion-30 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-30) var(--textbox-motion-30),border-color var(--textbox-motion-duration-30) var(--textbox-motion-30)}.ava-textbox--surface-40 .ava-textbox__container{--textbox-background: var(--textbox-surface-40);-webkit-backdrop-filter:var(--textbox-surface-blur-40);backdrop-filter:var(--textbox-surface-blur-40)}.ava-textbox--light-40 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-40)}.ava-textbox--motion-40 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-40) var(--textbox-motion-40),border-color var(--textbox-motion-duration-40) var(--textbox-motion-40)}.ava-textbox--surface-50 .ava-textbox__container{--textbox-background: var(--textbox-surface-50);-webkit-backdrop-filter:var(--textbox-surface-blur-50);backdrop-filter:var(--textbox-surface-blur-50)}.ava-textbox--light-50 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-50)}.ava-textbox--motion-50 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-50) var(--textbox-motion-50),border-color var(--textbox-motion-duration-50) var(--textbox-motion-50)}.ava-textbox--surface-60 .ava-textbox__container{--textbox-background: var(--textbox-surface-60);-webkit-backdrop-filter:var(--textbox-surface-blur-60);backdrop-filter:var(--textbox-surface-blur-60)}.ava-textbox--light-60 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-60)}.ava-textbox--motion-60 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-60) var(--textbox-motion-60),border-color var(--textbox-motion-duration-60) var(--textbox-motion-60)}.ava-textbox--surface-70 .ava-textbox__container{--textbox-background: var(--textbox-surface-70);-webkit-backdrop-filter:var(--textbox-surface-blur-70);backdrop-filter:var(--textbox-surface-blur-70)}.ava-textbox--light-70 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-70)}.ava-textbox--motion-70 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-70) var(--textbox-motion-70),border-color var(--textbox-motion-duration-70) var(--textbox-motion-70)}.ava-textbox--surface-80 .ava-textbox__container{--textbox-background: var(--textbox-surface-80);-webkit-backdrop-filter:var(--textbox-surface-blur-80);backdrop-filter:var(--textbox-surface-blur-80)}.ava-textbox--light-80 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-80)}.ava-textbox--motion-80 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-80) var(--textbox-motion-80),border-color var(--textbox-motion-duration-80) var(--textbox-motion-80)}.ava-textbox--surface-90 .ava-textbox__container{--textbox-background: var(--textbox-surface-90);-webkit-backdrop-filter:var(--textbox-surface-blur-90);backdrop-filter:var(--textbox-surface-blur-90)}.ava-textbox--light-90 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-90)}.ava-textbox--motion-90 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-90) var(--textbox-motion-90),border-color var(--textbox-motion-duration-90) var(--textbox-motion-90)}.ava-textbox--surface-100 .ava-textbox__container{--textbox-background: var(--textbox-surface-100);-webkit-backdrop-filter:var(--textbox-surface-blur-100);backdrop-filter:var(--textbox-surface-blur-100)}.ava-textbox--light-100 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-100)}.ava-textbox--motion-100 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-100) var(--textbox-motion-100),border-color var(--textbox-motion-duration-100) var(--textbox-motion-100)}.ava-textbox--gradient-0 .ava-textbox__container{background-image:var(--textbox-gradient-0)}.ava-textbox--gradient-20 .ava-textbox__container{background-image:var(--textbox-gradient-20)}.ava-textbox--gradient-50 .ava-textbox__container{background-image:var(--textbox-gradient-50)}.ava-textbox--gradient-80 .ava-textbox__container{background-image:var(--textbox-gradient-80)}.ava-textbox--gradient-100 .ava-textbox__container{background-image:var(--textbox-gradient-100)}.ava-textbox--gradient-glass-50 .ava-textbox__container:before{content:\"\";position:absolute;inset:0;background:var(--textbox-gradient-glass-50);border-radius:inherit;pointer-events:none;z-index:1}.ava-textbox--gradient-glass-100 .ava-textbox__container:before{content:\"\";position:absolute;inset:0;background:var(--textbox-gradient-glass-100);border-radius:inherit;pointer-events:none;z-index:1}.ava-textbox--light-ring-20 .ava-textbox__container:focus-within,.ava-textbox--light-ring-20.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-20)}.ava-textbox--light-ring-50 .ava-textbox__container:focus-within,.ava-textbox--light-ring-50.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-50)}.ava-textbox--light-ring-100 .ava-textbox__container:focus-within,.ava-textbox--light-ring-100.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-100)}.ava-textbox__container .ava-textbox__input{position:relative;z-index:2}.ava-textbox--glass .ava-textbox__container{--textbox-background: var(--textbox-surface-50);-webkit-backdrop-filter:var(--textbox-surface-blur-50);backdrop-filter:var(--textbox-surface-blur-50)}.ava-textbox--light .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-50)}.ava-textbox--liquid .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-60) var(--textbox-motion-60),border-color var(--textbox-motion-duration-60) var(--textbox-motion-60)}.ava-textbox--gradient .ava-textbox__container{background-image:var(--textbox-gradient-50)}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], animations: [
            trigger('fadeIcon', [
                transition(':enter', [
                    style({ opacity: 0, transform: 'scale(0.8)' }),
                    animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 1, transform: 'scale(1)' }))
                ]),
                transition(':leave', [
                    animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 0, transform: 'scale(0.8)' }))
                ])
            ])
        ], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTextboxComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-textbox', standalone: true, imports: [CommonModule, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, providers: [
                        {
                            provide: NG_VALUE_ACCESSOR,
                            useExisting: forwardRef(() => AvaTextboxComponent),
                            multi: true,
                        },
                    ], schemas: [CUSTOM_ELEMENTS_SCHEMA], animations: [
                        trigger('fadeIcon', [
                            transition(':enter', [
                                style({ opacity: 0, transform: 'scale(0.8)' }),
                                animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 1, transform: 'scale(1)' }))
                            ]),
                            transition(':leave', [
                                animate('180ms cubic-bezier(0.4,0,0.2,1)', style({ opacity: 0, transform: 'scale(0.8)' }))
                            ])
                        ])
                    ], template: "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textbox__label\"\r\n    [class.ava-textbox__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textbox__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textbox__container\" [ngStyle]=\"style\">\r\n    <!-- Prefix Slot -->\r\n    <div class=\"ava-textbox__prefix\" *ngIf=\"hasPrefix\">\r\n      <ng-content select=\"[slot=prefix]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Start Projected Icons (before input/textarea) -->\r\n    <div class=\"ava-textbox__icons ava-textbox__icons--start\">\r\n      <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Input Field -->\r\n    <input\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [type]=\"type\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [autocomplete]=\"autocomplete\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    />\r\n\r\n    <!-- End Projected Icons (before input/textarea) -->\r\n    <div class=\"ava-textbox__icons ava-textbox__icons--end\">\r\n      <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n    </div>\r\n\r\n    <!-- Suffix Slot -->\r\n    <div class=\"ava-textbox__suffix\" *ngIf=\"hasSuffix\">\r\n      <ng-content select=\"[slot=suffix]\"></ng-content>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textbox__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textbox__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textbox__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textbox__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", styles: [".ava-textbox{display:flex;flex-direction:column;gap:var(--textbox-gap);width:100%}.ava-textbox--full-width{width:100%}.ava-textbox--sm{gap:var(--textbox-gap-sm)}.ava-textbox--lg{gap:var(--textbox-gap-lg)}.ava-textbox__label{display:block;font:var(--textbox-label-font, 500 1rem/1.5 \"Inter\", sans-serif);color:var(--textbox-label-color, var(--color-text-primary))}.ava-textbox__label--required:after{content:\"\"}.ava-textbox__required{color:var(--textbox-required-color, var(--color-text-error));margin-left:.25rem}.ava-textbox__container{position:relative;display:flex;align-items:center;background:var(--textbox-background);border:var(--textbox-border-width) solid var(--textbox-border-color);border-radius:var(--textbox-border-radius);transition:border-color var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30)),box-shadow var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30))}.ava-textbox__container:focus-within{border-color:var(--textbox-focus-border-color);box-shadow:var(--textbox-focus-shadow)}.ava-textbox__container:hover:not(:focus-within){border-color:var(--textbox-hover-border-color)}.ava-textbox--disabled .ava-textbox__container{background:var(--textbox-background-disabled);border-color:var(--textbox-border-disabled-color);cursor:not-allowed}.ava-textbox--readonly .ava-textbox__container{background:var(--textbox-background-readonly);border-color:var(--textbox-border-readonly-color)}.ava-textbox--primary .ava-textbox__container{border-color:var(--textbox-border-primary-color)}.ava-textbox--success .ava-textbox__container{border-color:var(--textbox-border-success-color)}.ava-textbox--warning .ava-textbox__container{border-color:var(--textbox-border-warning-color)}.ava-textbox--info .ava-textbox__container{border-color:var(--textbox-border-info-color)}.ava-textbox__input{flex:1;border:none;outline:none;background:transparent;font-family:var(--textbox-input-font-family, \"Inter\", sans-serif);font-size:var(--textbox-input-font-size);color:var(--textbox-input-color);padding:var(--textbox-input-padding);min-height:var(--textbox-input-min-height);line-height:1.5;resize:none;font-weight:var(--textbox-label-weight);height:40px;box-sizing:border-box}.ava-textbox__input::placeholder{color:var(--textbox-placeholder-color);opacity:1}.ava-textbox__input:disabled{color:var(--textbox-input-disabled-color);cursor:not-allowed}.ava-textbox__input:read-only{color:var(--textbox-input-readonly-color);cursor:default}.ava-textbox__input--sm{padding:var(--textbox-input-padding-sm);min-height:var(--textbox-input-min-height-sm);font-size:var(--textbox-input-font-size-sm)}.ava-textbox__input--lg{padding:var(--textbox-input-padding-lg);min-height:var(--textbox-input-min-height-lg);font-size:var(--textbox-input-font-size-lg)}.ava-textbox__input--icon-start{padding-left:var(--textbox-input-icon-padding-start)}.ava-textbox__input--icon-end{padding-right:var(--textbox-input-icon-padding-end)}.ava-textbox__input--full-width{width:100%}.ava-textbox__icon{position:absolute;display:flex;align-items:center;justify-content:center;color:var(--textbox-icon-color);z-index:1;cursor:pointer;transition:color .2s;height:100%;top:0}.ava-textbox__icon:hover,.ava-textbox__icon:focus{color:var(--textbox-icon-focus-color);outline:none}.ava-textbox--disabled .ava-textbox__icon,.ava-textbox--readonly .ava-textbox__icon{color:var(--textbox-icon-disabled-color);cursor:not-allowed}.ava-textbox__icon--start{start:var(--textbox-icon-position-start)}.ava-textbox__icon--end{end:var(--textbox-icon-position-end)}.ava-textbox__icon .ava-icon-container{display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0;vertical-align:middle}.ava-textbox__prefix,.ava-textbox__suffix{display:flex;align-items:center;padding:var(--textbox-affix-padding);color:var(--textbox-affix-color);font-size:var(--textbox-affix-font-size);background:var(--textbox-affix-background);border-radius:var(--textbox-affix-border-radius)}.ava-textbox--disabled .ava-textbox__prefix,.ava-textbox--disabled .ava-textbox__suffix{color:var(--textbox-affix-disabled-color);background:var(--textbox-affix-disabled-background)}.ava-textbox__prefix{border-top-left-radius:var(--textbox-border-radius);border-bottom-left-radius:var(--textbox-border-radius)}.ava-textbox__suffix{border-top-right-radius:var(--textbox-border-radius);border-bottom-right-radius:var(--textbox-border-radius)}.ava-textbox__error{display:flex;align-items:flex-start;gap:var(--textbox-error-gap);color:var(--textbox-error-color);font-size:var(--textbox-error-font-size);line-height:1.4}.ava-textbox__error-icon{flex-shrink:0;margin-top:.125rem}.ava-textbox__error-text{flex:1}.ava-textbox__helper{display:flex;align-items:flex-start;gap:var(--textbox-helper-gap);color:var(--textbox-helper-color);font-size:var(--textbox-helper-font-size);line-height:1.4}.ava-textbox__helper-icon{flex-shrink:0;margin-top:.125rem}.ava-textbox__helper-text{flex:1}.ava-textbox__icons{display:flex;align-items:center;gap:.5rem;height:100%}.ava-textbox__icons--start{margin-right:.5rem}.ava-textbox__icons--end{margin-left:.5rem;gap:.5rem}.ava-textbox__icons ava-icon{cursor:pointer;transition:color .18s}.ava-textbox__icons ava-icon:active{opacity:.7}.ava-textbox__container.multiline .ava-textbox__icons--start{position:absolute;left:.75rem;bottom:.5rem;z-index:2;background:transparent;align-items:flex-end}.ava-textbox__container.multiline .ava-textbox__icons--end{position:absolute;right:.75rem;bottom:.5rem;z-index:2;background:transparent;align-items:flex-end}.ava-textbox--surface-0 .ava-textbox__container{--textbox-background: var(--textbox-surface-0);-webkit-backdrop-filter:var(--textbox-surface-blur-0);backdrop-filter:var(--textbox-surface-blur-0)}.ava-textbox--light-0 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-0)}.ava-textbox--motion-0 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-0) var(--textbox-motion-0),border-color var(--textbox-motion-duration-0) var(--textbox-motion-0)}.ava-textbox--surface-10 .ava-textbox__container{--textbox-background: var(--textbox-surface-10);-webkit-backdrop-filter:var(--textbox-surface-blur-10);backdrop-filter:var(--textbox-surface-blur-10)}.ava-textbox--light-10 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-10)}.ava-textbox--motion-10 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-10) var(--textbox-motion-10),border-color var(--textbox-motion-duration-10) var(--textbox-motion-10)}.ava-textbox--surface-20 .ava-textbox__container{--textbox-background: var(--textbox-surface-20);-webkit-backdrop-filter:var(--textbox-surface-blur-20);backdrop-filter:var(--textbox-surface-blur-20)}.ava-textbox--light-20 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-20)}.ava-textbox--motion-20 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-20) var(--textbox-motion-20),border-color var(--textbox-motion-duration-20) var(--textbox-motion-20)}.ava-textbox--surface-30 .ava-textbox__container{--textbox-background: var(--textbox-surface-30);-webkit-backdrop-filter:var(--textbox-surface-blur-30);backdrop-filter:var(--textbox-surface-blur-30)}.ava-textbox--light-30 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-30)}.ava-textbox--motion-30 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-30) var(--textbox-motion-30),border-color var(--textbox-motion-duration-30) var(--textbox-motion-30)}.ava-textbox--surface-40 .ava-textbox__container{--textbox-background: var(--textbox-surface-40);-webkit-backdrop-filter:var(--textbox-surface-blur-40);backdrop-filter:var(--textbox-surface-blur-40)}.ava-textbox--light-40 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-40)}.ava-textbox--motion-40 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-40) var(--textbox-motion-40),border-color var(--textbox-motion-duration-40) var(--textbox-motion-40)}.ava-textbox--surface-50 .ava-textbox__container{--textbox-background: var(--textbox-surface-50);-webkit-backdrop-filter:var(--textbox-surface-blur-50);backdrop-filter:var(--textbox-surface-blur-50)}.ava-textbox--light-50 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-50)}.ava-textbox--motion-50 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-50) var(--textbox-motion-50),border-color var(--textbox-motion-duration-50) var(--textbox-motion-50)}.ava-textbox--surface-60 .ava-textbox__container{--textbox-background: var(--textbox-surface-60);-webkit-backdrop-filter:var(--textbox-surface-blur-60);backdrop-filter:var(--textbox-surface-blur-60)}.ava-textbox--light-60 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-60)}.ava-textbox--motion-60 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-60) var(--textbox-motion-60),border-color var(--textbox-motion-duration-60) var(--textbox-motion-60)}.ava-textbox--surface-70 .ava-textbox__container{--textbox-background: var(--textbox-surface-70);-webkit-backdrop-filter:var(--textbox-surface-blur-70);backdrop-filter:var(--textbox-surface-blur-70)}.ava-textbox--light-70 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-70)}.ava-textbox--motion-70 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-70) var(--textbox-motion-70),border-color var(--textbox-motion-duration-70) var(--textbox-motion-70)}.ava-textbox--surface-80 .ava-textbox__container{--textbox-background: var(--textbox-surface-80);-webkit-backdrop-filter:var(--textbox-surface-blur-80);backdrop-filter:var(--textbox-surface-blur-80)}.ava-textbox--light-80 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-80)}.ava-textbox--motion-80 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-80) var(--textbox-motion-80),border-color var(--textbox-motion-duration-80) var(--textbox-motion-80)}.ava-textbox--surface-90 .ava-textbox__container{--textbox-background: var(--textbox-surface-90);-webkit-backdrop-filter:var(--textbox-surface-blur-90);backdrop-filter:var(--textbox-surface-blur-90)}.ava-textbox--light-90 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-90)}.ava-textbox--motion-90 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-90) var(--textbox-motion-90),border-color var(--textbox-motion-duration-90) var(--textbox-motion-90)}.ava-textbox--surface-100 .ava-textbox__container{--textbox-background: var(--textbox-surface-100);-webkit-backdrop-filter:var(--textbox-surface-blur-100);backdrop-filter:var(--textbox-surface-blur-100)}.ava-textbox--light-100 .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-100)}.ava-textbox--motion-100 .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-100) var(--textbox-motion-100),border-color var(--textbox-motion-duration-100) var(--textbox-motion-100)}.ava-textbox--gradient-0 .ava-textbox__container{background-image:var(--textbox-gradient-0)}.ava-textbox--gradient-20 .ava-textbox__container{background-image:var(--textbox-gradient-20)}.ava-textbox--gradient-50 .ava-textbox__container{background-image:var(--textbox-gradient-50)}.ava-textbox--gradient-80 .ava-textbox__container{background-image:var(--textbox-gradient-80)}.ava-textbox--gradient-100 .ava-textbox__container{background-image:var(--textbox-gradient-100)}.ava-textbox--gradient-glass-50 .ava-textbox__container:before{content:\"\";position:absolute;inset:0;background:var(--textbox-gradient-glass-50);border-radius:inherit;pointer-events:none;z-index:1}.ava-textbox--gradient-glass-100 .ava-textbox__container:before{content:\"\";position:absolute;inset:0;background:var(--textbox-gradient-glass-100);border-radius:inherit;pointer-events:none;z-index:1}.ava-textbox--light-ring-20 .ava-textbox__container:focus-within,.ava-textbox--light-ring-20.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-20)}.ava-textbox--light-ring-50 .ava-textbox__container:focus-within,.ava-textbox--light-ring-50.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-50)}.ava-textbox--light-ring-100 .ava-textbox__container:focus-within,.ava-textbox--light-ring-100.ava-textbox--focused .ava-textbox__container{box-shadow:var(--textbox-light-ring-100)}.ava-textbox__container .ava-textbox__input{position:relative;z-index:2}.ava-textbox--glass .ava-textbox__container{--textbox-background: var(--textbox-surface-50);-webkit-backdrop-filter:var(--textbox-surface-blur-50);backdrop-filter:var(--textbox-surface-blur-50)}.ava-textbox--light .ava-textbox__container{--textbox-focus-shadow: var(--textbox-light-50)}.ava-textbox--liquid .ava-textbox__container{transition:box-shadow var(--textbox-motion-duration-60) var(--textbox-motion-60),border-color var(--textbox-motion-duration-60) var(--textbox-motion-60)}.ava-textbox--gradient .ava-textbox__container{background-image:var(--textbox-gradient-50)}\n"] }]
        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { label: [{
                type: Input
            }], placeholder: [{
                type: Input
            }], variant: [{
                type: Input
            }], size: [{
                type: Input
            }], disabled: [{
                type: Input
            }], readonly: [{
                type: Input
            }], error: [{
                type: Input
            }], helper: [{
                type: Input
            }], icon: [{
                type: Input
            }], iconPosition: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], id: [{
                type: Input
            }], name: [{
                type: Input
            }], autocomplete: [{
                type: Input
            }], type: [{
                type: Input
            }], maxlength: [{
                type: Input
            }], minlength: [{
                type: Input
            }], required: [{
                type: Input
            }], fullWidth: [{
                type: Input
            }], style: [{
                type: Input
            }], metaphor: [{
                type: Input
            }], textboxBlur: [{
                type: Output
            }], textboxFocus: [{
                type: Output
            }], textboxInput: [{
                type: Output
            }], textboxChange: [{
                type: Output
            }], iconStartClick: [{
                type: Output
            }], iconEndClick: [{
                type: Output
            }] } });

class BadgesComponent {
    state = 'neutral';
    size = 'medium';
    count;
    iconName;
    iconColor;
    iconSize;
    get displayCount() {
        if (!this.count)
            return '';
        if (this.count > 999)
            return '999+';
        if (this.count > 99)
            return '99+';
        if (this.count > 9)
            return '9+';
        return this.count.toString();
    }
    get badgeClasses() {
        const baseClasses = `badge badge--${this.state} badge--${this.size}`;
        // Add expanded class only for multi-character content
        if (this.count && this.displayCount.length > 1) {
            return `${baseClasses} badge--expanded`;
        }
        return baseClasses;
    }
    get hasContent() {
        return !!(this.count || this.iconName);
    }
    get isSingleDigit() {
        return this.count !== undefined && this.count >= 0 && this.count <= 9;
    }
    onKeyPress() {
        // Handle the key press event, e.g., trigger an action or navigate
        console.log('Badge component pressed via keyboard');
        // Add your custom logic here
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: BadgesComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: BadgesComponent, isStandalone: true, selector: "ava-badges", inputs: { state: "state", size: "size", count: "count", iconName: "iconName", iconColor: "iconColor", iconSize: "iconSize" }, ngImport: i0, template: "<div\r\n  [class]=\"badgeClasses\"\r\n  tabindex=\"0\"\r\n  (keydown.enter)=\"onKeyPress()\"\r\n  (keydown.space)=\"onKeyPress()\"\r\n>\r\n  <ng-container *ngIf=\"hasContent\">\r\n    <!-- Display ava-icon if iconName is provided and no count -->\r\n    <ng-container *ngIf=\"iconName && !count\">\r\n      <ava-icon\r\n        class=\"badge__icon\"\r\n        [iconName]=\"iconName\"\r\n        [iconColor]=\"iconColor!\"\r\n        [iconSize]=\"iconSize!\"\r\n      >\r\n      </ava-icon>\r\n    </ng-container>\r\n    <!-- Display count if provided -->\r\n    <span *ngIf=\"count\" class=\"badge__count\">{{ displayCount }}</span>\r\n  </ng-container>\r\n</div>\r\n", styles: [":host{display:inline-block}.badge{display:flex;flex-direction:column;justify-content:var(--badge-justify-content);align-items:var(--badge-align-items);gap:10px;flex-shrink:0;border-radius:var(--Round, 1000px);font-weight:var(--badge-weight);font-family:system-ui,-apple-system,sans-serif;box-sizing:border-box}.badge--large{width:var(--badge-size-lg-min-width);height:var(--badge-size-lg-height);padding:12px;font-size:var(--badge-size-lg-font);line-height:1}.badge--medium{width:var(--badge-size-md-min-width);height:var(--badge-size-md-height);padding:8px;font-size:var(--badge-size-md-font);line-height:1}.badge--small{width:var(--badge-size-sm-min-width);height:var(--badge-size-sm-height);padding:4px;font-size:14px;line-height:1}.badge--high-priority{background:var(--badge-error-background);color:var(--badge-error-text)}.badge--medium-priority{background:var(--badge-warning-background);color:var(--badge-warning-text)}.badge--low-priority{background:var(--badge-success-background);color:var(--badge-success-text)}.badge--neutral{background:var(--badge-default-background);color:var(--badge-default-text)}.badge--information{background:var(--badge-info-background);color:var(--badge-info-text)}.badge__count{font-weight:600;text-align:center;white-space:nowrap}.badge__icon{display:flex;align-items:center;justify-content:center;line-height:1}.badge--expanded{width:auto!important}.badge--expanded.badge--large{min-width:var(--badge-size-lg-min-width);padding-left:4px;padding-right:4px}.badge--expanded.badge--medium{min-width:var(--badge-size-md-min-width);padding-left:4px!important;padding-right:4px!important}.badge--expanded.badge--small{min-width:var(--badge-size-sm-min-width);padding-left:4px;padding-right:4px}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: BadgesComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-badges', imports: [CommonModule, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div\r\n  [class]=\"badgeClasses\"\r\n  tabindex=\"0\"\r\n  (keydown.enter)=\"onKeyPress()\"\r\n  (keydown.space)=\"onKeyPress()\"\r\n>\r\n  <ng-container *ngIf=\"hasContent\">\r\n    <!-- Display ava-icon if iconName is provided and no count -->\r\n    <ng-container *ngIf=\"iconName && !count\">\r\n      <ava-icon\r\n        class=\"badge__icon\"\r\n        [iconName]=\"iconName\"\r\n        [iconColor]=\"iconColor!\"\r\n        [iconSize]=\"iconSize!\"\r\n      >\r\n      </ava-icon>\r\n    </ng-container>\r\n    <!-- Display count if provided -->\r\n    <span *ngIf=\"count\" class=\"badge__count\">{{ displayCount }}</span>\r\n  </ng-container>\r\n</div>\r\n", styles: [":host{display:inline-block}.badge{display:flex;flex-direction:column;justify-content:var(--badge-justify-content);align-items:var(--badge-align-items);gap:10px;flex-shrink:0;border-radius:var(--Round, 1000px);font-weight:var(--badge-weight);font-family:system-ui,-apple-system,sans-serif;box-sizing:border-box}.badge--large{width:var(--badge-size-lg-min-width);height:var(--badge-size-lg-height);padding:12px;font-size:var(--badge-size-lg-font);line-height:1}.badge--medium{width:var(--badge-size-md-min-width);height:var(--badge-size-md-height);padding:8px;font-size:var(--badge-size-md-font);line-height:1}.badge--small{width:var(--badge-size-sm-min-width);height:var(--badge-size-sm-height);padding:4px;font-size:14px;line-height:1}.badge--high-priority{background:var(--badge-error-background);color:var(--badge-error-text)}.badge--medium-priority{background:var(--badge-warning-background);color:var(--badge-warning-text)}.badge--low-priority{background:var(--badge-success-background);color:var(--badge-success-text)}.badge--neutral{background:var(--badge-default-background);color:var(--badge-default-text)}.badge--information{background:var(--badge-info-background);color:var(--badge-info-text)}.badge__count{font-weight:600;text-align:center;white-space:nowrap}.badge__icon{display:flex;align-items:center;justify-content:center;line-height:1}.badge--expanded{width:auto!important}.badge--expanded.badge--large{min-width:var(--badge-size-lg-min-width);padding-left:4px;padding-right:4px}.badge--expanded.badge--medium{min-width:var(--badge-size-md-min-width);padding-left:4px!important;padding-right:4px!important}.badge--expanded.badge--small{min-width:var(--badge-size-sm-min-width);padding-left:4px;padding-right:4px}\n"] }]
        }], propDecorators: { state: [{
                type: Input
            }], size: [{
                type: Input
            }], count: [{
                type: Input
            }], iconName: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], iconSize: [{
                type: Input
            }] } });

class AvatarsComponent {
    size = 'large';
    shape = 'pill';
    imageUrl = '';
    statusText;
    profileText;
    badgeState;
    badgeSize;
    badgeCount;
    get avatarClasses() {
        return `avatar avatar--${this.size} avatar--${this.shape}`;
    }
    get hasBadge() {
        return !!(this.badgeState || this.badgeCount);
    }
    get hasStatusText() {
        return !!this.statusText;
    }
    get hasProfileText() {
        return !!this.profileText;
    }
    get hasAnyText() {
        return this.hasStatusText || this.hasProfileText;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvatarsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AvatarsComponent, isStandalone: true, selector: "ava-avatars", inputs: { size: "size", shape: "shape", imageUrl: "imageUrl", statusText: "statusText", profileText: "profileText", badgeState: "badgeState", badgeSize: "badgeSize", badgeCount: "badgeCount" }, ngImport: i0, template: "<div class=\"avatar-container\">\r\n  <div class=\"avatar-wrapper\">\r\n    <div\r\n      [class]=\"avatarClasses\"\r\n      [style.background-image]=\"imageUrl ? 'url(' + imageUrl + ')' : 'none'\"\r\n    >\r\n      <ava-badges\r\n        *ngIf=\"hasBadge\"\r\n        [state]=\"badgeState!\"\r\n        [size]=\"badgeSize!\"\r\n        [count]=\"badgeCount\"\r\n        class=\"avatar-badge\"\r\n      >\r\n      </ava-badges>\r\n    </div>\r\n    <!-- Text labels - can have both status and profile -->\r\n    <div *ngIf=\"hasAnyText\" class=\"avatar-text-container\">\r\n      <div *ngIf=\"hasStatusText\" class=\"avatar-text avatar-text--status\">\r\n        {{ statusText }}\r\n      </div>\r\n      <div *ngIf=\"hasProfileText\" class=\"avatar-text avatar-text--profile\">\r\n        {{ profileText }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n", styles: [".avatar-container{position:relative;display:inline-block}.avatar-container .avatar-wrapper{display:inline-flex;padding:8px;align-items:center;gap:8px;background:var(--avatar-status-border-color)}.avatar-container .avatar{display:flex;justify-content:flex-end;align-items:flex-start;gap:10px;flex-shrink:0;aspect-ratio:1/1;background-color:var(--avatar-background);background-size:100% 149.982%;background-repeat:no-repeat;position:relative}.avatar-container .avatar--large{width:var(--avatar-size-lg);height:var(--avatar-size-lg)}.avatar-container .avatar--large.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--large.avatar--square{border-radius:12px}.avatar-container .avatar--medium{width:var(--avatar-size-md);height:var(--avatar-size-md)}.avatar-container .avatar--medium.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--medium.avatar--square{border-radius:8px}.avatar-container .avatar--small{width:var(--avatar-size-sm);height:var(--avatar-size-sm)}.avatar-container .avatar--small.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--small.avatar--square{border-radius:4px}.avatar-badge{position:absolute;top:-4px;right:-4px;z-index:1}.avatar-text-container{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;gap:8px}.avatar-text{color:var(--avatar-text-color);text-align:center;font-weight:var(--avatar-text-font)}.avatar-text--status{font-size:var(--avatar-text-size-sm)}.avatar-text--profile{font-size:var(--avatar-text-size-md)}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "component", type: BadgesComponent, selector: "ava-badges", inputs: ["state", "size", "count", "iconName", "iconColor", "iconSize"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvatarsComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-avatars', imports: [CommonModule, BadgesComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"avatar-container\">\r\n  <div class=\"avatar-wrapper\">\r\n    <div\r\n      [class]=\"avatarClasses\"\r\n      [style.background-image]=\"imageUrl ? 'url(' + imageUrl + ')' : 'none'\"\r\n    >\r\n      <ava-badges\r\n        *ngIf=\"hasBadge\"\r\n        [state]=\"badgeState!\"\r\n        [size]=\"badgeSize!\"\r\n        [count]=\"badgeCount\"\r\n        class=\"avatar-badge\"\r\n      >\r\n      </ava-badges>\r\n    </div>\r\n    <!-- Text labels - can have both status and profile -->\r\n    <div *ngIf=\"hasAnyText\" class=\"avatar-text-container\">\r\n      <div *ngIf=\"hasStatusText\" class=\"avatar-text avatar-text--status\">\r\n        {{ statusText }}\r\n      </div>\r\n      <div *ngIf=\"hasProfileText\" class=\"avatar-text avatar-text--profile\">\r\n        {{ profileText }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n", styles: [".avatar-container{position:relative;display:inline-block}.avatar-container .avatar-wrapper{display:inline-flex;padding:8px;align-items:center;gap:8px;background:var(--avatar-status-border-color)}.avatar-container .avatar{display:flex;justify-content:flex-end;align-items:flex-start;gap:10px;flex-shrink:0;aspect-ratio:1/1;background-color:var(--avatar-background);background-size:100% 149.982%;background-repeat:no-repeat;position:relative}.avatar-container .avatar--large{width:var(--avatar-size-lg);height:var(--avatar-size-lg)}.avatar-container .avatar--large.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--large.avatar--square{border-radius:12px}.avatar-container .avatar--medium{width:var(--avatar-size-md);height:var(--avatar-size-md)}.avatar-container .avatar--medium.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--medium.avatar--square{border-radius:8px}.avatar-container .avatar--small{width:var(--avatar-size-sm);height:var(--avatar-size-sm)}.avatar-container .avatar--small.avatar--pill{border-radius:var(--avatar-border-radius)}.avatar-container .avatar--small.avatar--square{border-radius:4px}.avatar-badge{position:absolute;top:-4px;right:-4px;z-index:1}.avatar-text-container{display:flex;flex-direction:column;justify-content:center;align-items:flex-start;gap:8px}.avatar-text{color:var(--avatar-text-color);text-align:center;font-weight:var(--avatar-text-font)}.avatar-text--status{font-size:var(--avatar-text-size-sm)}.avatar-text--profile{font-size:var(--avatar-text-size-md)}\n"] }]
        }], propDecorators: { size: [{
                type: Input
            }], shape: [{
                type: Input
            }], imageUrl: [{
                type: Input
            }], statusText: [{
                type: Input
            }], profileText: [{
                type: Input
            }], badgeState: [{
                type: Input
            }], badgeSize: [{
                type: Input
            }], badgeCount: [{
                type: Input
            }] } });

class SpinnerComponent {
    type = 'circular';
    size = 'md';
    className = '';
    animation = true;
    color = 'primary';
    progressIndex;
    // Size mapping (reused)
    get sizeClass() {
        const sizeMap = {
            sm: 'size-sm',
            md: 'size-md',
            lg: 'size-lg',
            xl: 'size-xl'
        };
        return sizeMap[this.size];
    }
    // Check if the type is a simple spinner type
    get isStandardSpinner() {
        return ['circular', 'dotted', 'partial', 'dashed'].includes(this.type);
    }
    get progressClass() {
        if (this.progressIndex === undefined)
            return '';
        const rounded = Math.round(this.progressIndex);
        if (rounded <= 25)
            return 'rotate-25';
        if (rounded <= 50)
            return 'rotate-50';
        if (rounded <= 75)
            return 'rotate-75';
        return 'rotate-100';
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: SpinnerComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: SpinnerComponent, isStandalone: true, selector: "ava-spinner", inputs: { type: "type", size: "size", className: "className", animation: "animation", color: "color", progressIndex: "progressIndex" }, ngImport: i0, template: "<div class=\"ava-spinner-container\">\r\n <div *ngIf=\"isStandardSpinner\"\r\n     class=\"spinner\"\r\n     [ngClass]=\"[\r\n        type,\r\n        sizeClass,\r\n        animation ? 'animated' : '',\r\n        color,\r\n        progressIndex !== undefined ? 'progress-spinner' : '',\r\n        progressClass\r\n     ]\">\r\n</div>\r\n\r\n  <div *ngIf=\"type === 'gradient'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner gradient\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\">\r\n      <div class=\"gradient-inner\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"type === 'double'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner double-outer\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n    <div class=\"spinner double-inner\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n  </div>\r\n</div>", styles: [".ava-spinner-container{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:1.5rem;padding:1rem}.ava-spinner-container .spinner{border-radius:50%;transition:transform .45s linear}.ava-spinner-container .spinner.progress-spinner{animation:none!important}.ava-spinner-container .spinner.rotate-25{transform:rotate(90deg)}.ava-spinner-container .spinner.rotate-50{transform:rotate(180deg)}.ava-spinner-container .spinner.rotate-75{transform:rotate(270deg)}.ava-spinner-container .spinner.rotate-100{transform:rotate(360deg)}.ava-spinner-container .spinner.primary{border:3px solid var(--spinner-primary-track);border-top-color:var(--spinner-primary-fill)}.ava-spinner-container .spinner.secondary{border:3px solid var(--spinner-secondary-track);border-top-color:var(--spinner-secondary-fill)}.ava-spinner-container .spinner.success{border:3px solid var(--spinner-success-track);border-top-color:var(--spinner-success-fill)}.ava-spinner-container .spinner.warning{border:3px solid var(--spinner-warning-track);border-top-color:var(--spinner-warning-fill)}.ava-spinner-container .spinner.danger{border:3px solid var(--spinner-error-track);border-top-color:var(--spinner-error-fill)}.ava-spinner-container .spinner.purple{border:3px solid #e2c6fa;border-top-color:#9c27b0}.ava-spinner-container .spinner.animated{animation:spin 3s linear infinite}.ava-spinner-container .spinner.circular{border-width:5px}.ava-spinner-container .spinner.dotted{border-style:dotted;border-width:3px}.ava-spinner-container .spinner.partial{border:3px solid transparent;border-top-color:#9c27b0}.ava-spinner-container .spinner.gradient{border:3px solid transparent;border-image:linear-gradient(45deg,#e2c6fa,#9c27b0) 1}.ava-spinner-container .spinner.dashed{border-style:dashed;border-width:3px}.ava-spinner-container .spinner.double{border-style:double;border-width:3px}.ava-spinner-container .spinner.size-sm{width:var(--spinner-size-sm);height:var(--spinner-size-sm)}.ava-spinner-container .spinner.size-md{width:var(--spinner-size-md);height:var(--spinner-size-md)}.ava-spinner-container .spinner.size-lg{width:var(--spinner-size-lg);height:var(--spinner-size-lg)}.ava-spinner-container .spinner.size-xl{width:var(--spinner-size-xl);height:var(--spinner-size-xl)}@keyframes spin{to{transform:rotate(360deg)}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: SpinnerComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-spinner', standalone: true, imports: [CommonModule], template: "<div class=\"ava-spinner-container\">\r\n <div *ngIf=\"isStandardSpinner\"\r\n     class=\"spinner\"\r\n     [ngClass]=\"[\r\n        type,\r\n        sizeClass,\r\n        animation ? 'animated' : '',\r\n        color,\r\n        progressIndex !== undefined ? 'progress-spinner' : '',\r\n        progressClass\r\n     ]\">\r\n</div>\r\n\r\n  <div *ngIf=\"type === 'gradient'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner gradient\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\">\r\n      <div class=\"gradient-inner\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"type === 'double'\" class=\"spinner-wrapper\" [ngClass]=\"sizeClass\">\r\n    <div class=\"spinner double-outer\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n    <div class=\"spinner double-inner\" [ngClass]=\"[sizeClass, animation ? 'animated' : '',color]\"></div>\r\n  </div>\r\n</div>", styles: [".ava-spinner-container{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:1.5rem;padding:1rem}.ava-spinner-container .spinner{border-radius:50%;transition:transform .45s linear}.ava-spinner-container .spinner.progress-spinner{animation:none!important}.ava-spinner-container .spinner.rotate-25{transform:rotate(90deg)}.ava-spinner-container .spinner.rotate-50{transform:rotate(180deg)}.ava-spinner-container .spinner.rotate-75{transform:rotate(270deg)}.ava-spinner-container .spinner.rotate-100{transform:rotate(360deg)}.ava-spinner-container .spinner.primary{border:3px solid var(--spinner-primary-track);border-top-color:var(--spinner-primary-fill)}.ava-spinner-container .spinner.secondary{border:3px solid var(--spinner-secondary-track);border-top-color:var(--spinner-secondary-fill)}.ava-spinner-container .spinner.success{border:3px solid var(--spinner-success-track);border-top-color:var(--spinner-success-fill)}.ava-spinner-container .spinner.warning{border:3px solid var(--spinner-warning-track);border-top-color:var(--spinner-warning-fill)}.ava-spinner-container .spinner.danger{border:3px solid var(--spinner-error-track);border-top-color:var(--spinner-error-fill)}.ava-spinner-container .spinner.purple{border:3px solid #e2c6fa;border-top-color:#9c27b0}.ava-spinner-container .spinner.animated{animation:spin 3s linear infinite}.ava-spinner-container .spinner.circular{border-width:5px}.ava-spinner-container .spinner.dotted{border-style:dotted;border-width:3px}.ava-spinner-container .spinner.partial{border:3px solid transparent;border-top-color:#9c27b0}.ava-spinner-container .spinner.gradient{border:3px solid transparent;border-image:linear-gradient(45deg,#e2c6fa,#9c27b0) 1}.ava-spinner-container .spinner.dashed{border-style:dashed;border-width:3px}.ava-spinner-container .spinner.double{border-style:double;border-width:3px}.ava-spinner-container .spinner.size-sm{width:var(--spinner-size-sm);height:var(--spinner-size-sm)}.ava-spinner-container .spinner.size-md{width:var(--spinner-size-md);height:var(--spinner-size-md)}.ava-spinner-container .spinner.size-lg{width:var(--spinner-size-lg);height:var(--spinner-size-lg)}.ava-spinner-container .spinner.size-xl{width:var(--spinner-size-xl);height:var(--spinner-size-xl)}@keyframes spin{to{transform:rotate(360deg)}}\n"] }]
        }], propDecorators: { type: [{
                type: Input
            }], size: [{
                type: Input
            }], className: [{
                type: Input
            }], animation: [{
                type: Input
            }], color: [{
                type: Input
            }], progressIndex: [{
                type: Input
            }] } });

class CardComponent {
    heading = '';
    content = '';
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: CardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: CardComponent, isStandalone: true, selector: "ava-card", inputs: { heading: "heading", content: "content" }, ngImport: i0, template: "<div class=\"ava-card-container\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-card card\">\r\n        <div class=\"card-wrapper\">\r\n            <div class=\"card-header\">\r\n                <ng-content select=\"div[header]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-content\">\r\n                <ng-content select=\"div[content]\"></ng-content>\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-footer\">\r\n                <ng-content select=\"div[footer]\"></ng-content>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", styles: [".ava-card-container .ava-card{width:100%;border-radius:5px;padding:20px;text-align:center;box-shadow:-6px -6px 12px #ffffff0d,6px 6px 12px #0006,inset 1px 1px 2px #ffffff0d,inset -1px -1px 2px #0000004d;transition:all .3s ease}.ava-card-container .ava-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-card-container .ava-card h3{font-weight:400;margin:0}.ava-card-container .ava-card p{margin:0}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: CardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-card', imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-card-container\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-card card\">\r\n        <div class=\"card-wrapper\">\r\n            <div class=\"card-header\">\r\n                <ng-content select=\"div[header]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-content\">\r\n                <ng-content select=\"div[content]\"></ng-content>\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n            </div>\r\n\r\n            <div class=\"card-footer\">\r\n                <ng-content select=\"div[footer]\"></ng-content>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", styles: [".ava-card-container .ava-card{width:100%;border-radius:5px;padding:20px;text-align:center;box-shadow:-6px -6px 12px #ffffff0d,6px 6px 12px #0006,inset 1px 1px 2px #ffffff0d,inset -1px -1px 2px #0000004d;transition:all .3s ease}.ava-card-container .ava-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-card-container .ava-card h3{font-weight:400;margin:0}.ava-card-container .ava-card p{margin:0}\n"] }]
        }], propDecorators: { heading: [{
                type: Input
            }], content: [{
                type: Input
            }] } });

class FeatureCardComponent {
    heading = '';
    content = '';
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: FeatureCardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: FeatureCardComponent, isStandalone: true, selector: "ava-feature-card", inputs: { heading: "heading", content: "content" }, ngImport: i0, template: "<div class=\"ava-featured-card-container\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-feature-card\">        \r\n          <div class=\"card-content\">       \r\n          <div class=\"card-header\">\r\n              <ng-content select=\"div[header]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-content\">\r\n              <ng-content select=\"div[content]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-footer\">\r\n              <ng-content select=\"div[footer]\"></ng-content>\r\n          </div>\r\n    </div>\r\n    </div>\r\n</div>", styles: [".ava-featured-card-container .ava-feature-card{width:100%;border-radius:5px;padding:20px;text-align:center;box-shadow:6px 6px 12px #b8b9be,-6px -6px 12px #fff;transition:all .3s ease;background:#e6e7ee}.ava-featured-card-container .ava-feature-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-featured-card-container .ava-feature-card h3{font-weight:400;margin:0}.ava-featured-card-container .ava-feature-card p{margin:0}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: FeatureCardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-feature-card', imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-featured-card-container\" [attr.role]=\"'listitem'\">\r\n    <div class=\"ava-feature-card\">        \r\n          <div class=\"card-content\">       \r\n          <div class=\"card-header\">\r\n              <ng-content select=\"div[header]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-content\">\r\n              <ng-content select=\"div[content]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-footer\">\r\n              <ng-content select=\"div[footer]\"></ng-content>\r\n          </div>\r\n    </div>\r\n    </div>\r\n</div>", styles: [".ava-featured-card-container .ava-feature-card{width:100%;border-radius:5px;padding:20px;text-align:center;box-shadow:6px 6px 12px #b8b9be,-6px -6px 12px #fff;transition:all .3s ease;background:#e6e7ee}.ava-featured-card-container .ava-feature-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-featured-card-container .ava-feature-card h3{font-weight:400;margin:0}.ava-featured-card-container .ava-feature-card p{margin:0}\n"] }]
        }], propDecorators: { heading: [{
                type: Input
            }], content: [{
                type: Input
            }] } });

class AdvancedCardComponent {
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AdvancedCardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AdvancedCardComponent, isStandalone: true, selector: "ava-advanced-card", ngImport: i0, template: "<div class=\"ava-advanced-card-container pricing-container\" [attr.role]=\"'listitem'\">\r\n  <div class=\"ava-advanced-card card\">\r\n    <div class=\"card-content\">       \r\n          <div class=\"card-header\">\r\n              <ng-content select=\"div[header]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-content\">\r\n              <ng-content select=\"div[content]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-footer\">\r\n              <ng-content select=\"div[footer]\"></ng-content>\r\n          </div>\r\n    </div>\r\n\r\n  </div>\r\n</div>", styles: [".ava-advanced-card-container .ava-advanced-card{background:#ffffff0d;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);border-radius:20px;padding:2.5rem;color:#fff;overflow:hidden;transition:all .3s ease;text-align:center}.ava-advanced-card-container .ava-advanced-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-advanced-card-container .ava-advanced-card .card-content p{color:#ffffffb3}.ava-advanced-card-container .ava-advanced-card:hover{transform:translateY(-10px);box-shadow:0 20px 40px #0000004d}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AdvancedCardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-advanced-card', imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-advanced-card-container pricing-container\" [attr.role]=\"'listitem'\">\r\n  <div class=\"ava-advanced-card card\">\r\n    <div class=\"card-content\">       \r\n          <div class=\"card-header\">\r\n              <ng-content select=\"div[header]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-content\">\r\n              <ng-content select=\"div[content]\"></ng-content>\r\n          </div>\r\n\r\n          <div class=\"card-footer\">\r\n              <ng-content select=\"div[footer]\"></ng-content>\r\n          </div>\r\n    </div>\r\n\r\n  </div>\r\n</div>", styles: [".ava-advanced-card-container .ava-advanced-card{background:#ffffff0d;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);border-radius:20px;padding:2.5rem;color:#fff;overflow:hidden;transition:all .3s ease;text-align:center}.ava-advanced-card-container .ava-advanced-card .card-content{display:flex;flex-direction:column;gap:.5rem}.ava-advanced-card-container .ava-advanced-card .card-content p{color:#ffffffb3}.ava-advanced-card-container .ava-advanced-card:hover{transform:translateY(-10px);box-shadow:0 20px 40px #0000004d}\n"] }]
        }] });

class PopupComponent {
    // Controls popup visibility
    show = false;
    // Message ALignment 
    messageAlignment = 'center';
    // Title and message content
    title = 'SUCCESS';
    message = 'Action completed successfully.';
    showTitle = true;
    // Header icon configuration
    showHeaderIcon = true;
    headerIconName = 'circle-check';
    iconColor = 'green';
    iconSize = 70;
    // Close button (top-right corner) configuration
    showClose = true;
    closeIconName = 'x';
    closeIconColor = '#a2a2a2';
    closeIconSize = 24;
    // Inline message (icon + text row below title)
    showInlineMessage = false;
    inlineIconName = 'badge-check';
    inlineIconSize = 40;
    inlineIconColor = 'green';
    inlineMessage = '';
    // Popup width input — accepts string or number (converted to px)
    _popupWidth = '400px';
    set popupWidth(value) {
        this._popupWidth = typeof value === 'number' ? `${value}px` : value;
    }
    get popupWidth() {
        return this._popupWidth;
    }
    // Button visibility controls
    showConfirm = false;
    showCancel = false;
    // Cancel button configuration
    cancelButtonLabel = 'Cancel';
    cancelButtonSize = 'small';
    cancelButtonVariant = 'secondary';
    cancelButtonBackground = '';
    // Confirm button configuration
    confirmButtonLabel = 'Confirm';
    confirmButtonSize = 'small';
    confirmButtonVariant = 'primary';
    confirmButtonBackground = '';
    // Output events
    confirm = new EventEmitter(); // Emit on confirm
    cancel = new EventEmitter(); // Emit on cancel
    closed = new EventEmitter(); // Emit when popup is closed
    // Called when confirm button is clicked
    onConfirm() {
        this.confirm.emit();
        this.closePopup();
    }
    // Called when cancel button is clicked
    onCancel() {
        this.cancel.emit();
        this.closePopup();
    }
    // Closes the popup and emits the closed event
    closePopup() {
        this.closed.emit();
    }
    /**
     * Splits multiline message content using <br> tags and trims each line.
     * Used in the template to support line breaks.
     */
    getMessageLines() {
        return this.message.split(/<br\s*\/?>/i).map(line => line.trim());
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: PopupComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: PopupComponent, isStandalone: true, selector: "ava-popup", inputs: { show: "show", messageAlignment: "messageAlignment", title: "title", message: "message", showTitle: "showTitle", showHeaderIcon: "showHeaderIcon", headerIconName: "headerIconName", iconColor: "iconColor", iconSize: "iconSize", showClose: "showClose", closeIconName: "closeIconName", closeIconColor: "closeIconColor", closeIconSize: "closeIconSize", showInlineMessage: "showInlineMessage", inlineIconName: "inlineIconName", inlineIconSize: "inlineIconSize", inlineIconColor: "inlineIconColor", inlineMessage: "inlineMessage", popupWidth: "popupWidth", showConfirm: "showConfirm", showCancel: "showCancel", cancelButtonLabel: "cancelButtonLabel", cancelButtonSize: "cancelButtonSize", cancelButtonVariant: "cancelButtonVariant", cancelButtonBackground: "cancelButtonBackground", confirmButtonLabel: "confirmButtonLabel", confirmButtonSize: "confirmButtonSize", confirmButtonVariant: "confirmButtonVariant", confirmButtonBackground: "confirmButtonBackground" }, outputs: { confirm: "confirm", cancel: "cancel", closed: "closed" }, ngImport: i0, template: "<div class=\"popup-backdrop\" *ngIf=\"show\" role=\"dialog\" aria-modal=\"true\"\r\n    [attr.aria-labelledby]=\"showTitle ? 'popup-title' : null\"\r\n    [attr.aria-describedby]=\"message ? 'popup-description' : null\">\r\n\r\n    <div class=\"popup-container\" [ngStyle]=\"{ width: popupWidth }\">\r\n\r\n        <ava-icon *ngIf=\"showClose\" class=\"close-btn\" [iconName]=\"closeIconName\" [iconSize]=\"closeIconSize\"\r\n            [iconColor]=\"closeIconColor\" [cursor]=\"true\" (click)=\"closePopup()\" role=\"button\" aria-label=\"Close popup\"\r\n            tabindex=\"0\">\r\n        </ava-icon>\r\n\r\n        <div class=\"popup-icon\" *ngIf=\"showHeaderIcon\">\r\n            <ava-icon [iconName]=\"headerIconName\" [iconSize]=\"iconSize\" [iconColor]=\"iconColor\" aria-hidden=\"true\">\r\n            </ava-icon>\r\n        </div>\r\n\r\n        <h3 class=\"popup-title\" id=\"popup-title\" *ngIf=\"showTitle\">\r\n            {{ title }}\r\n        </h3>\r\n\r\n        <div class=\"popup-inline-message single-line\" *ngIf=\"showInlineMessage\">\r\n            <span class=\"inline-icon\">\r\n                <ava-icon [iconName]=\"inlineIconName\" [iconSize]=\"inlineIconSize\" [iconColor]=\"inlineIconColor\"\r\n                    aria-hidden=\"true\">\r\n                </ava-icon>\r\n            </span>\r\n            <span class=\"inline-text\">{{ inlineMessage }}</span>\r\n        </div>\r\n\r\n        <p *ngIf=\"message\" class=\"popup-message\" id=\"popup-description\" [ngClass]=\"{\r\n    'left-align': messageAlignment === 'left',\r\n    'center-align': messageAlignment === 'center',\r\n    'right-align': messageAlignment === 'right'\r\n  }\">\r\n            <ng-container *ngFor=\"let line of getMessageLines(); let last = last\">\r\n                {{ line }}<br *ngIf=\"!last\" />\r\n            </ng-container>\r\n        </p>\r\n\r\n        <!-- Slot for custom content -->\r\n        <ng-content></ng-content>\r\n\r\n        <div class=\"popup-actions\" *ngIf=\"showCancel || showConfirm\">\r\n\r\n            <ava-button *ngIf=\"showCancel\" [label]=\"cancelButtonLabel\" [size]=\"cancelButtonSize\"\r\n                [variant]=\"cancelButtonVariant\" [background]=\"cancelButtonBackground\" (click)=\"onCancel()\">\r\n            </ava-button>\r\n\r\n            <ava-button *ngIf=\"showConfirm\" [label]=\"confirmButtonLabel\" [size]=\"confirmButtonSize\"\r\n                [variant]=\"confirmButtonVariant\" [background]=\"confirmButtonBackground\" (click)=\"onConfirm()\">\r\n            </ava-button>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>", styles: [".popup-backdrop{position:fixed;inset:0;z-index:var(--popup-z-index);background-color:var(--popup-overlay-background);display:flex;justify-content:center;align-items:center;box-shadow:var(--popup-shadow)}.popup-container{position:relative;padding:var(--popup-content-padding-xl);width:100%;max-height:var(--popup-max-height);background-color:var(--popup-background);border:var(--popup-border);border-radius:var(--popup-border-radius);text-align:center;display:flex;flex-direction:column;align-items:center;gap:var(--popup-content-gap);transition:all .3s ease}.popup-container .popup-title{font-size:var(--popup-heading-size);font-weight:var(--popup-heading-weight);margin:var(--popup-heading-margin);color:var(--popup-heading-text);line-height:var(--popup-heading-line-height);text-align:center}.popup-container .popup-inline-message.single-line{display:flex;align-items:center;gap:8px;width:100%;text-align:left;margin:0}.popup-container .popup-inline-message.single-line .inline-icon{display:flex;align-items:center;justify-content:center;flex-shrink:0;margin:0}.popup-container .popup-inline-message.single-line .inline-text{font-size:var(--popup-description-size);color:var(--popup-black-color);line-height:var(--popup-description-line-height);word-break:break-word;overflow-wrap:anywhere;flex:1;display:block;margin:0}.popup-container .popup-message{font-size:var(--popup-description-font);font-weight:var(--popup-description-weight);margin:var(--popup-description-margin);letter-spacing:-.022em;color:var(--popup-description-text);line-height:var(--popup-description-line-height);padding-bottom:8px;width:100%}.popup-container .popup-message.left-align{text-align:left}.popup-container .popup-message.center-align{text-align:center}.popup-container .popup-message.right-align{text-align:right}.popup-container .popup-actions{display:flex;justify-content:center;gap:12px;margin:0;padding-top:10px}.popup-container .close-btn{position:absolute;top:10px;right:12px;background:none;border:none;cursor:pointer}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgForOf, selector: "[ngFor][ngForOf]", inputs: ["ngForOf", "ngForTrackBy", "ngForTemplate"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }, { kind: "ngmodule", type: FormsModule }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }, { kind: "component", type: ButtonComponent, selector: "ava-button", inputs: ["label", "variant", "size", "state", "visual", "pill", "disabled", "width", "height", "gradient", "background", "color", "dropdown", "iconName", "iconColor", "iconSize", "iconPosition"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: PopupComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-popup', standalone: true, imports: [CommonModule, FormsModule, IconComponent, ButtonComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"popup-backdrop\" *ngIf=\"show\" role=\"dialog\" aria-modal=\"true\"\r\n    [attr.aria-labelledby]=\"showTitle ? 'popup-title' : null\"\r\n    [attr.aria-describedby]=\"message ? 'popup-description' : null\">\r\n\r\n    <div class=\"popup-container\" [ngStyle]=\"{ width: popupWidth }\">\r\n\r\n        <ava-icon *ngIf=\"showClose\" class=\"close-btn\" [iconName]=\"closeIconName\" [iconSize]=\"closeIconSize\"\r\n            [iconColor]=\"closeIconColor\" [cursor]=\"true\" (click)=\"closePopup()\" role=\"button\" aria-label=\"Close popup\"\r\n            tabindex=\"0\">\r\n        </ava-icon>\r\n\r\n        <div class=\"popup-icon\" *ngIf=\"showHeaderIcon\">\r\n            <ava-icon [iconName]=\"headerIconName\" [iconSize]=\"iconSize\" [iconColor]=\"iconColor\" aria-hidden=\"true\">\r\n            </ava-icon>\r\n        </div>\r\n\r\n        <h3 class=\"popup-title\" id=\"popup-title\" *ngIf=\"showTitle\">\r\n            {{ title }}\r\n        </h3>\r\n\r\n        <div class=\"popup-inline-message single-line\" *ngIf=\"showInlineMessage\">\r\n            <span class=\"inline-icon\">\r\n                <ava-icon [iconName]=\"inlineIconName\" [iconSize]=\"inlineIconSize\" [iconColor]=\"inlineIconColor\"\r\n                    aria-hidden=\"true\">\r\n                </ava-icon>\r\n            </span>\r\n            <span class=\"inline-text\">{{ inlineMessage }}</span>\r\n        </div>\r\n\r\n        <p *ngIf=\"message\" class=\"popup-message\" id=\"popup-description\" [ngClass]=\"{\r\n    'left-align': messageAlignment === 'left',\r\n    'center-align': messageAlignment === 'center',\r\n    'right-align': messageAlignment === 'right'\r\n  }\">\r\n            <ng-container *ngFor=\"let line of getMessageLines(); let last = last\">\r\n                {{ line }}<br *ngIf=\"!last\" />\r\n            </ng-container>\r\n        </p>\r\n\r\n        <!-- Slot for custom content -->\r\n        <ng-content></ng-content>\r\n\r\n        <div class=\"popup-actions\" *ngIf=\"showCancel || showConfirm\">\r\n\r\n            <ava-button *ngIf=\"showCancel\" [label]=\"cancelButtonLabel\" [size]=\"cancelButtonSize\"\r\n                [variant]=\"cancelButtonVariant\" [background]=\"cancelButtonBackground\" (click)=\"onCancel()\">\r\n            </ava-button>\r\n\r\n            <ava-button *ngIf=\"showConfirm\" [label]=\"confirmButtonLabel\" [size]=\"confirmButtonSize\"\r\n                [variant]=\"confirmButtonVariant\" [background]=\"confirmButtonBackground\" (click)=\"onConfirm()\">\r\n            </ava-button>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>", styles: [".popup-backdrop{position:fixed;inset:0;z-index:var(--popup-z-index);background-color:var(--popup-overlay-background);display:flex;justify-content:center;align-items:center;box-shadow:var(--popup-shadow)}.popup-container{position:relative;padding:var(--popup-content-padding-xl);width:100%;max-height:var(--popup-max-height);background-color:var(--popup-background);border:var(--popup-border);border-radius:var(--popup-border-radius);text-align:center;display:flex;flex-direction:column;align-items:center;gap:var(--popup-content-gap);transition:all .3s ease}.popup-container .popup-title{font-size:var(--popup-heading-size);font-weight:var(--popup-heading-weight);margin:var(--popup-heading-margin);color:var(--popup-heading-text);line-height:var(--popup-heading-line-height);text-align:center}.popup-container .popup-inline-message.single-line{display:flex;align-items:center;gap:8px;width:100%;text-align:left;margin:0}.popup-container .popup-inline-message.single-line .inline-icon{display:flex;align-items:center;justify-content:center;flex-shrink:0;margin:0}.popup-container .popup-inline-message.single-line .inline-text{font-size:var(--popup-description-size);color:var(--popup-black-color);line-height:var(--popup-description-line-height);word-break:break-word;overflow-wrap:anywhere;flex:1;display:block;margin:0}.popup-container .popup-message{font-size:var(--popup-description-font);font-weight:var(--popup-description-weight);margin:var(--popup-description-margin);letter-spacing:-.022em;color:var(--popup-description-text);line-height:var(--popup-description-line-height);padding-bottom:8px;width:100%}.popup-container .popup-message.left-align{text-align:left}.popup-container .popup-message.center-align{text-align:center}.popup-container .popup-message.right-align{text-align:right}.popup-container .popup-actions{display:flex;justify-content:center;gap:12px;margin:0;padding-top:10px}.popup-container .close-btn{position:absolute;top:10px;right:12px;background:none;border:none;cursor:pointer}\n"] }]
        }], propDecorators: { show: [{
                type: Input
            }], messageAlignment: [{
                type: Input
            }], title: [{
                type: Input
            }], message: [{
                type: Input
            }], showTitle: [{
                type: Input
            }], showHeaderIcon: [{
                type: Input
            }], headerIconName: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], iconSize: [{
                type: Input
            }], showClose: [{
                type: Input
            }], closeIconName: [{
                type: Input
            }], closeIconColor: [{
                type: Input
            }], closeIconSize: [{
                type: Input
            }], showInlineMessage: [{
                type: Input
            }], inlineIconName: [{
                type: Input
            }], inlineIconSize: [{
                type: Input
            }], inlineIconColor: [{
                type: Input
            }], inlineMessage: [{
                type: Input
            }], popupWidth: [{
                type: Input
            }], showConfirm: [{
                type: Input
            }], showCancel: [{
                type: Input
            }], cancelButtonLabel: [{
                type: Input
            }], cancelButtonSize: [{
                type: Input
            }], cancelButtonVariant: [{
                type: Input
            }], cancelButtonBackground: [{
                type: Input
            }], confirmButtonLabel: [{
                type: Input
            }], confirmButtonSize: [{
                type: Input
            }], confirmButtonVariant: [{
                type: Input
            }], confirmButtonBackground: [{
                type: Input
            }], confirm: [{
                type: Output
            }], cancel: [{
                type: Output
            }], closed: [{
                type: Output
            }] } });

class LinkComponent {
    label = 'Action Link';
    color = 'default';
    size = 'medium';
    underline = false;
    isHexColor(color) {
        return /^#([0-9A-F]{3}){1,2}$/i.test(color);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: LinkComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: LinkComponent, isStandalone: true, selector: "ava-link", inputs: { label: "label", color: "color", size: "size", underline: "underline" }, ngImport: i0, template: "<a\r\n class=\"action-link\"\r\n [ngClass]=\"[color, size, underline ? 'underline' : '']\"\r\n [ngStyle]=\"isHexColor(color) ? {'color': color} : {}\"\r\n>\r\n {{ label }}\r\n</a>\r\n\r\n", styles: [".action-link{cursor:pointer;text-decoration:none;display:inline-flex;align-items:center;font-weight:500;transition:color .3s ease}.action-link.small{font-size:var(--link-size-sm-font)}.action-link.medium{font-size:var(--link-size-md-font)}.action-link.large{font-size:var(--link-size-lg-font)}.action-link .icon-left{margin-right:6px}.action-link.primary{color:var(--link-primary-text)}.action-link.danger{color:var(--link-danger-text)}.action-link.success{color:var(--link-success-text)}.action-link.warning{color:var(--link-warning-text)}.action-link.info{color:var(--link-info-text)}.action-link:hover{color:var(--color-text-interactive-hover)!important}.action-link.underline{text-decoration:var(--link-active-text-decoration)}.action-link.underline:hover{text-decoration:underline}.action-link.underline:hover{color:var(--color-text-interactive-hover)}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: LinkComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-link', standalone: true, imports: [CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: "<a\r\n class=\"action-link\"\r\n [ngClass]=\"[color, size, underline ? 'underline' : '']\"\r\n [ngStyle]=\"isHexColor(color) ? {'color': color} : {}\"\r\n>\r\n {{ label }}\r\n</a>\r\n\r\n", styles: [".action-link{cursor:pointer;text-decoration:none;display:inline-flex;align-items:center;font-weight:500;transition:color .3s ease}.action-link.small{font-size:var(--link-size-sm-font)}.action-link.medium{font-size:var(--link-size-md-font)}.action-link.large{font-size:var(--link-size-lg-font)}.action-link .icon-left{margin-right:6px}.action-link.primary{color:var(--link-primary-text)}.action-link.danger{color:var(--link-danger-text)}.action-link.success{color:var(--link-success-text)}.action-link.warning{color:var(--link-warning-text)}.action-link.info{color:var(--link-info-text)}.action-link:hover{color:var(--color-text-interactive-hover)!important}.action-link.underline{text-decoration:var(--link-active-text-decoration)}.action-link.underline:hover{text-decoration:underline}.action-link.underline:hover{color:var(--color-text-interactive-hover)}\n"] }]
        }], propDecorators: { label: [{
                type: Input
            }], color: [{
                type: Input
            }], size: [{
                type: Input
            }], underline: [{
                type: Input
            }] } });

/**
 * AvaTagComponent: Modern, accessible, and highly customizable tag/chip component.
 * Supports filled/outlined, color variants, pill/rect, removable, icons, avatars, sizes, and custom styles.
 */
class AvaTagComponent {
    /** Tag label text */
    label;
    /** Color variant */
    color = 'default';
    /** Outlined or filled */
    variant = 'filled';
    /** Tag size */
    size = 'md';
    /** Pill/rounded shape */
    pill = false;
    /** Removable (shows close icon) */
    removable = false;
    /** Disabled state */
    disabled = false;
    /** Icon name (ava-icon) */
    icon;
    /** Icon position (left only, close always right) */
    iconPosition = 'start';
    /** Avatar: image URL or initials */
    avatar;
    /** Custom style object for CSS vars */
    customStyle;
    /** Custom class for tag */
    customClass;
    /** Custom icon color */
    iconColor;
    /** Emits when tag is removed (close icon) */
    removed = new EventEmitter();
    /** Emits when tag is clicked (if handler provided) */
    clicked = new EventEmitter();
    /** True if tag is clickable (handler attached and not disabled) */
    get clickable() {
        return this.clicked.observers.length > 0 && !this.disabled;
    }
    /** Remove handler (close icon) */
    onRemove(event) {
        event.stopPropagation();
        if (!this.disabled) {
            this.removed.emit();
        }
    }
    /** Click handler (entire tag) */
    onClick() {
        if (this.clickable) {
            this.clicked.emit();
        }
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTagComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AvaTagComponent, isStandalone: true, selector: "ava-tag", inputs: { label: "label", color: "color", variant: "variant", size: "size", pill: "pill", removable: "removable", disabled: "disabled", icon: "icon", iconPosition: "iconPosition", avatar: "avatar", customStyle: "customStyle", customClass: "customClass", iconColor: "iconColor" }, outputs: { removed: "removed", clicked: "clicked" }, ngImport: i0, template: "<ng-container>\r\n  <span\r\n    class=\"ava-tag\"\r\n    [ngClass]=\"[\r\n      'ava-tag',\r\n      'ava-tag--' + color,\r\n      'ava-tag--' + variant,\r\n      'ava-tag--' + size,\r\n      pill ? 'ava-tag--pill' : '',\r\n      disabled ? 'ava-tag--disabled' : '',\r\n      customClass || '',\r\n      clickable ? 'ava-tag--clickable' : ''\r\n    ]\"\r\n    [ngStyle]=\"customStyle\"\r\n    [attr.tabindex]=\"clickable ? 0 : null\"\r\n    [attr.role]=\"clickable ? 'button' : null\"\r\n    [attr.aria-disabled]=\"disabled ? 'true' : null\"\r\n    (click)=\"onClick()\"\r\n    (keyup.enter)=\"onClick()\"\r\n  >\r\n    <ng-container *ngIf=\"avatar\">\r\n      <span class=\"ava-tag__avatar\" *ngIf=\"avatar && avatar.startsWith('http')\">\r\n        <img [src]=\"avatar\" alt=\"avatar\" />\r\n      </span>\r\n      <span\r\n        class=\"ava-tag__avatar ava-tag__avatar--initials\"\r\n        *ngIf=\"avatar && !avatar.startsWith('http')\"\r\n      >\r\n        {{ avatar }}\r\n      </span>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"icon && iconPosition === 'start'\">\r\n      <ava-icon\r\n        [iconName]=\"icon || ''\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"false\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__icon ava-tag__icon--start\"\r\n      ></ava-icon>\r\n    </ng-container>\r\n    <span class=\"ava-tag__label\">{{ label }}</span>\r\n    <button\r\n      *ngIf=\"removable\"\r\n      type=\"button\"\r\n      class=\"ava-tag__remove-btn\"\r\n      [disabled]=\"disabled\"\r\n      tabindex=\"0\"\r\n      aria-label=\"Remove tag\"\r\n      (click)=\"onRemove($event)\"\r\n      (keyup.enter)=\"onRemove($event)\"\r\n    >\r\n      <ava-icon\r\n        iconName=\"x\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"true\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__remove\"\r\n      ></ava-icon>\r\n    </button>\r\n  </span>\r\n</ng-container>\r\n", styles: [".ava-tag{display:inline-flex;align-items:center;gap:.12em;font:var(--tags-font);border-radius:.7em;padding:.38em .28em;background:var(--tags-filled-background);color:var(--tags-filled-text);border:var(--tags-filled-border);transition:box-shadow .18s,outline .18s,background .18s,color .18s,border .18s;cursor:default;outline:none;min-width:0;max-width:100%;white-space:nowrap;-webkit-user-select:none;user-select:none;position:relative;box-shadow:none;line-height:1.8;font-size:1.05rem}.ava-tag.ava-tag--pill{border-radius:var(--tags-pill-border-radius)!important}.ava-tag.ava-tag--disabled{background:var(--tags-disabled-background)!important;color:var(--tags-disabled-text)!important;border:var(--tags-disabled-border)!important;cursor:var(--tags-disabled-cursor);opacity:.6;pointer-events:none}.ava-tag.ava-tag--clickable:not(.ava-tag--disabled){cursor:pointer}.ava-tag.ava-tag--clickable:not(.ava-tag--disabled):hover,.ava-tag.ava-tag--clickable:not(.ava-tag--disabled):focus-visible{box-shadow:0 0 0 2px var(--tags-hover-border);outline:2px solid var(--tags-hover-border);outline-offset:1px;border:var(--tags-hover-border)}.ava-tag.ava-tag--sm{font-size:var(--tags-size-sm-font);padding:var(--tags-size-sm-padding);border-radius:var(--tags-size-sm-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--md{font-size:var(--tags-size-md-font);padding:var(--tags-size-md-padding);border-radius:var(--tags-size-md-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--lg{font-size:var(--tags-size-lg-font);padding:var(--tags-size-lg-padding);border-radius:var(--tags-size-lg-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--filled{background:var(--tags-filled-background);color:var(--tags-filled-text);border:var(--tags-filled-border)}.ava-tag.ava-tag--outlined{background:var(--tags-outlined-background)!important;color:var(--tags-outlined-text);border:var(--tags-outlined-border)}.ava-tag.ava-tag--outlined.ava-tag--primary{color:var(--tags-outlined-primary-text);border:var(--tags-outlined-primary-border)}.ava-tag.ava-tag--outlined.ava-tag--success{color:var(--tags-outlined-success-text);border:var(--tags-outlined-success-border)}.ava-tag.ava-tag--outlined.ava-tag--warning{color:var(--tags-outlined-warning-text);border:var(--tags-outlined-warning-border)}.ava-tag.ava-tag--outlined.ava-tag--error{color:var(--tags-outlined-error-text);border:var(--tags-outlined-error-border)}.ava-tag.ava-tag--outlined.ava-tag--info{color:var(--tags-outlined-info-text);border:var(--tags-outlined-info-border)}.ava-tag.ava-tag--outlined.ava-tag--custom{background:var(--tag-custom-bg)!important;color:var(--tag-custom-color);border:var(--tag-custom-border)}.ava-tag.ava-tag--primary{background:var(--tags-filled-primary-background);color:var(--tags-filled-primary-text);border:var(--tags-filled-primary-border)}.ava-tag.ava-tag--success{background:var(--tags-filled-success-background);color:var(--tags-filled-success-text);border:var(--tags-filled-success-border)}.ava-tag.ava-tag--warning{background:var(--tags-filled-warning-background);color:var(--tags-filled-warning-text);border:var(--tags-filled-warning-border)}.ava-tag.ava-tag--error{background:var(--tags-filled-error-background);color:var(--tags-filled-error-text);border:var(--tags-filled-error-border)}.ava-tag.ava-tag--info{background:var(--tags-filled-info-background);color:var(--tags-filled-info-text);border:var(--tags-filled-info-border)}.ava-tag.ava-tag--custom{background:var(--tag-custom-bg);color:var(--tag-custom-color);border:var(--tag-custom-border)}.ava-tag__icon{font-size:.9em;display:inline-flex;align-items:center;color:var(--tags-icon-color, currentColor);margin-right:.12em;margin-left:0;transition:color .18s}.ava-tag__icon--start{margin-right:.6em;margin-left:-.4em}.ava-tag__icon--end{margin-left:.8em;margin-right:0}.ava-tag__avatar{width:1.3em;height:1.3em;margin-right:.3em;margin-left:-.2em;font-size:1em;border-radius:50%;background:#e5e7eb;color:#222;overflow:hidden}.ava-tag__avatar img{width:100%;height:100%;object-fit:cover;border-radius:50%}.ava-tag__avatar--initials{background:#d1d5db;color:#222}.ava-tag__label{display:inline-block;vertical-align:middle;max-width:12em;overflow:hidden;text-overflow:ellipsis;font-weight:500;letter-spacing:.01em}.ava-tag__remove-btn{background:none;border:none;padding:.1em;margin-left:.2em;margin-right:-.4em;display:flex;align-items:center;cursor:pointer;border-radius:50%;transition:none;opacity:.7}.ava-tag__remove-btn:hover:not(:disabled),.ava-tag__remove-btn:active:not(:disabled){background:none;color:inherit;opacity:1}.ava-tag__remove-btn:focus-visible{outline:2px solid var(--tags-focus-outline, #2563eb);outline-offset:2px}.ava-tag__remove-btn:disabled{cursor:not-allowed;opacity:.5}.ava-tag__remove{font-size:var(--tags-removable-button-size, 1em);color:var(--tags-removable-button-text, #888);background:transparent;pointer-events:none}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTagComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-tag', standalone: true, imports: [CommonModule, IconComponent], template: "<ng-container>\r\n  <span\r\n    class=\"ava-tag\"\r\n    [ngClass]=\"[\r\n      'ava-tag',\r\n      'ava-tag--' + color,\r\n      'ava-tag--' + variant,\r\n      'ava-tag--' + size,\r\n      pill ? 'ava-tag--pill' : '',\r\n      disabled ? 'ava-tag--disabled' : '',\r\n      customClass || '',\r\n      clickable ? 'ava-tag--clickable' : ''\r\n    ]\"\r\n    [ngStyle]=\"customStyle\"\r\n    [attr.tabindex]=\"clickable ? 0 : null\"\r\n    [attr.role]=\"clickable ? 'button' : null\"\r\n    [attr.aria-disabled]=\"disabled ? 'true' : null\"\r\n    (click)=\"onClick()\"\r\n    (keyup.enter)=\"onClick()\"\r\n  >\r\n    <ng-container *ngIf=\"avatar\">\r\n      <span class=\"ava-tag__avatar\" *ngIf=\"avatar && avatar.startsWith('http')\">\r\n        <img [src]=\"avatar\" alt=\"avatar\" />\r\n      </span>\r\n      <span\r\n        class=\"ava-tag__avatar ava-tag__avatar--initials\"\r\n        *ngIf=\"avatar && !avatar.startsWith('http')\"\r\n      >\r\n        {{ avatar }}\r\n      </span>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"icon && iconPosition === 'start'\">\r\n      <ava-icon\r\n        [iconName]=\"icon || ''\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"false\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__icon ava-tag__icon--start\"\r\n      ></ava-icon>\r\n    </ng-container>\r\n    <span class=\"ava-tag__label\">{{ label }}</span>\r\n    <button\r\n      *ngIf=\"removable\"\r\n      type=\"button\"\r\n      class=\"ava-tag__remove-btn\"\r\n      [disabled]=\"disabled\"\r\n      tabindex=\"0\"\r\n      aria-label=\"Remove tag\"\r\n      (click)=\"onRemove($event)\"\r\n      (keyup.enter)=\"onRemove($event)\"\r\n    >\r\n      <ava-icon\r\n        iconName=\"x\"\r\n        [iconSize]=\"16\"\r\n        [iconColor]=\"iconColor || 'currentColor'\"\r\n        [cursor]=\"true\"\r\n        [disabled]=\"disabled\"\r\n        class=\"ava-tag__remove\"\r\n      ></ava-icon>\r\n    </button>\r\n  </span>\r\n</ng-container>\r\n", styles: [".ava-tag{display:inline-flex;align-items:center;gap:.12em;font:var(--tags-font);border-radius:.7em;padding:.38em .28em;background:var(--tags-filled-background);color:var(--tags-filled-text);border:var(--tags-filled-border);transition:box-shadow .18s,outline .18s,background .18s,color .18s,border .18s;cursor:default;outline:none;min-width:0;max-width:100%;white-space:nowrap;-webkit-user-select:none;user-select:none;position:relative;box-shadow:none;line-height:1.8;font-size:1.05rem}.ava-tag.ava-tag--pill{border-radius:var(--tags-pill-border-radius)!important}.ava-tag.ava-tag--disabled{background:var(--tags-disabled-background)!important;color:var(--tags-disabled-text)!important;border:var(--tags-disabled-border)!important;cursor:var(--tags-disabled-cursor);opacity:.6;pointer-events:none}.ava-tag.ava-tag--clickable:not(.ava-tag--disabled){cursor:pointer}.ava-tag.ava-tag--clickable:not(.ava-tag--disabled):hover,.ava-tag.ava-tag--clickable:not(.ava-tag--disabled):focus-visible{box-shadow:0 0 0 2px var(--tags-hover-border);outline:2px solid var(--tags-hover-border);outline-offset:1px;border:var(--tags-hover-border)}.ava-tag.ava-tag--sm{font-size:var(--tags-size-sm-font);padding:var(--tags-size-sm-padding);border-radius:var(--tags-size-sm-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--md{font-size:var(--tags-size-md-font);padding:var(--tags-size-md-padding);border-radius:var(--tags-size-md-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--lg{font-size:var(--tags-size-lg-font);padding:var(--tags-size-lg-padding);border-radius:var(--tags-size-lg-border-radius);--tags-icon-size: var(--tags-icon-size)}.ava-tag.ava-tag--filled{background:var(--tags-filled-background);color:var(--tags-filled-text);border:var(--tags-filled-border)}.ava-tag.ava-tag--outlined{background:var(--tags-outlined-background)!important;color:var(--tags-outlined-text);border:var(--tags-outlined-border)}.ava-tag.ava-tag--outlined.ava-tag--primary{color:var(--tags-outlined-primary-text);border:var(--tags-outlined-primary-border)}.ava-tag.ava-tag--outlined.ava-tag--success{color:var(--tags-outlined-success-text);border:var(--tags-outlined-success-border)}.ava-tag.ava-tag--outlined.ava-tag--warning{color:var(--tags-outlined-warning-text);border:var(--tags-outlined-warning-border)}.ava-tag.ava-tag--outlined.ava-tag--error{color:var(--tags-outlined-error-text);border:var(--tags-outlined-error-border)}.ava-tag.ava-tag--outlined.ava-tag--info{color:var(--tags-outlined-info-text);border:var(--tags-outlined-info-border)}.ava-tag.ava-tag--outlined.ava-tag--custom{background:var(--tag-custom-bg)!important;color:var(--tag-custom-color);border:var(--tag-custom-border)}.ava-tag.ava-tag--primary{background:var(--tags-filled-primary-background);color:var(--tags-filled-primary-text);border:var(--tags-filled-primary-border)}.ava-tag.ava-tag--success{background:var(--tags-filled-success-background);color:var(--tags-filled-success-text);border:var(--tags-filled-success-border)}.ava-tag.ava-tag--warning{background:var(--tags-filled-warning-background);color:var(--tags-filled-warning-text);border:var(--tags-filled-warning-border)}.ava-tag.ava-tag--error{background:var(--tags-filled-error-background);color:var(--tags-filled-error-text);border:var(--tags-filled-error-border)}.ava-tag.ava-tag--info{background:var(--tags-filled-info-background);color:var(--tags-filled-info-text);border:var(--tags-filled-info-border)}.ava-tag.ava-tag--custom{background:var(--tag-custom-bg);color:var(--tag-custom-color);border:var(--tag-custom-border)}.ava-tag__icon{font-size:.9em;display:inline-flex;align-items:center;color:var(--tags-icon-color, currentColor);margin-right:.12em;margin-left:0;transition:color .18s}.ava-tag__icon--start{margin-right:.6em;margin-left:-.4em}.ava-tag__icon--end{margin-left:.8em;margin-right:0}.ava-tag__avatar{width:1.3em;height:1.3em;margin-right:.3em;margin-left:-.2em;font-size:1em;border-radius:50%;background:#e5e7eb;color:#222;overflow:hidden}.ava-tag__avatar img{width:100%;height:100%;object-fit:cover;border-radius:50%}.ava-tag__avatar--initials{background:#d1d5db;color:#222}.ava-tag__label{display:inline-block;vertical-align:middle;max-width:12em;overflow:hidden;text-overflow:ellipsis;font-weight:500;letter-spacing:.01em}.ava-tag__remove-btn{background:none;border:none;padding:.1em;margin-left:.2em;margin-right:-.4em;display:flex;align-items:center;cursor:pointer;border-radius:50%;transition:none;opacity:.7}.ava-tag__remove-btn:hover:not(:disabled),.ava-tag__remove-btn:active:not(:disabled){background:none;color:inherit;opacity:1}.ava-tag__remove-btn:focus-visible{outline:2px solid var(--tags-focus-outline, #2563eb);outline-offset:2px}.ava-tag__remove-btn:disabled{cursor:not-allowed;opacity:.5}.ava-tag__remove{font-size:var(--tags-removable-button-size, 1em);color:var(--tags-removable-button-text, #888);background:transparent;pointer-events:none}\n"] }]
        }], propDecorators: { label: [{
                type: Input
            }], color: [{
                type: Input
            }], variant: [{
                type: Input
            }], size: [{
                type: Input
            }], pill: [{
                type: Input
            }], removable: [{
                type: Input
            }], disabled: [{
                type: Input
            }], icon: [{
                type: Input
            }], iconPosition: [{
                type: Input
            }], avatar: [{
                type: Input
            }], customStyle: [{
                type: Input
            }], customClass: [{
                type: Input
            }], iconColor: [{
                type: Input
            }], removed: [{
                type: Output
            }], clicked: [{
                type: Output
            }] } });

class ApprovalCardComponent {
    cardData;
    height = '300px';
    contentTemplate;
    contentsBackground = '';
    cardContainerBackground = '';
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ApprovalCardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: ApprovalCardComponent, isStandalone: true, selector: "ava-approval-card", inputs: { cardData: "cardData", height: "height", contentTemplate: "contentTemplate", contentsBackground: "contentsBackground", cardContainerBackground: "cardContainerBackground" }, ngImport: i0, template: "<div class=\"ava-console-approval-card-container\" [style.background-color]=\"cardContainerBackground\">\r\n    <ava-card>\r\n        <div header *ngIf=\"cardData?.header\">\r\n            <h2 *ngIf=\"cardData?.header?.title\">{{cardData?.header?.title}}</h2>\r\n        </div>\r\n        <div [style.height.px]=\"height?height:null\" class=\"content-wrapper\" content *ngIf=\"cardData?.contents\">\r\n            <div class=\"contents\" *ngFor=\"let each of cardData?.contents; let i= index\"\r\n                [style.background-color]=\"contentsBackground\">\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n\r\n                <div class=\"box c-header-wrapper\" style=\"display: flex; justify-content: space-between; width: 100%;\">\r\n                    <div class=\"head-left\">\r\n                        <p *ngIf=\"each?.session1?.title\">{{each?.session1?.title}} </p>\r\n                    </div>\r\n                    <div class=\"head-right\">\r\n                        <ava-tag *ngFor=\"let eachLabel of each?.session1?.labels\" [label]=\"eachLabel.name\"\r\n                            [color]=\"eachLabel.color\" size=\"sm\"></ava-tag>\r\n                        <ava-icon [iconColor]=\"'green'\" iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                    </div>\r\n\r\n                </div>\r\n                <div class=\"box label-wrapper\">\r\n                    <ava-tag *ngFor=\"let eachLabel of each?.session2\" [label]=\"eachLabel.name\" [color]=\"eachLabel.color\"\r\n                        size=\"sm\"></ava-tag>\r\n                </div>\r\n\r\n                <div class=\"box info-wrapper\">\r\n                    <div [ngClass]=\"{'f': i==0}\" *ngFor=\"let eachLabel of each?.session3; let i=index\">\r\n                        <ava-icon iconSize=\"20\" [iconName]=\"eachLabel.iconName\"></ava-icon>\r\n                        <span>{{eachLabel.label}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"box footer-wrapper\" *ngIf=\" each?.session4\">\r\n                    <ng-container *ngTemplateOutlet=\"contentTemplate;context: { index: i , label:each.session4 }\">\r\n                    </ng-container>\r\n                </div>\r\n\r\n\r\n            </div>\r\n        </div>\r\n\r\n    </ava-card>\r\n</div>", styles: [".ava-console-approval-card-container ava-card h2{text-align:left;margin:0;font-size:24px;font-weight:700}::ng-deep .ava-console-approval-card-container ava-card .ava-card-container .card-wrapper{margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-header{margin-bottom:24px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper{overflow-y:scroll}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents{padding:24px;margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box{margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.c-header-wrapper p{text-align:left;margin-right:50px;padding:0;margin-top:8px;margin-bottom:0;font-size:24px;font-weight:400}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.c-header-wrapper ava-tag{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper{text-align:left}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper ava-tag{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper ava-tag span{border:none}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper{text-align:left;display:flex}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper ava-icon{margin-right:5px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper .f{margin-right:50px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content{display:flex;justify-content:space-between;width:100%}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-left{margin-right:20px;margin-top:13px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-left ava-icon{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-right{display:flex;text-align:right}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-right ava-button{margin-right:8px}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgForOf, selector: "[ngFor][ngForOf]", inputs: ["ngForOf", "ngForTrackBy", "ngForTemplate"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgTemplateOutlet, selector: "[ngTemplateOutlet]", inputs: ["ngTemplateOutletContext", "ngTemplateOutlet", "ngTemplateOutletInjector"] }, { kind: "component", type: CardComponent, selector: "ava-card", inputs: ["heading", "content"] }, { kind: "component", type: AvaTagComponent, selector: "ava-tag", inputs: ["label", "color", "variant", "size", "pill", "removable", "disabled", "icon", "iconPosition", "avatar", "customStyle", "customClass", "iconColor"], outputs: ["removed", "clicked"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ApprovalCardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-approval-card', imports: [CommonModule, NgIf, NgFor, CardComponent, AvaTagComponent, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-console-approval-card-container\" [style.background-color]=\"cardContainerBackground\">\r\n    <ava-card>\r\n        <div header *ngIf=\"cardData?.header\">\r\n            <h2 *ngIf=\"cardData?.header?.title\">{{cardData?.header?.title}}</h2>\r\n        </div>\r\n        <div [style.height.px]=\"height?height:null\" class=\"content-wrapper\" content *ngIf=\"cardData?.contents\">\r\n            <div class=\"contents\" *ngFor=\"let each of cardData?.contents; let i= index\"\r\n                [style.background-color]=\"contentsBackground\">\r\n                <ng-content select=\"div[contentFooter]\"></ng-content>\r\n\r\n                <div class=\"box c-header-wrapper\" style=\"display: flex; justify-content: space-between; width: 100%;\">\r\n                    <div class=\"head-left\">\r\n                        <p *ngIf=\"each?.session1?.title\">{{each?.session1?.title}} </p>\r\n                    </div>\r\n                    <div class=\"head-right\">\r\n                        <ava-tag *ngFor=\"let eachLabel of each?.session1?.labels\" [label]=\"eachLabel.name\"\r\n                            [color]=\"eachLabel.color\" size=\"sm\"></ava-tag>\r\n                        <ava-icon [iconColor]=\"'green'\" iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                    </div>\r\n\r\n                </div>\r\n                <div class=\"box label-wrapper\">\r\n                    <ava-tag *ngFor=\"let eachLabel of each?.session2\" [label]=\"eachLabel.name\" [color]=\"eachLabel.color\"\r\n                        size=\"sm\"></ava-tag>\r\n                </div>\r\n\r\n                <div class=\"box info-wrapper\">\r\n                    <div [ngClass]=\"{'f': i==0}\" *ngFor=\"let eachLabel of each?.session3; let i=index\">\r\n                        <ava-icon iconSize=\"20\" [iconName]=\"eachLabel.iconName\"></ava-icon>\r\n                        <span>{{eachLabel.label}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"box footer-wrapper\" *ngIf=\" each?.session4\">\r\n                    <ng-container *ngTemplateOutlet=\"contentTemplate;context: { index: i , label:each.session4 }\">\r\n                    </ng-container>\r\n                </div>\r\n\r\n\r\n            </div>\r\n        </div>\r\n\r\n    </ava-card>\r\n</div>", styles: [".ava-console-approval-card-container ava-card h2{text-align:left;margin:0;font-size:24px;font-weight:700}::ng-deep .ava-console-approval-card-container ava-card .ava-card-container .card-wrapper{margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-header{margin-bottom:24px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper{overflow-y:scroll}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents{padding:24px;margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box{margin-bottom:28px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.c-header-wrapper p{text-align:left;margin-right:50px;padding:0;margin-top:8px;margin-bottom:0;font-size:24px;font-weight:400}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.c-header-wrapper ava-tag{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper{text-align:left}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper ava-tag{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.label-wrapper ava-tag span{border:none}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper{text-align:left;display:flex}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper ava-icon{margin-right:5px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.info-wrapper .f{margin-right:50px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content{display:flex;justify-content:space-between;width:100%}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-left{margin-right:20px;margin-top:13px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-left ava-icon{margin-right:10px}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-right{display:flex;text-align:right}::ng-deep .ava-console-approval-card-container ava-card .card-content .content-wrapper .contents .box.footer-wrapper .footer-content .footer-right ava-button{margin-right:8px}\n"] }]
        }], propDecorators: { cardData: [{
                type: Input
            }], height: [{
                type: Input
            }], contentTemplate: [{
                type: Input
            }], contentsBackground: [{
                type: Input
            }], cardContainerBackground: [{
                type: Input
            }] } });

class ImageCardComponent {
    imageUrl = '';
    name = '';
    title = 'Welcome to Console 🚀';
    imagePosition = 'left'; //default;
    getWrapperClass() {
        if (this.imagePosition === 'top' || this.imagePosition === 'bottom') {
            return 'img-card-wrapper-vertical';
        }
        return 'img-card-wrapper-horizontal';
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ImageCardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: ImageCardComponent, isStandalone: true, selector: "ava-image-card", inputs: { imageUrl: "imageUrl", name: "name", title: "title", imagePosition: "imagePosition" }, ngImport: i0, template: "<ava-card>\r\n    <div content>\r\n        <div [ngClass]=\"getWrapperClass()\">\r\n            <ng-container [ngSwitch]=\"imagePosition || 'left'\">\r\n                <!-- Image first for left or top -->\r\n                <ng-container *ngSwitchCase=\"'left'\">\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'right'\">\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n            </ng-container>\r\n        </div>\r\n    </div>\r\n</ava-card>", styles: [":host{display:block}.img-card-wrapper-horizontal{display:flex;justify-content:space-between;align-items:center;padding:1rem;gap:1rem}.img-card-wrapper-horizontal .left-wrapper img,.img-card-wrapper-horizontal .right-wrapper img{max-width:100px;height:auto;display:block}.img-card-wrapper-horizontal .left-wrapper p,.img-card-wrapper-horizontal .right-wrapper p{margin:0;font-size:1.2rem;font-weight:500}.img-card-wrapper-horizontal .left-wrapper span,.img-card-wrapper-horizontal .right-wrapper span{font-size:1rem}.img-card-wrapper-vertical{display:flex;flex-direction:column;align-items:center;padding:1rem;gap:1rem}.img-card-wrapper-vertical .top-wrapper,.img-card-wrapper-vertical .bottom-wrapper{text-align:center}.img-card-wrapper-vertical .top-wrapper img,.img-card-wrapper-vertical .bottom-wrapper img{max-width:100px;height:auto;display:block}.img-card-wrapper-vertical .top-wrapper p,.img-card-wrapper-vertical .bottom-wrapper p{margin:0;font-size:1.2rem;font-weight:500}.img-card-wrapper-vertical .top-wrapper span,.img-card-wrapper-vertical .bottom-wrapper span{font-size:1rem}\n"], dependencies: [{ kind: "component", type: CardComponent, selector: "ava-card", inputs: ["heading", "content"] }, { kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgClass, selector: "[ngClass]", inputs: ["class", "ngClass"] }, { kind: "directive", type: i1.NgSwitch, selector: "[ngSwitch]", inputs: ["ngSwitch"] }, { kind: "directive", type: i1.NgSwitchCase, selector: "[ngSwitchCase]", inputs: ["ngSwitchCase"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ImageCardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-image-card', standalone: true, imports: [CardComponent, CommonModule], changeDetection: ChangeDetectionStrategy.OnPush, template: "<ava-card>\r\n    <div content>\r\n        <div [ngClass]=\"getWrapperClass()\">\r\n            <ng-container [ngSwitch]=\"imagePosition || 'left'\">\r\n                <!-- Image first for left or top -->\r\n                <ng-container *ngSwitchCase=\"'left'\">\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'right'\">\r\n                    <div class=\"right-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"left-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'top'\">\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                </ng-container>\r\n\r\n                <ng-container *ngSwitchCase=\"'bottom'\">\r\n                    <div class=\"bottom-wrapper\">\r\n                        <p>{{ title }}</p>\r\n                        <span>{{ name }}</span>\r\n                    </div>\r\n                    <div class=\"top-wrapper\">\r\n                        <img [src]=\"imageUrl\" />\r\n                    </div>\r\n                </ng-container>\r\n            </ng-container>\r\n        </div>\r\n    </div>\r\n</ava-card>", styles: [":host{display:block}.img-card-wrapper-horizontal{display:flex;justify-content:space-between;align-items:center;padding:1rem;gap:1rem}.img-card-wrapper-horizontal .left-wrapper img,.img-card-wrapper-horizontal .right-wrapper img{max-width:100px;height:auto;display:block}.img-card-wrapper-horizontal .left-wrapper p,.img-card-wrapper-horizontal .right-wrapper p{margin:0;font-size:1.2rem;font-weight:500}.img-card-wrapper-horizontal .left-wrapper span,.img-card-wrapper-horizontal .right-wrapper span{font-size:1rem}.img-card-wrapper-vertical{display:flex;flex-direction:column;align-items:center;padding:1rem;gap:1rem}.img-card-wrapper-vertical .top-wrapper,.img-card-wrapper-vertical .bottom-wrapper{text-align:center}.img-card-wrapper-vertical .top-wrapper img,.img-card-wrapper-vertical .bottom-wrapper img{max-width:100px;height:auto;display:block}.img-card-wrapper-vertical .top-wrapper p,.img-card-wrapper-vertical .bottom-wrapper p{margin:0;font-size:1.2rem;font-weight:500}.img-card-wrapper-vertical .top-wrapper span,.img-card-wrapper-vertical .bottom-wrapper span{font-size:1rem}\n"] }]
        }], propDecorators: { imageUrl: [{
                type: Input
            }], name: [{
                type: Input
            }], title: [{
                type: Input
            }], imagePosition: [{
                type: Input
            }] } });

class TextCardComponent {
    iconName = 'trending-up';
    title = '';
    value = '';
    description = '';
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: TextCardComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: TextCardComponent, isStandalone: true, selector: "ava-text-card", inputs: { iconName: "iconName", title: "title", value: "value", description: "description" }, ngImport: i0, template: "<ava-card>\r\n    <div header>\r\n        <div class=\"card-header\">\r\n            <div class=\"icon-circle\">\r\n                <ava-icon [iconName]=\"iconName\" [iconSize]=\"24\"></ava-icon>\r\n            </div>\r\n            <div class=\"text-card\">\r\n                <h3>{{ title }}</h3>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div content>\r\n        <div class=\"card-content\">\r\n            <h2>{{ value }}</h2>\r\n        </div>\r\n    </div>\r\n\r\n    <div footer>\r\n        <div class=\"card-footer\">\r\n            <p>{{ description }}</p>\r\n        </div>\r\n    </div>\r\n</ava-card>", styles: [":host{display:block;min-width:375px;min-height:200px}.ava-card .card-header{display:flex;align-items:center;padding:1rem;gap:12px}.ava-card .card-header .icon-circle{border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.ava-card .card-content h2{margin:0;font-size:40px;font-weight:700}.ava-card .card-footer p{font-size:14px;margin:0}.text-card{display:flex;flex-direction:column;justify-content:center;padding-left:10px}.text-card h3{margin:0 0 4px;font-size:1.1rem;font-weight:600}\n"], dependencies: [{ kind: "component", type: CardComponent, selector: "ava-card", inputs: ["heading", "content"] }, { kind: "ngmodule", type: CommonModule }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: TextCardComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-text-card', standalone: true, imports: [CardComponent, CommonModule, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<ava-card>\r\n    <div header>\r\n        <div class=\"card-header\">\r\n            <div class=\"icon-circle\">\r\n                <ava-icon [iconName]=\"iconName\" [iconSize]=\"24\"></ava-icon>\r\n            </div>\r\n            <div class=\"text-card\">\r\n                <h3>{{ title }}</h3>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div content>\r\n        <div class=\"card-content\">\r\n            <h2>{{ value }}</h2>\r\n        </div>\r\n    </div>\r\n\r\n    <div footer>\r\n        <div class=\"card-footer\">\r\n            <p>{{ description }}</p>\r\n        </div>\r\n    </div>\r\n</ava-card>", styles: [":host{display:block;min-width:375px;min-height:200px}.ava-card .card-header{display:flex;align-items:center;padding:1rem;gap:12px}.ava-card .card-header .icon-circle{border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.ava-card .card-content h2{margin:0;font-size:40px;font-weight:700}.ava-card .card-footer p{font-size:14px;margin:0}.text-card{display:flex;flex-direction:column;justify-content:center;padding-left:10px}.text-card h3{margin:0 0 4px;font-size:1.1rem;font-weight:600}\n"] }]
        }], propDecorators: { iconName: [{
                type: Input
            }], title: [{
                type: Input
            }], value: [{
                type: Input
            }], description: [{
                type: Input
            }] } });

class DropdownComponent {
    elementRef;
    cdr;
    dropdownTitle = 'Select a Category';
    options = [];
    suboptions = {};
    checkboxOptions = [];
    iconOptions = [];
    search = false;
    enableSearch = false;
    selectedValue = '';
    singleSelect = false;
    dropdownIcon = 'chevron-down'; // Icon for dropdown toggle
    disabled = false; // Disable entire dropdown
    selectionChange = new EventEmitter();
    valueChange = new EventEmitter();
    isOpen = false;
    searchTerm = '';
    selectedOptions = [];
    filteredOptions = [];
    expandedOption = null;
    // Keyboard navigation properties
    focusedOptionIndex = -1;
    focusedSubOptionIndex = -1;
    isNavigatingSubOptions = false;
    // Static property to track all dropdown instances
    static allDropdowns = [];
    value = '';
    constructor(elementRef, cdr) {
        this.elementRef = elementRef;
        this.cdr = cdr;
        // Add this instance to the static array
        DropdownComponent.allDropdowns.push(this);
    }
    // ControlValueAccessor 
    onChange = (value) => { };
    onTouched = () => { };
    // Required by ControlValueAccessor
    writeValue(obj) {
        this.value = obj;
        // Optional: update internal state/UI if needed
    }
    registerOnChange(fn) {
        this.onChange = fn;
    }
    registerOnTouched(fn) {
        this.onTouched = fn;
    }
    //
    ngOnInit() {
        this.filteredOptions = [...this.options];
    }
    ngOnDestroy() {
        // Remove this instance from the static array
        const index = DropdownComponent.allDropdowns.indexOf(this);
        if (index > -1) {
            DropdownComponent.allDropdowns.splice(index, 1);
        }
    }
    // Static method to close all other dropdowns except the current one
    static closeAllDropdownsExcept(currentDropdown) {
        console.log('Closing other dropdowns, total dropdowns:', DropdownComponent.allDropdowns.length);
        DropdownComponent.allDropdowns.forEach(dropdown => {
            if (dropdown !== currentDropdown && dropdown.isOpen) {
                console.log('Closing dropdown');
                dropdown.isOpen = false;
                dropdown.expandedOption = null;
                dropdown.resetFocusStates();
                // Trigger change detection for the closed dropdown
                dropdown.cdr.detectChanges();
            }
        });
    }
    toggleDropdown() {
        // Don't toggle if dropdown is disabled
        if (this.disabled) {
            return;
        }
        console.log('Toggle dropdown called, current state:', this.isOpen);
        if (this.isOpen) {
            // Closing dropdown
            this.isOpen = false;
            this.expandedOption = null;
            this.resetFocusStates();
        }
        else {
            // Opening dropdown - this will automatically close others
            console.log('Opening dropdown, will close others first');
            DropdownComponent.closeAllDropdownsExcept(this);
            this.isOpen = true;
        }
    }
    closeDropdown() {
        this.isOpen = false;
        this.searchTerm = '';
        this.filteredOptions = [...this.options];
        this.expandedOption = null;
        this.resetFocusStates();
    }
    resetFocusStates() {
        this.focusedOptionIndex = -1;
        this.focusedSubOptionIndex = -1;
        this.isNavigatingSubOptions = false;
    }
    onSearch() {
        if (!this.searchTerm.trim()) {
            this.filteredOptions = [...this.options];
            return;
        }
        const searchLower = this.searchTerm.toLowerCase();
        this.filteredOptions = this.options.filter(option => {
            // Check if main option matches
            const mainOptionMatches = option.name.toLowerCase().includes(searchLower);
            // Check if any sub-option matches
            const subOptionsMatch = this.suboptions[option.name]?.some(subOption => subOption.name.toLowerCase().includes(searchLower)) || false;
            return mainOptionMatches || subOptionsMatch;
        });
    }
    selectOption(option) {
        // Don't select if option is disabled or dropdown is disabled
        if (option.disabled || this.disabled) {
            return;
        }
        if (this.checkboxOptions.includes(option.name)) {
            this.handleCheckboxSelection(option);
        }
        else {
            this.selectedOptions = [option];
            this.selectedValue = option.name;
            // Close dropdown for single select options and reset expanded state
            this.isOpen = false;
            this.expandedOption = null;
            this.emitChanges();
        }
        this.value = this.selectedValue;
        this.onChange(this.selectedValue);
        this.onTouched();
    }
    handleCheckboxSelection(option) {
        // Don't handle if option is disabled
        if (option.disabled) {
            return;
        }
        const index = this.selectedOptions.findIndex(opt => opt.value === option.value);
        if (index > -1) {
            this.selectedOptions.splice(index, 1);
        }
        else {
            if (this.singleSelect) {
                this.selectedOptions = [option];
            }
            else {
                this.selectedOptions.push(option);
            }
        }
        this.emitChanges();
    }
    selectSubOption(subOption) {
        // Don't select if sub-option is disabled or dropdown is disabled
        if (subOption.disabled || this.disabled) {
            return;
        }
        // Treat sub-option selection like regular option selection
        this.selectedOptions = [subOption];
        this.selectedValue = subOption.name;
        // Close dropdown after selecting sub-option
        this.isOpen = false;
        this.expandedOption = null;
        this.emitChanges();
    }
    toggleSubOptions(optionName) {
        // Don't toggle if dropdown is disabled
        if (this.disabled) {
            return;
        }
        // If clicking the same option, close it; otherwise, open the new one
        this.expandedOption = this.expandedOption === optionName ? null : optionName;
    }
    isOptionSelected(option) {
        return this.selectedOptions.some(opt => opt.value === option.value);
    }
    isSubOptionSelected(subOption) {
        return this.selectedOptions.some(opt => opt.value === subOption.value);
    }
    hasSubOptions(optionName) {
        return !!(this.suboptions[optionName] && this.suboptions[optionName].length > 0);
    }
    getOptionIcon(option) {
        // If option has a custom icon, use it
        if (option.icon) {
            return option.icon;
        }
        // Otherwise, use the default icon for options in iconOptions array
        return 'circle-check';
    }
    shouldShowIcon(option) {
        // Show icon if option has custom icon or if it's in iconOptions array
        return !!(option.icon || this.iconOptions.includes(option.name));
    }
    isOptionDisabled(option) {
        return !!(option.disabled || this.disabled);
    }
    isSubOptionDisabled(subOption) {
        return !!(subOption.disabled || this.disabled);
    }
    getDisplayText() {
        if (this.selectedOptions.length === 0) {
            return this.dropdownTitle;
        }
        if (this.selectedOptions.length === 1) {
            return this.selectedOptions[0].name;
        }
        return `${this.selectedOptions.length} items selected`;
    }
    emitChanges() {
        const data = {
            selectedOptions: this.selectedOptions,
            selectedValue: this.selectedValue
        };
        this.selectionChange.emit(data);
        this.valueChange.emit(data);
    }
    onDocumentClick(event) {
        const target = event.target;
        const clickedDropdown = target.closest('ava-dropdown');
        // If clicked outside this dropdown, close it
        if (!this.elementRef.nativeElement.contains(target)) {
            this.closeDropdown();
        }
        // If a dropdown toggle was clicked, close all other dropdowns
        if (clickedDropdown && target.closest('.dropdown-toggle')) {
            console.log('Dropdown toggle clicked, closing others');
            DropdownComponent.allDropdowns.forEach(dropdown => {
                if (dropdown.elementRef.nativeElement !== clickedDropdown && dropdown.isOpen) {
                    console.log('Closing other dropdown via document click');
                    dropdown.isOpen = false;
                    dropdown.expandedOption = null;
                    dropdown.resetFocusStates();
                    dropdown.cdr.detectChanges();
                }
            });
        }
    }
    // Close dropdown when clicking on the toggle button while open
    onToggleClick(event) {
        event.stopPropagation();
        this.toggleDropdown();
    }
    // Keyboard navigation methods
    onToggleKeyDown(event) {
        const actions = {
            'Enter': () => this.handleToggleActivation(),
            ' ': () => this.handleToggleActivation(),
            'ArrowDown': () => this.openAndFocus('first'),
            'ArrowUp': () => this.openAndFocus('last'),
            'Escape': () => this.isOpen && this.closeDropdown()
        };
        const action = actions[event.key];
        if (action) {
            event.preventDefault();
            action();
        }
    }
    onDropdownKeyDown(event) {
        if (!this.isOpen)
            return;
        const actions = {
            'Escape': () => { this.closeDropdown(); this.focusToggle(); },
            'Tab': () => this.closeDropdown()
        };
        const action = actions[event.key];
        if (action) {
            if (event.key !== 'Tab')
                event.preventDefault();
            action();
        }
    }
    onSearchKeyDown(event) {
        const actions = {
            'ArrowDown': () => {
                if (this.filteredOptions.length > 0) {
                    this.focusOption(0);
                }
            },
            'ArrowUp': () => {
                if (this.filteredOptions.length > 0) {
                    this.focusOption(this.filteredOptions.length - 1);
                }
            },
            'Escape': () => {
                this.closeDropdown();
                this.focusToggle();
            },
            'Enter': () => {
                if (this.filteredOptions.length > 0) {
                    this.selectOption(this.filteredOptions[0]);
                }
            }
        };
        const action = actions[event.key];
        if (action) {
            event.preventDefault();
            action();
        }
    }
    onOptionKeyDown(event, option) {
        const actions = {
            'Enter': () => this.handleOptionActivation(option),
            ' ': () => this.handleOptionActivation(option),
            'ArrowDown': () => this.navigateOptions(1),
            'ArrowUp': () => this.navigateOptions(-1),
            'ArrowRight': () => this.expandSubOptions(option),
            'Escape': () => { this.closeDropdown(); this.focusToggle(); },
            'Home': () => this.focusOption(0),
            'End': () => this.focusOption(this.filteredOptions.length - 1)
        };
        const action = actions[event.key];
        if (action) {
            event.preventDefault();
            action();
        }
    }
    onSubOptionKeyDown(event, subOption) {
        const actions = {
            'Enter': () => this.selectSubOption(subOption),
            ' ': () => this.selectSubOption(subOption),
            'ArrowDown': () => this.navigateSubOptions(1),
            'ArrowUp': () => this.navigateSubOptions(-1),
            'ArrowLeft': () => { this.expandedOption = null; this.focusOption(this.focusedOptionIndex); },
            'Escape': () => { this.closeDropdown(); this.focusToggle(); }
        };
        const action = actions[event.key];
        if (action) {
            event.preventDefault();
            action();
        }
    }
    // Optimized helper methods
    handleToggleActivation() {
        this.toggleDropdown();
        if (this.isOpen) {
            if (this.search || this.enableSearch) {
                this.focusSearchInput();
            }
            else {
                this.focusOption(0);
            }
        }
    }
    openAndFocus(position) {
        if (!this.isOpen)
            this.toggleDropdown();
        if (this.search || this.enableSearch) {
            this.focusSearchInput();
        }
        else {
            this.focusOption(position === 'first' ? 0 : this.filteredOptions.length - 1);
        }
    }
    handleOptionActivation(option) {
        if (this.hasSubOptions(option.name)) {
            this.toggleSubOptions(option.name);
            if (this.expandedOption === option.name)
                this.focusSubOption(0);
        }
        else {
            this.selectOption(option);
        }
    }
    expandSubOptions(option) {
        if (this.hasSubOptions(option.name)) {
            this.expandedOption = option.name;
            this.focusSubOption(0);
        }
    }
    navigateOptions(direction) {
        const newIndex = this.focusedOptionIndex + direction;
        // If going up from first option and search is enabled, focus search
        if (direction === -1 && this.focusedOptionIndex === 0 && (this.search || this.enableSearch)) {
            this.focusSearchInput();
            this.resetFocusStates();
            return;
        }
        if (newIndex >= 0 && newIndex < this.filteredOptions.length) {
            this.focusOption(newIndex);
        }
    }
    navigateSubOptions(direction) {
        const currentOption = this.filteredOptions[this.focusedOptionIndex];
        const subOptions = this.suboptions[currentOption.name];
        if (!subOptions)
            return;
        const newIndex = this.focusedSubOptionIndex + direction;
        if (newIndex >= 0 && newIndex < subOptions.length) {
            this.focusSubOption(newIndex);
        }
    }
    focusOption(index) {
        this.focusedOptionIndex = index;
        this.isNavigatingSubOptions = false;
        this.focusedSubOptionIndex = -1;
        setTimeout(() => this.focusElement('.option', index), 0);
    }
    focusSubOption(index) {
        this.focusedSubOptionIndex = index;
        this.isNavigatingSubOptions = true;
        setTimeout(() => this.focusElement('.suboption', index), 0);
    }
    focusElement(selector, index) {
        const elements = this.elementRef.nativeElement.querySelectorAll(selector);
        if (elements[index]) {
            elements[index].focus();
        }
    }
    focusSearchInput() {
        setTimeout(() => {
            const searchInput = this.elementRef.nativeElement.querySelector('.search-box input');
            if (searchInput)
                searchInput.focus();
        }, 0);
    }
    focusToggle() {
        const toggle = this.elementRef.nativeElement.querySelector('.dropdown-toggle');
        if (toggle)
            toggle.focus();
    }
    // Calculate position for sub-options based on option index
    getSuboptionPosition(optionIndex) {
        // Calculate position relative to the dropdown 
        const toggleHeight = 50;
        const dropdownMenuTop = 2;
        const searchBoxHeight = (this.search || this.enableSearch) ? 40 : 0;
        const optionHeight = 40; // Height of each option
        const optionsContainerPadding = 0; // No padding on options container
        // Position to align the first sub-option with the hovered main option
        return toggleHeight + dropdownMenuTop + searchBoxHeight + optionsContainerPadding + (optionIndex * optionHeight);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: DropdownComponent, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: DropdownComponent, isStandalone: true, selector: "ava-dropdown", inputs: { dropdownTitle: "dropdownTitle", options: "options", suboptions: "suboptions", checkboxOptions: "checkboxOptions", iconOptions: "iconOptions", search: "search", enableSearch: "enableSearch", selectedValue: "selectedValue", singleSelect: "singleSelect", dropdownIcon: "dropdownIcon", disabled: "disabled" }, outputs: { selectionChange: "selectionChange", valueChange: "valueChange" }, host: { listeners: { "document:click": "onDocumentClick($event)" } }, providers: [
            {
                provide: NG_VALUE_ACCESSOR,
                useExisting: forwardRef(() => DropdownComponent),
                multi: true
            }
        ], ngImport: i0, template: "<div class=\"ava-dropdown\" [class.open]=\"isOpen\" (keydown)=\"onDropdownKeyDown($event)\">\r\n  <!-- Dropdown Toggle -->\r\n  <button class=\"dropdown-toggle\"\r\n          [class.open]=\"isOpen\"\r\n          [class.disabled]=\"disabled\"\r\n          [disabled]=\"disabled\"\r\n          (click)=\"onToggleClick($event)\"\r\n          (keydown)=\"onToggleKeyDown($event)\"\r\n          [attr.aria-expanded]=\"isOpen\"\r\n          [attr.aria-haspopup]=\"'listbox'\"\r\n          [attr.aria-label]=\"getDisplayText()\">\r\n    <span>{{ getDisplayText() }}</span>\r\n    <ava-icon [iconName]=\"dropdownIcon\" [iconSize]=\"16\" [style.transform]=\"isOpen ? 'rotate(180deg)' : 'rotate(0deg)'\"></ava-icon>\r\n  </button>\r\n\r\n  <!-- Dropdown Menu -->\r\n  <div class=\"dropdown-menu\"\r\n       *ngIf=\"isOpen\"\r\n       role=\"listbox\"\r\n       [attr.aria-label]=\"dropdownTitle\">\r\n\r\n    <!-- Search -->\r\n    <div class=\"search-box\" *ngIf=\"search || enableSearch\">\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Search...\"\r\n        [(ngModel)]=\"searchTerm\"\r\n        (input)=\"onSearch()\"\r\n        (keydown)=\"onSearchKeyDown($event)\"\r\n        [attr.aria-label]=\"'Search ' + dropdownTitle\"\r\n        tabindex=\"0\"\r\n        autocomplete=\"off\">\r\n      <ava-icon [iconName]=\"'search'\" [iconSize]=\"14\"></ava-icon>\r\n    </div>\r\n\r\n    <!-- Options -->\r\n    <div class=\"options\" role=\"group\">\r\n      <div *ngFor=\"let option of filteredOptions; let i = index\" class=\"option-group\">\r\n\r\n        <!-- Main Option -->\r\n        <div class=\"option\"\r\n             [class.selected]=\"isOptionSelected(option)\"\r\n             [class.has-suboptions]=\"hasSubOptions(option.name)\"\r\n             [class.checkbox-option]=\"checkboxOptions.includes(option.name)\"\r\n             [class.focused]=\"focusedOptionIndex === i\"\r\n             [class.disabled]=\"isOptionDisabled(option)\"\r\n             (click)=\"hasSubOptions(option.name) ? toggleSubOptions(option.name) : selectOption(option); $event.stopPropagation()\"\r\n             (keydown)=\"onOptionKeyDown($event, option)\"\r\n             [attr.tabindex]=\"focusedOptionIndex === i ? '0' : '-1'\"\r\n             [attr.role]=\"'option'\"\r\n             [attr.aria-selected]=\"isOptionSelected(option)\"\r\n             [attr.aria-expanded]=\"hasSubOptions(option.name) ? (expandedOption === option.name) : null\"\r\n             [attr.aria-label]=\"option.name\"\r\n             [attr.aria-disabled]=\"isOptionDisabled(option)\">\r\n\r\n          <!-- With Checkbox -->\r\n          <ava-checkbox\r\n            *ngIf=\"checkboxOptions.includes(option.name)\"\r\n            variant=\"with-bg\"\r\n            [isChecked]=\"isOptionSelected(option)\"\r\n            [label]=\"option.name\"\r\n            [disable]=\"isOptionDisabled(option)\">\r\n          </ava-checkbox>\r\n\r\n          <!-- Without Checkbox -->\r\n          <div *ngIf=\"!checkboxOptions.includes(option.name)\" class=\"option-content\">\r\n            <ava-icon\r\n              *ngIf=\"shouldShowIcon(option)\"\r\n              [iconName]=\"getOptionIcon(option)\"\r\n              [iconSize]=\"16\">\r\n            </ava-icon>\r\n            <span>{{ option.name }}</span>\r\n          </div>\r\n\r\n          <!-- Sub-options arrow: down by default, right when expanded -->\r\n          <ava-icon\r\n            *ngIf=\"hasSubOptions(option.name)\"\r\n            [iconName]=\"expandedOption === option.name ? 'chevron-right' : 'chevron-down'\"\r\n            [iconSize]=\"12\"\r\n            (click)=\"toggleSubOptions(option.name); $event.stopPropagation()\">\r\n          </ava-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No results -->\r\n    <div *ngIf=\"filteredOptions.length === 0\" class=\"no-results\">\r\n      No options found\r\n    </div>\r\n  </div>\r\n\r\n    <!-- Sub-options positioned next to hovered option -->\r\n    <ng-container *ngFor=\"let option of filteredOptions; let optionIndex = index\">\r\n      <div class=\"suboptions-overlay\"\r\n           *ngIf=\"expandedOption === option.name && isOpen\"\r\n           [style.top.px]=\"getSuboptionPosition(optionIndex)\"\r\n           role=\"listbox\"\r\n           [attr.aria-label]=\"'Sub-options for ' + option.name\">\r\n        <div class=\"suboptions-panel\">\r\n          <div *ngFor=\"let subOption of suboptions[option.name]; let i = index\"\r\n               class=\"suboption\"\r\n               [class.selected]=\"isSubOptionSelected(subOption)\"\r\n               [class.focused]=\"focusedSubOptionIndex === i\"\r\n               [class.disabled]=\"isSubOptionDisabled(subOption)\"\r\n               (click)=\"selectSubOption(subOption); $event.stopPropagation()\"\r\n               (keydown)=\"onSubOptionKeyDown($event, subOption)\"\r\n               [attr.tabindex]=\"focusedSubOptionIndex === i ? '0' : '-1'\"\r\n               [attr.role]=\"'option'\"\r\n               [attr.aria-selected]=\"isSubOptionSelected(subOption)\"\r\n               [attr.aria-label]=\"subOption.name\"\r\n               [attr.aria-disabled]=\"isSubOptionDisabled(subOption)\">\r\n            <span>{{ subOption.name }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n", styles: [".ava-dropdown{position:relative;display:inline-block;width:250px;overflow:visible}.ava-dropdown .dropdown-toggle{display:flex;align-items:center;justify-content:space-between;width:100%;padding:var(--dropdown-toggle-padding);background:var(--dropdown-toggle-background);border:var(--dropdown-toggle-background) solid 1px;border-radius:var(--dropdown-toggle-border-radius);cursor:pointer;font:var(--dropdown-toggle-font);color:var(--dropdown-toggle-text);transition:var(--dropdown-transition);min-height:var(--dropdown-size-md-height);box-shadow:0 2px 4px #0000001a;margin-bottom:5px}.ava-dropdown .dropdown-toggle:hover{border-color:var(--color-brand-primary)}.ava-dropdown .dropdown-toggle:focus{outline:none;border-color:var(--color-brand-primary)}.ava-dropdown .dropdown-toggle.open{border-color:var(--color-brand-primary);border:var(--color-brand-primary) solid 2px}.ava-dropdown .dropdown-toggle.disabled{cursor:not-allowed;opacity:.6;background:var(--color-background-disabled);color:var(--color-text-disabled)}.ava-dropdown .dropdown-toggle.disabled:hover{border-color:var(--color-border-disabled)}.ava-dropdown .dropdown-toggle ava-icon{transition:transform .2s ease;color:var(--dropdown-toggle-icon-color)}.ava-dropdown .dropdown-menu{position:absolute;top:calc(100% + 2px);left:0;right:0;z-index:1000;background:var(--dropdown-menu-background);border:var(--dropdown-menu-border) solid 1px;border-radius:var(--dropdown-menu-border-radius);box-shadow:var(--dropdown-menu-shadow);max-height:240px;overflow-y:auto;overflow-x:hidden;opacity:0;transform:translateY(-8px);animation:dropdownSlideDown .2s ease forwards}.ava-dropdown .search-box{position:relative;padding:var(--dropdown-menu-padding);background:var(--dropdown-search-background)}.ava-dropdown .search-box input{width:100%;height:var(--dropdown-size-sm-height);padding:var(--dropdown-search-padding);border:none;border-radius:var(--dropdown-search-border-radius);font:var(--dropdown-search-font);color:var(--dropdown-search-text);background:transparent;box-sizing:border-box}.ava-dropdown .search-box input::placeholder{color:var(--color-text-placeholder)}.ava-dropdown .search-box input:focus{outline:none;background:transparent}.ava-dropdown .search-box ava-icon{position:absolute;right:8px;top:50%;transform:translateY(-50%);color:var(--color-text-placeholder);pointer-events:none}.ava-dropdown .options{padding:0;overflow:visible;position:relative}.ava-dropdown .option-group{margin-bottom:0;position:relative;opacity:0;transform:translateY(-10px);animation:optionSlideIn .3s ease forwards;overflow:visible}.ava-dropdown .option-group:nth-child(1){animation-delay:50ms}.ava-dropdown .option-group:nth-child(2){animation-delay:.1s}.ava-dropdown .option-group:nth-child(3){animation-delay:.15s}.ava-dropdown .option-group:nth-child(4){animation-delay:.2s}.ava-dropdown .option-group:nth-child(5){animation-delay:.25s}.ava-dropdown .option-group:nth-child(6){animation-delay:.3s}.ava-dropdown .option-group:nth-child(7){animation-delay:.35s}.ava-dropdown .option-group:nth-child(8){animation-delay:.4s}.ava-dropdown .option-group:nth-child(9){animation-delay:.45s}.ava-dropdown .option-group:nth-child(10){animation-delay:.5s}.ava-dropdown .option-group:nth-child(11){animation-delay:.55s}.ava-dropdown .option-group:nth-child(12){animation-delay:.6s}.ava-dropdown .option-group:nth-child(13){animation-delay:.65s}.ava-dropdown .option-group:nth-child(14){animation-delay:.7s}.ava-dropdown .option-group:nth-child(15){animation-delay:.75s}.ava-dropdown .option-group:nth-child(16){animation-delay:.8s}.ava-dropdown .option-group:nth-child(17){animation-delay:.85s}.ava-dropdown .option-group:nth-child(18){animation-delay:.9s}.ava-dropdown .option-group:nth-child(19){animation-delay:.95s}.ava-dropdown .option-group:nth-child(20){animation-delay:1s}.ava-dropdown .option-group:nth-child(21){animation-delay:1.05s}.ava-dropdown .option-group:nth-child(22){animation-delay:1.1s}.ava-dropdown .option-group:nth-child(23){animation-delay:1.15s}.ava-dropdown .option-group:nth-child(24){animation-delay:1.2s}.ava-dropdown .option-group:nth-child(25){animation-delay:1.25s}.ava-dropdown .option-group:nth-child(26){animation-delay:1.3s}.ava-dropdown .option-group:nth-child(27){animation-delay:1.35s}.ava-dropdown .option-group:nth-child(28){animation-delay:1.4s}.ava-dropdown .option-group:nth-child(29){animation-delay:1.45s}.ava-dropdown .option-group:nth-child(30){animation-delay:1.5s}.ava-dropdown .option-group:nth-child(31){animation-delay:1.55s}.ava-dropdown .option-group:nth-child(32){animation-delay:1.6s}.ava-dropdown .option-group:nth-child(33){animation-delay:1.65s}.ava-dropdown .option-group:nth-child(34){animation-delay:1.7s}.ava-dropdown .option-group:nth-child(35){animation-delay:1.75s}.ava-dropdown .option-group:nth-child(36){animation-delay:1.8s}.ava-dropdown .option-group:nth-child(37){animation-delay:1.85s}.ava-dropdown .option-group:nth-child(38){animation-delay:1.9s}.ava-dropdown .option-group:nth-child(39){animation-delay:1.95s}.ava-dropdown .option-group:nth-child(40){animation-delay:2s}.ava-dropdown .option-group:nth-child(41){animation-delay:2.05s}.ava-dropdown .option-group:nth-child(42){animation-delay:2.1s}.ava-dropdown .option-group:nth-child(43){animation-delay:2.15s}.ava-dropdown .option-group:nth-child(44){animation-delay:2.2s}.ava-dropdown .option-group:nth-child(45){animation-delay:2.25s}.ava-dropdown .option-group:nth-child(46){animation-delay:2.3s}.ava-dropdown .option-group:nth-child(47){animation-delay:2.35s}.ava-dropdown .option-group:nth-child(48){animation-delay:2.4s}.ava-dropdown .option-group:nth-child(49){animation-delay:2.45s}.ava-dropdown .option-group:nth-child(50){animation-delay:2.5s}.ava-dropdown .option{display:flex;align-items:center;padding:var(--dropdown-item-padding);cursor:pointer;transition:var(--dropdown-item-transition);font:var(--dropdown-item-font);color:var(--dropdown-item-text);border-radius:0;margin:0;position:relative;min-height:var(--dropdown-size-md-height);line-height:1.5;background:var(--dropdown-item-background);border-left:3px solid transparent}.ava-dropdown .option:hover:not(.checkbox-option),.ava-dropdown .option:focus:not(.checkbox-option),.ava-dropdown .option.focused:not(.checkbox-option){background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .option.selected:not(.checkbox-option){background:var(--color-brand-primary);color:var(--dropdown-item-text-active);border-left-color:var(--color-brand-primary)}.ava-dropdown .option.checkbox-option:hover,.ava-dropdown .option.checkbox-option:focus,.ava-dropdown .option.checkbox-option.focused{background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .option.checkbox-option.selected{background:transparent;color:var(--dropdown-item-text-active)}.ava-dropdown .option.has-suboptions{position:relative}.ava-dropdown .option.disabled{cursor:not-allowed;opacity:.5;color:var(--color-text-disabled)}.ava-dropdown .option.disabled:hover,.ava-dropdown .option.disabled:focus,.ava-dropdown .option.disabled.focused{background:var(--dropdown-item-background);color:var(--color-text-disabled);box-shadow:none}.ava-dropdown .option.disabled.selected{background:var(--dropdown-item-background);color:var(--color-text-disabled);border-left-color:transparent}.ava-dropdown .option .option-content{display:flex;align-items:center;gap:8px;flex:1}.ava-dropdown .option ava-icon{color:currentColor;transition:all .2s ease}.ava-dropdown .option ava-checkbox{margin-right:8px}.ava-dropdown .suboptions-overlay{position:absolute;left:calc(100% + 4px);top:0;z-index:9999;pointer-events:auto}.ava-dropdown .suboptions-panel{background:var(--dropdown-submenu-background);border:var(--dropdown-menu-border) solid 1px;border-radius:var(--dropdown-submenu-border-radius);box-shadow:var(--dropdown-submenu-shadow);min-width:200px;max-height:240px;overflow-y:auto;overflow-x:hidden;opacity:0;transform:translate(-5px);animation:slideInFromLeft .2s ease forwards}.ava-dropdown .suboptions-panel .suboption{padding:var(--dropdown-item-padding);cursor:pointer;transition:var(--dropdown-item-transition);font:var(--dropdown-item-font);color:var(--dropdown-item-text);margin:0;border-radius:0;line-height:1.5;min-height:var(--dropdown-size-md-height);display:flex;align-items:center;background:var(--dropdown-item-background);border-left:3px solid transparent}.ava-dropdown .suboptions-panel .suboption:hover,.ava-dropdown .suboptions-panel .suboption:focus,.ava-dropdown .suboptions-panel .suboption.focused{background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .suboptions-panel .suboption.selected{background:var(--color-brand-primary);color:var(--dropdown-item-text-active);border-left-color:var(--color-brand-primary)}.ava-dropdown .suboptions-panel .suboption.disabled{cursor:not-allowed;opacity:.5;color:var(--color-text-disabled)}.ava-dropdown .suboptions-panel .suboption.disabled:hover,.ava-dropdown .suboptions-panel .suboption.disabled:focus,.ava-dropdown .suboptions-panel .suboption.disabled.focused{background:var(--dropdown-item-background);color:var(--color-text-disabled);box-shadow:none}.ava-dropdown .suboptions-panel .suboption.disabled.selected{background:var(--dropdown-item-background);color:var(--color-text-disabled);border-left-color:transparent}.ava-dropdown .no-results{padding:var(--dropdown-item-padding);text-align:center;color:var(--color-text-placeholder);font:var(--dropdown-item-font);font-style:italic}@keyframes dropdownSlideDown{0%{opacity:0;transform:translateY(-8px)}to{opacity:1;transform:translateY(0)}}@keyframes optionSlideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes slideInFromLeft{0%{opacity:0;transform:translate(-5px)}to{opacity:1;transform:translate(0)}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgForOf, selector: "[ngFor][ngForOf]", inputs: ["ngForOf", "ngForTrackBy", "ngForTemplate"] }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "ngmodule", type: FormsModule }, { kind: "directive", type: i2$1.DefaultValueAccessor, selector: "input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]" }, { kind: "directive", type: i2$1.NgControlStatus, selector: "[formControlName],[ngModel],[formControl]" }, { kind: "directive", type: i2$1.NgModel, selector: "[ngModel]:not([formControlName]):not([formControl])", inputs: ["name", "disabled", "ngModel", "ngModelOptions"], outputs: ["ngModelChange"], exportAs: ["ngModel"] }, { kind: "component", type: CheckboxComponent, selector: "ava-checkbox", inputs: ["variant", "size", "label", "isChecked", "indeterminate", "disable"], outputs: ["isCheckedChange"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: DropdownComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-dropdown', imports: [CommonModule, FormsModule, CheckboxComponent, IconComponent], providers: [
                        {
                            provide: NG_VALUE_ACCESSOR,
                            useExisting: forwardRef(() => DropdownComponent),
                            multi: true
                        }
                    ], template: "<div class=\"ava-dropdown\" [class.open]=\"isOpen\" (keydown)=\"onDropdownKeyDown($event)\">\r\n  <!-- Dropdown Toggle -->\r\n  <button class=\"dropdown-toggle\"\r\n          [class.open]=\"isOpen\"\r\n          [class.disabled]=\"disabled\"\r\n          [disabled]=\"disabled\"\r\n          (click)=\"onToggleClick($event)\"\r\n          (keydown)=\"onToggleKeyDown($event)\"\r\n          [attr.aria-expanded]=\"isOpen\"\r\n          [attr.aria-haspopup]=\"'listbox'\"\r\n          [attr.aria-label]=\"getDisplayText()\">\r\n    <span>{{ getDisplayText() }}</span>\r\n    <ava-icon [iconName]=\"dropdownIcon\" [iconSize]=\"16\" [style.transform]=\"isOpen ? 'rotate(180deg)' : 'rotate(0deg)'\"></ava-icon>\r\n  </button>\r\n\r\n  <!-- Dropdown Menu -->\r\n  <div class=\"dropdown-menu\"\r\n       *ngIf=\"isOpen\"\r\n       role=\"listbox\"\r\n       [attr.aria-label]=\"dropdownTitle\">\r\n\r\n    <!-- Search -->\r\n    <div class=\"search-box\" *ngIf=\"search || enableSearch\">\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Search...\"\r\n        [(ngModel)]=\"searchTerm\"\r\n        (input)=\"onSearch()\"\r\n        (keydown)=\"onSearchKeyDown($event)\"\r\n        [attr.aria-label]=\"'Search ' + dropdownTitle\"\r\n        tabindex=\"0\"\r\n        autocomplete=\"off\">\r\n      <ava-icon [iconName]=\"'search'\" [iconSize]=\"14\"></ava-icon>\r\n    </div>\r\n\r\n    <!-- Options -->\r\n    <div class=\"options\" role=\"group\">\r\n      <div *ngFor=\"let option of filteredOptions; let i = index\" class=\"option-group\">\r\n\r\n        <!-- Main Option -->\r\n        <div class=\"option\"\r\n             [class.selected]=\"isOptionSelected(option)\"\r\n             [class.has-suboptions]=\"hasSubOptions(option.name)\"\r\n             [class.checkbox-option]=\"checkboxOptions.includes(option.name)\"\r\n             [class.focused]=\"focusedOptionIndex === i\"\r\n             [class.disabled]=\"isOptionDisabled(option)\"\r\n             (click)=\"hasSubOptions(option.name) ? toggleSubOptions(option.name) : selectOption(option); $event.stopPropagation()\"\r\n             (keydown)=\"onOptionKeyDown($event, option)\"\r\n             [attr.tabindex]=\"focusedOptionIndex === i ? '0' : '-1'\"\r\n             [attr.role]=\"'option'\"\r\n             [attr.aria-selected]=\"isOptionSelected(option)\"\r\n             [attr.aria-expanded]=\"hasSubOptions(option.name) ? (expandedOption === option.name) : null\"\r\n             [attr.aria-label]=\"option.name\"\r\n             [attr.aria-disabled]=\"isOptionDisabled(option)\">\r\n\r\n          <!-- With Checkbox -->\r\n          <ava-checkbox\r\n            *ngIf=\"checkboxOptions.includes(option.name)\"\r\n            variant=\"with-bg\"\r\n            [isChecked]=\"isOptionSelected(option)\"\r\n            [label]=\"option.name\"\r\n            [disable]=\"isOptionDisabled(option)\">\r\n          </ava-checkbox>\r\n\r\n          <!-- Without Checkbox -->\r\n          <div *ngIf=\"!checkboxOptions.includes(option.name)\" class=\"option-content\">\r\n            <ava-icon\r\n              *ngIf=\"shouldShowIcon(option)\"\r\n              [iconName]=\"getOptionIcon(option)\"\r\n              [iconSize]=\"16\">\r\n            </ava-icon>\r\n            <span>{{ option.name }}</span>\r\n          </div>\r\n\r\n          <!-- Sub-options arrow: down by default, right when expanded -->\r\n          <ava-icon\r\n            *ngIf=\"hasSubOptions(option.name)\"\r\n            [iconName]=\"expandedOption === option.name ? 'chevron-right' : 'chevron-down'\"\r\n            [iconSize]=\"12\"\r\n            (click)=\"toggleSubOptions(option.name); $event.stopPropagation()\">\r\n          </ava-icon>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No results -->\r\n    <div *ngIf=\"filteredOptions.length === 0\" class=\"no-results\">\r\n      No options found\r\n    </div>\r\n  </div>\r\n\r\n    <!-- Sub-options positioned next to hovered option -->\r\n    <ng-container *ngFor=\"let option of filteredOptions; let optionIndex = index\">\r\n      <div class=\"suboptions-overlay\"\r\n           *ngIf=\"expandedOption === option.name && isOpen\"\r\n           [style.top.px]=\"getSuboptionPosition(optionIndex)\"\r\n           role=\"listbox\"\r\n           [attr.aria-label]=\"'Sub-options for ' + option.name\">\r\n        <div class=\"suboptions-panel\">\r\n          <div *ngFor=\"let subOption of suboptions[option.name]; let i = index\"\r\n               class=\"suboption\"\r\n               [class.selected]=\"isSubOptionSelected(subOption)\"\r\n               [class.focused]=\"focusedSubOptionIndex === i\"\r\n               [class.disabled]=\"isSubOptionDisabled(subOption)\"\r\n               (click)=\"selectSubOption(subOption); $event.stopPropagation()\"\r\n               (keydown)=\"onSubOptionKeyDown($event, subOption)\"\r\n               [attr.tabindex]=\"focusedSubOptionIndex === i ? '0' : '-1'\"\r\n               [attr.role]=\"'option'\"\r\n               [attr.aria-selected]=\"isSubOptionSelected(subOption)\"\r\n               [attr.aria-label]=\"subOption.name\"\r\n               [attr.aria-disabled]=\"isSubOptionDisabled(subOption)\">\r\n            <span>{{ subOption.name }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n", styles: [".ava-dropdown{position:relative;display:inline-block;width:250px;overflow:visible}.ava-dropdown .dropdown-toggle{display:flex;align-items:center;justify-content:space-between;width:100%;padding:var(--dropdown-toggle-padding);background:var(--dropdown-toggle-background);border:var(--dropdown-toggle-background) solid 1px;border-radius:var(--dropdown-toggle-border-radius);cursor:pointer;font:var(--dropdown-toggle-font);color:var(--dropdown-toggle-text);transition:var(--dropdown-transition);min-height:var(--dropdown-size-md-height);box-shadow:0 2px 4px #0000001a;margin-bottom:5px}.ava-dropdown .dropdown-toggle:hover{border-color:var(--color-brand-primary)}.ava-dropdown .dropdown-toggle:focus{outline:none;border-color:var(--color-brand-primary)}.ava-dropdown .dropdown-toggle.open{border-color:var(--color-brand-primary);border:var(--color-brand-primary) solid 2px}.ava-dropdown .dropdown-toggle.disabled{cursor:not-allowed;opacity:.6;background:var(--color-background-disabled);color:var(--color-text-disabled)}.ava-dropdown .dropdown-toggle.disabled:hover{border-color:var(--color-border-disabled)}.ava-dropdown .dropdown-toggle ava-icon{transition:transform .2s ease;color:var(--dropdown-toggle-icon-color)}.ava-dropdown .dropdown-menu{position:absolute;top:calc(100% + 2px);left:0;right:0;z-index:1000;background:var(--dropdown-menu-background);border:var(--dropdown-menu-border) solid 1px;border-radius:var(--dropdown-menu-border-radius);box-shadow:var(--dropdown-menu-shadow);max-height:240px;overflow-y:auto;overflow-x:hidden;opacity:0;transform:translateY(-8px);animation:dropdownSlideDown .2s ease forwards}.ava-dropdown .search-box{position:relative;padding:var(--dropdown-menu-padding);background:var(--dropdown-search-background)}.ava-dropdown .search-box input{width:100%;height:var(--dropdown-size-sm-height);padding:var(--dropdown-search-padding);border:none;border-radius:var(--dropdown-search-border-radius);font:var(--dropdown-search-font);color:var(--dropdown-search-text);background:transparent;box-sizing:border-box}.ava-dropdown .search-box input::placeholder{color:var(--color-text-placeholder)}.ava-dropdown .search-box input:focus{outline:none;background:transparent}.ava-dropdown .search-box ava-icon{position:absolute;right:8px;top:50%;transform:translateY(-50%);color:var(--color-text-placeholder);pointer-events:none}.ava-dropdown .options{padding:0;overflow:visible;position:relative}.ava-dropdown .option-group{margin-bottom:0;position:relative;opacity:0;transform:translateY(-10px);animation:optionSlideIn .3s ease forwards;overflow:visible}.ava-dropdown .option-group:nth-child(1){animation-delay:50ms}.ava-dropdown .option-group:nth-child(2){animation-delay:.1s}.ava-dropdown .option-group:nth-child(3){animation-delay:.15s}.ava-dropdown .option-group:nth-child(4){animation-delay:.2s}.ava-dropdown .option-group:nth-child(5){animation-delay:.25s}.ava-dropdown .option-group:nth-child(6){animation-delay:.3s}.ava-dropdown .option-group:nth-child(7){animation-delay:.35s}.ava-dropdown .option-group:nth-child(8){animation-delay:.4s}.ava-dropdown .option-group:nth-child(9){animation-delay:.45s}.ava-dropdown .option-group:nth-child(10){animation-delay:.5s}.ava-dropdown .option-group:nth-child(11){animation-delay:.55s}.ava-dropdown .option-group:nth-child(12){animation-delay:.6s}.ava-dropdown .option-group:nth-child(13){animation-delay:.65s}.ava-dropdown .option-group:nth-child(14){animation-delay:.7s}.ava-dropdown .option-group:nth-child(15){animation-delay:.75s}.ava-dropdown .option-group:nth-child(16){animation-delay:.8s}.ava-dropdown .option-group:nth-child(17){animation-delay:.85s}.ava-dropdown .option-group:nth-child(18){animation-delay:.9s}.ava-dropdown .option-group:nth-child(19){animation-delay:.95s}.ava-dropdown .option-group:nth-child(20){animation-delay:1s}.ava-dropdown .option-group:nth-child(21){animation-delay:1.05s}.ava-dropdown .option-group:nth-child(22){animation-delay:1.1s}.ava-dropdown .option-group:nth-child(23){animation-delay:1.15s}.ava-dropdown .option-group:nth-child(24){animation-delay:1.2s}.ava-dropdown .option-group:nth-child(25){animation-delay:1.25s}.ava-dropdown .option-group:nth-child(26){animation-delay:1.3s}.ava-dropdown .option-group:nth-child(27){animation-delay:1.35s}.ava-dropdown .option-group:nth-child(28){animation-delay:1.4s}.ava-dropdown .option-group:nth-child(29){animation-delay:1.45s}.ava-dropdown .option-group:nth-child(30){animation-delay:1.5s}.ava-dropdown .option-group:nth-child(31){animation-delay:1.55s}.ava-dropdown .option-group:nth-child(32){animation-delay:1.6s}.ava-dropdown .option-group:nth-child(33){animation-delay:1.65s}.ava-dropdown .option-group:nth-child(34){animation-delay:1.7s}.ava-dropdown .option-group:nth-child(35){animation-delay:1.75s}.ava-dropdown .option-group:nth-child(36){animation-delay:1.8s}.ava-dropdown .option-group:nth-child(37){animation-delay:1.85s}.ava-dropdown .option-group:nth-child(38){animation-delay:1.9s}.ava-dropdown .option-group:nth-child(39){animation-delay:1.95s}.ava-dropdown .option-group:nth-child(40){animation-delay:2s}.ava-dropdown .option-group:nth-child(41){animation-delay:2.05s}.ava-dropdown .option-group:nth-child(42){animation-delay:2.1s}.ava-dropdown .option-group:nth-child(43){animation-delay:2.15s}.ava-dropdown .option-group:nth-child(44){animation-delay:2.2s}.ava-dropdown .option-group:nth-child(45){animation-delay:2.25s}.ava-dropdown .option-group:nth-child(46){animation-delay:2.3s}.ava-dropdown .option-group:nth-child(47){animation-delay:2.35s}.ava-dropdown .option-group:nth-child(48){animation-delay:2.4s}.ava-dropdown .option-group:nth-child(49){animation-delay:2.45s}.ava-dropdown .option-group:nth-child(50){animation-delay:2.5s}.ava-dropdown .option{display:flex;align-items:center;padding:var(--dropdown-item-padding);cursor:pointer;transition:var(--dropdown-item-transition);font:var(--dropdown-item-font);color:var(--dropdown-item-text);border-radius:0;margin:0;position:relative;min-height:var(--dropdown-size-md-height);line-height:1.5;background:var(--dropdown-item-background);border-left:3px solid transparent}.ava-dropdown .option:hover:not(.checkbox-option),.ava-dropdown .option:focus:not(.checkbox-option),.ava-dropdown .option.focused:not(.checkbox-option){background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .option.selected:not(.checkbox-option){background:var(--color-brand-primary);color:var(--dropdown-item-text-active);border-left-color:var(--color-brand-primary)}.ava-dropdown .option.checkbox-option:hover,.ava-dropdown .option.checkbox-option:focus,.ava-dropdown .option.checkbox-option.focused{background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .option.checkbox-option.selected{background:transparent;color:var(--dropdown-item-text-active)}.ava-dropdown .option.has-suboptions{position:relative}.ava-dropdown .option.disabled{cursor:not-allowed;opacity:.5;color:var(--color-text-disabled)}.ava-dropdown .option.disabled:hover,.ava-dropdown .option.disabled:focus,.ava-dropdown .option.disabled.focused{background:var(--dropdown-item-background);color:var(--color-text-disabled);box-shadow:none}.ava-dropdown .option.disabled.selected{background:var(--dropdown-item-background);color:var(--color-text-disabled);border-left-color:transparent}.ava-dropdown .option .option-content{display:flex;align-items:center;gap:8px;flex:1}.ava-dropdown .option ava-icon{color:currentColor;transition:all .2s ease}.ava-dropdown .option ava-checkbox{margin-right:8px}.ava-dropdown .suboptions-overlay{position:absolute;left:calc(100% + 4px);top:0;z-index:9999;pointer-events:auto}.ava-dropdown .suboptions-panel{background:var(--dropdown-submenu-background);border:var(--dropdown-menu-border) solid 1px;border-radius:var(--dropdown-submenu-border-radius);box-shadow:var(--dropdown-submenu-shadow);min-width:200px;max-height:240px;overflow-y:auto;overflow-x:hidden;opacity:0;transform:translate(-5px);animation:slideInFromLeft .2s ease forwards}.ava-dropdown .suboptions-panel .suboption{padding:var(--dropdown-item-padding);cursor:pointer;transition:var(--dropdown-item-transition);font:var(--dropdown-item-font);color:var(--dropdown-item-text);margin:0;border-radius:0;line-height:1.5;min-height:var(--dropdown-size-md-height);display:flex;align-items:center;background:var(--dropdown-item-background);border-left:3px solid transparent}.ava-dropdown .suboptions-panel .suboption:hover,.ava-dropdown .suboptions-panel .suboption:focus,.ava-dropdown .suboptions-panel .suboption.focused{background:var(--dropdown-item-background-hover);color:var(--dropdown-item-text-hover)}.ava-dropdown .suboptions-panel .suboption.selected{background:var(--color-brand-primary);color:var(--dropdown-item-text-active);border-left-color:var(--color-brand-primary)}.ava-dropdown .suboptions-panel .suboption.disabled{cursor:not-allowed;opacity:.5;color:var(--color-text-disabled)}.ava-dropdown .suboptions-panel .suboption.disabled:hover,.ava-dropdown .suboptions-panel .suboption.disabled:focus,.ava-dropdown .suboptions-panel .suboption.disabled.focused{background:var(--dropdown-item-background);color:var(--color-text-disabled);box-shadow:none}.ava-dropdown .suboptions-panel .suboption.disabled.selected{background:var(--dropdown-item-background);color:var(--color-text-disabled);border-left-color:transparent}.ava-dropdown .no-results{padding:var(--dropdown-item-padding);text-align:center;color:var(--color-text-placeholder);font:var(--dropdown-item-font);font-style:italic}@keyframes dropdownSlideDown{0%{opacity:0;transform:translateY(-8px)}to{opacity:1;transform:translateY(0)}}@keyframes optionSlideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes slideInFromLeft{0%{opacity:0;transform:translate(-5px)}to{opacity:1;transform:translate(0)}}\n"] }]
        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { dropdownTitle: [{
                type: Input
            }], options: [{
                type: Input
            }], suboptions: [{
                type: Input
            }], checkboxOptions: [{
                type: Input
            }], iconOptions: [{
                type: Input
            }], search: [{
                type: Input
            }], enableSearch: [{
                type: Input
            }], selectedValue: [{
                type: Input
            }], singleSelect: [{
                type: Input
            }], dropdownIcon: [{
                type: Input
            }], disabled: [{
                type: Input
            }], selectionChange: [{
                type: Output
            }], valueChange: [{
                type: Output
            }], onDocumentClick: [{
                type: HostListener,
                args: ['document:click', ['$event']]
            }] } });

class SidebarComponent {
    cdr;
    width;
    collapsedWidth;
    showCollapseButton;
    isCollapsed;
    collapseToggle = new EventEmitter();
    _isCollapsed = false;
    constructor(cdr) {
        this.cdr = cdr;
    }
    ngOnInit() {
        this._isCollapsed = this.isCollapsed || false;
    }
    toggleCollapse() {
        this._isCollapsed = !this._isCollapsed;
        this.collapseToggle.emit(this._isCollapsed);
        this.cdr.markForCheck();
    }
    get sidebarWidth() {
        if (this._isCollapsed && this.collapsedWidth) {
            return this.collapsedWidth;
        }
        return this.width || '';
    }
    get collapsed() {
        return this._isCollapsed;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: SidebarComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: SidebarComponent, isStandalone: true, selector: "ava-sidebar", inputs: { width: "width", collapsedWidth: "collapsedWidth", showCollapseButton: "showCollapseButton", isCollapsed: "isCollapsed" }, outputs: { collapseToggle: "collapseToggle" }, ngImport: i0, template: "<div class=\"ava-sidebar\" [style.width]=\"sidebarWidth\" [class.collapsed]=\"collapsed\">\r\n    <!-- Header Section with Collapse Button -->\r\n    <div class=\"sidebar-header\">\r\n        <div class=\"header-content\" *ngIf=\"!collapsed\">\r\n            <ng-content select=\"[slot=header]\"></ng-content>\r\n        </div>\r\n        <div class=\"header-controls\" *ngIf=\"showCollapseButton\">\r\n            <ava-button\r\n                class=\"collapse-btn\"\r\n                (click)=\"toggleCollapse()\"\r\n                iconPosition=\"only\"\r\n                [iconName]=\"collapsed ? 'ArrowRight' : 'ArrowLeft'\">\r\n            </ava-button>\r\n        </div>\r\n    </div>\r\n    \r\n    <!-- Main Content Section -->\r\n    <div class=\"sidebar-content\">\r\n        <ng-content select=\"[slot=content]\"></ng-content>\r\n    </div>\r\n    \r\n    <!-- Footer Section -->\r\n    <div class=\"sidebar-footer\">\r\n        <ng-content select=\"[slot=footer]\"></ng-content>\r\n    </div>\r\n</div>", styles: [".ava-sidebar{display:flex;flex-direction:column;min-width:260px;background-color:#fff;border-right:1px solid #e5e7eb;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;box-shadow:0 1px 3px #0000001a;overflow:hidden}.ava-sidebar.collapsed{min-width:50px}.ava-sidebar .sidebar-header{display:flex;align-items:center;justify-content:space-between;padding:16px;border-bottom:1px solid #e5e7eb;background-color:#f9fafb;min-height:64px;flex-shrink:0}.ava-sidebar .sidebar-header .header-content{flex:1;margin-right:12px;overflow:hidden}.ava-sidebar .sidebar-header .header-controls{display:flex;align-items:center;justify-content:center}.ava-sidebar .sidebar-header .header-controls .collapse-btn{display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.ava-sidebar .sidebar-header .header-controls .collapse-btn:active{transform:scale(.95)}.ava-sidebar .sidebar-content{flex:1;overflow-y:auto;overflow-x:hidden;padding:16px 12px}.ava-sidebar .sidebar-content::-webkit-scrollbar{width:4px}.ava-sidebar .sidebar-content::-webkit-scrollbar-track{background:transparent}.ava-sidebar .sidebar-content::-webkit-scrollbar-thumb{background-color:#d1d5db;border-radius:2px}.ava-sidebar .sidebar-content::-webkit-scrollbar-thumb:hover{background-color:#9ca3af}.ava-sidebar .sidebar-footer{padding:16px;border-top:1px solid #e5e7eb;background-color:#f9fafb;flex-shrink:0}.ava-sidebar.collapsed .sidebar-header{justify-content:center;padding:16px 8px}.ava-sidebar.collapsed .sidebar-content{padding:16px 8px;display:flex;flex-direction:column;align-items:center}.ava-sidebar.collapsed .sidebar-footer{padding:16px 8px;display:flex;justify-content:center}@media (max-width: 768px){.ava-sidebar{position:fixed;top:0;left:0;z-index:1000;height:100vh}.ava-sidebar.collapsed{transform:translate(-100%)}}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "component", type: ButtonComponent, selector: "ava-button", inputs: ["label", "variant", "size", "state", "visual", "pill", "disabled", "width", "height", "gradient", "background", "color", "dropdown", "iconName", "iconColor", "iconSize", "iconPosition"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: SidebarComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-sidebar', imports: [CommonModule, ButtonComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-sidebar\" [style.width]=\"sidebarWidth\" [class.collapsed]=\"collapsed\">\r\n    <!-- Header Section with Collapse Button -->\r\n    <div class=\"sidebar-header\">\r\n        <div class=\"header-content\" *ngIf=\"!collapsed\">\r\n            <ng-content select=\"[slot=header]\"></ng-content>\r\n        </div>\r\n        <div class=\"header-controls\" *ngIf=\"showCollapseButton\">\r\n            <ava-button\r\n                class=\"collapse-btn\"\r\n                (click)=\"toggleCollapse()\"\r\n                iconPosition=\"only\"\r\n                [iconName]=\"collapsed ? 'ArrowRight' : 'ArrowLeft'\">\r\n            </ava-button>\r\n        </div>\r\n    </div>\r\n    \r\n    <!-- Main Content Section -->\r\n    <div class=\"sidebar-content\">\r\n        <ng-content select=\"[slot=content]\"></ng-content>\r\n    </div>\r\n    \r\n    <!-- Footer Section -->\r\n    <div class=\"sidebar-footer\">\r\n        <ng-content select=\"[slot=footer]\"></ng-content>\r\n    </div>\r\n</div>", styles: [".ava-sidebar{display:flex;flex-direction:column;min-width:260px;background-color:#fff;border-right:1px solid #e5e7eb;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;box-shadow:0 1px 3px #0000001a;overflow:hidden}.ava-sidebar.collapsed{min-width:50px}.ava-sidebar .sidebar-header{display:flex;align-items:center;justify-content:space-between;padding:16px;border-bottom:1px solid #e5e7eb;background-color:#f9fafb;min-height:64px;flex-shrink:0}.ava-sidebar .sidebar-header .header-content{flex:1;margin-right:12px;overflow:hidden}.ava-sidebar .sidebar-header .header-controls{display:flex;align-items:center;justify-content:center}.ava-sidebar .sidebar-header .header-controls .collapse-btn{display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.ava-sidebar .sidebar-header .header-controls .collapse-btn:active{transform:scale(.95)}.ava-sidebar .sidebar-content{flex:1;overflow-y:auto;overflow-x:hidden;padding:16px 12px}.ava-sidebar .sidebar-content::-webkit-scrollbar{width:4px}.ava-sidebar .sidebar-content::-webkit-scrollbar-track{background:transparent}.ava-sidebar .sidebar-content::-webkit-scrollbar-thumb{background-color:#d1d5db;border-radius:2px}.ava-sidebar .sidebar-content::-webkit-scrollbar-thumb:hover{background-color:#9ca3af}.ava-sidebar .sidebar-footer{padding:16px;border-top:1px solid #e5e7eb;background-color:#f9fafb;flex-shrink:0}.ava-sidebar.collapsed .sidebar-header{justify-content:center;padding:16px 8px}.ava-sidebar.collapsed .sidebar-content{padding:16px 8px;display:flex;flex-direction:column;align-items:center}.ava-sidebar.collapsed .sidebar-footer{padding:16px 8px;display:flex;justify-content:center}@media (max-width: 768px){.ava-sidebar{position:fixed;top:0;left:0;z-index:1000;height:100vh}.ava-sidebar.collapsed{transform:translate(-100%)}}\n"] }]
        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { width: [{
                type: Input
            }], collapsedWidth: [{
                type: Input
            }], showCollapseButton: [{
                type: Input
            }], isCollapsed: [{
                type: Input
            }], collapseToggle: [{
                type: Output
            }] } });

class AvaTextareaComponent {
    cdr;
    label = '';
    placeholder = '';
    variant = 'default';
    size = 'md';
    disabled = false;
    readonly = false;
    error = '';
    helper = '';
    rows = 3;
    id = '';
    name = '';
    maxlength;
    minlength;
    required = false;
    fullWidth = false;
    style;
    resizable = true;
    textareaBlur = new EventEmitter();
    textareaFocus = new EventEmitter();
    textareaInput = new EventEmitter();
    textareaChange = new EventEmitter();
    iconStartClick = new EventEmitter();
    iconEndClick = new EventEmitter();
    value = '';
    isFocused = false;
    constructor(cdr) {
        this.cdr = cdr;
    }
    // ControlValueAccessor implementation
    writeValue(value) {
        this.value = value || '';
        this.cdr.markForCheck();
    }
    // These are set by Angular forms
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onChange = () => { };
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onTouched = () => { };
    registerOnChange(fn) { this.onChange = fn; }
    registerOnTouched(fn) { this.onTouched = fn; }
    setDisabledState(isDisabled) {
        this.disabled = isDisabled;
        this.cdr.markForCheck();
    }
    // Event handlers
    onInput(event) {
        const target = event.target;
        this.value = target.value;
        this.onChange(this.value);
        this.textareaInput.emit(event);
    }
    onFocus(event) {
        this.isFocused = true;
        this.textareaFocus.emit(event);
    }
    onBlur(event) {
        this.isFocused = false;
        this.onTouched();
        this.textareaBlur.emit(event);
    }
    onChange_(event) {
        this.textareaChange.emit(event);
    }
    // Icon click handlers
    onIconStartClick(event) {
        if (this.disabled || this.readonly)
            return;
        event.stopPropagation();
        this.iconStartClick.emit(event);
    }
    onIconEndClick(event) {
        if (this.disabled || this.readonly)
            return;
        event.stopPropagation();
        this.iconEndClick.emit(event);
    }
    onIconKeydown(event, position) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            if (position === 'start')
                this.onIconStartClick(event);
            else
                this.onIconEndClick(event);
        }
    }
    // Computed properties
    get hasError() {
        return !!this.error;
    }
    get hasHelper() {
        return !!this.helper;
    }
    get inputId() {
        return this.id || `ava-textarea-${Math.random().toString(36).substr(2, 9)}`;
    }
    get errorId() {
        return `${this.inputId}-error`;
    }
    get helperId() {
        return `${this.inputId}-helper`;
    }
    get ariaDescribedBy() {
        const ids = [];
        if (this.hasError)
            ids.push(this.errorId);
        if (this.hasHelper)
            ids.push(this.helperId);
        return ids.join(' ') || '';
    }
    get inputClasses() {
        const classes = ['ava-textarea__input'];
        if (this.size)
            classes.push(`ava-textarea__input--${this.size}`);
        if (this.variant)
            classes.push(`ava-textarea__input--${this.variant}`);
        if (this.hasError)
            classes.push('ava-textarea__input--error');
        if (this.disabled)
            classes.push('ava-textarea__input--disabled');
        if (this.readonly)
            classes.push('ava-textarea__input--readonly');
        if (this.isFocused)
            classes.push('ava-textarea__input--focused');
        if (this.fullWidth)
            classes.push('ava-textarea__input--full-width');
        return classes.join(' ');
    }
    get wrapperClasses() {
        const classes = ['ava-textarea'];
        if (this.size)
            classes.push(`ava-textarea--${this.size}`);
        if (this.variant)
            classes.push(`ava-textarea--${this.variant}`);
        if (this.hasError)
            classes.push('ava-textarea--error');
        if (this.disabled)
            classes.push('ava-textarea--disabled');
        if (this.readonly)
            classes.push('ava-textarea--readonly');
        if (this.isFocused)
            classes.push('ava-textarea--focused');
        if (this.fullWidth)
            classes.push('ava-textarea--full-width');
        return classes.join(' ');
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTextareaComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: AvaTextareaComponent, isStandalone: true, selector: "ava-textarea", inputs: { label: "label", placeholder: "placeholder", variant: "variant", size: "size", disabled: "disabled", readonly: "readonly", error: "error", helper: "helper", rows: "rows", id: "id", name: "name", maxlength: "maxlength", minlength: "minlength", required: "required", fullWidth: "fullWidth", style: "style", resizable: "resizable" }, outputs: { textareaBlur: "textareaBlur", textareaFocus: "textareaFocus", textareaInput: "textareaInput", textareaChange: "textareaChange", iconStartClick: "iconStartClick", iconEndClick: "iconEndClick" }, providers: [
            {
                provide: NG_VALUE_ACCESSOR,
                useExisting: forwardRef(() => AvaTextareaComponent),
                multi: true,
            },
        ], ngImport: i0, template: "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textarea__label\"\r\n    [class.ava-textarea__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textarea__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textarea__container\" [ngStyle]=\"style\">\r\n    <!-- Textarea Field -->\r\n    <textarea\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [rows]=\"rows\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      [style.resize]=\"resizable ? 'vertical' : 'none'\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    ></textarea>\r\n    <!-- Icons Bar (stacked below textarea) -->\r\n    <div class=\"ava-textarea__iconsbar\">\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--start\">\r\n        <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n      </div>\r\n      <div class=\"ava-textarea__iconsbar-spacer\"></div>\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--end\">\r\n        <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textarea__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textarea__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", styles: [".ava-textarea{display:flex;flex-direction:column;gap:var(--textbox-gap);width:100%}.ava-textarea--full-width{width:100%}.ava-textarea--sm{gap:var(--textbox-gap-sm)}.ava-textarea--lg{gap:var(--textbox-gap-lg)}.ava-textarea__label{display:block;font:var(--textbox-label-font);color:var(--textbox-label-color);font-weight:var(--textbox-label-weight)}.ava-textarea__label--required:after{content:\"\"}.ava-textarea__required{color:var(--textbox-required-color);margin-left:.25rem}.ava-textarea__container{padding:.5rem 1rem;position:relative;display:flex;align-items:flex-start;background:var(--textbox-background);border:var(--textbox-border-width) solid var(--textbox-border-color);border-radius:var(--textbox-border-radius);transition:border-color .2s,box-shadow .2s;flex-direction:column}.ava-textarea__container:hover{border-color:var(--textbox-border-hover-color)}.ava-textarea--focused .ava-textarea__container{border-color:var(--textbox-border-focus-color);box-shadow:var(--textbox-focus-shadow)}.ava-textarea--error .ava-textarea__container{border-color:var(--textbox-border-error-color)}.ava-textarea--disabled .ava-textarea__container{background:var(--textbox-background-disabled);border-color:var(--textbox-border-disabled-color);cursor:not-allowed}.ava-textarea--readonly .ava-textarea__container{background:var(--textbox-background-readonly);border-color:var(--textbox-border-readonly-color)}.ava-textarea__input{border:none;outline:none;background:transparent;font:var(--textbox-input-font);color:var(--textbox-input-color);padding:var(--textbox-input-padding);min-height:var(--textbox-textarea-min-height);line-height:1.5;resize:vertical;font-weight:var(--textbox-label-weight);width:100%;box-sizing:border-box}.ava-textarea__input::placeholder{color:var(--textbox-placeholder-color);opacity:1}.ava-textarea__input:disabled{color:var(--textbox-input-disabled-color);cursor:not-allowed}.ava-textarea__input:read-only{color:var(--textbox-input-readonly-color);cursor:default}.ava-textarea__input--sm{padding:var(--textbox-input-padding-sm);min-height:var(--textbox-textarea-min-height-sm, var(--textbox-input-min-height-sm));font-size:var(--textbox-input-font-size-sm)}.ava-textarea__input--lg{padding:var(--textbox-input-padding-lg);min-height:var(--textbox-textarea-min-height-lg, var(--textbox-input-min-height-lg));font-size:var(--textbox-input-font-size-lg)}.ava-textarea__input--full-width{width:100%}.ava-textarea__iconsbar{display:flex;flex-direction:row;align-items:center;width:100%;margin-top:.25rem;margin-bottom:0}.ava-textarea__iconsbar-spacer{flex:1 1 auto}.ava-textarea__icons{display:flex;align-items:center;gap:.5rem}.ava-textarea__icons--start{justify-content:flex-start}.ava-textarea__icons--end{justify-content:flex-end}.ava-textarea__icons ava-icon{cursor:pointer;transition:color .18s}.ava-textarea__icons ava-icon:active{opacity:.7}.ava-textarea__prefix,.ava-textarea__suffix{display:flex;align-items:center;padding:var(--textbox-affix-padding);color:var(--textbox-affix-color);font-size:var(--textbox-affix-font-size);background:var(--textbox-affix-background);border-radius:var(--textbox-affix-border-radius)}.ava-textarea--disabled .ava-textarea__prefix,.ava-textarea--disabled .ava-textarea__suffix{color:var(--textbox-affix-disabled-color);background:var(--textbox-affix-disabled-background)}.ava-textarea__prefix{border-top-left-radius:var(--textbox-border-radius);border-bottom-left-radius:var(--textbox-border-radius)}.ava-textarea__suffix{border-top-right-radius:var(--textbox-border-radius);border-bottom-right-radius:var(--textbox-border-radius)}.ava-textarea__error{display:flex;align-items:flex-start;gap:var(--textbox-error-gap);color:var(--textbox-error-color);font-size:var(--textbox-error-font-size);line-height:1.4}.ava-textarea__error-icon{flex-shrink:0;margin-top:.125rem}.ava-textarea__error-text{flex:1}.ava-textarea__helper{display:flex;align-items:flex-start;gap:var(--textbox-helper-gap);color:var(--textbox-helper-color);font-size:var(--textbox-helper-font-size);line-height:1.4}.ava-textarea__helper-icon{flex-shrink:0;margin-top:.125rem}.ava-textarea__helper-text{flex:1}.ava-textarea--primary .ava-textarea__container{border-color:var(--textbox-border-primary-color)}.ava-textarea--success .ava-textarea__container{border-color:var(--textbox-border-success-color)}.ava-textarea--warning .ava-textarea__container{border-color:var(--textbox-border-warning-color)}.ava-textarea--info .ava-textarea__container{border-color:var(--textbox-border-info-color)}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "directive", type: i1.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { kind: "directive", type: i1.NgStyle, selector: "[ngStyle]", inputs: ["ngStyle"] }, { kind: "component", type: IconComponent, selector: "ava-icon", inputs: ["iconName", "color", "disabled", "iconColor", "iconSize", "cursor"], outputs: ["userClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: AvaTextareaComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-textarea', standalone: true, imports: [CommonModule, IconComponent], changeDetection: ChangeDetectionStrategy.OnPush, providers: [
                        {
                            provide: NG_VALUE_ACCESSOR,
                            useExisting: forwardRef(() => AvaTextareaComponent),
                            multi: true,
                        },
                    ], schemas: [CUSTOM_ELEMENTS_SCHEMA], template: "<div [class]=\"wrapperClasses\">\r\n  <!-- Label -->\r\n  <label\r\n    *ngIf=\"label\"\r\n    [for]=\"inputId\"\r\n    class=\"ava-textarea__label\"\r\n    [class.ava-textarea__label--required]=\"required\"\r\n  >\r\n    {{ label }}\r\n    <span *ngIf=\"required\" class=\"ava-textarea__required\" aria-hidden=\"true\"\r\n      >*</span\r\n    >\r\n  </label>\r\n\r\n  <!-- Input Container -->\r\n  <div class=\"ava-textarea__container\" [ngStyle]=\"style\">\r\n    <!-- Textarea Field -->\r\n    <textarea\r\n      [id]=\"inputId\"\r\n      [name]=\"name\"\r\n      [placeholder]=\"placeholder\"\r\n      [value]=\"value\"\r\n      [disabled]=\"disabled\"\r\n      [readonly]=\"readonly\"\r\n      [required]=\"required\"\r\n      [attr.maxlength]=\"maxlength\"\r\n      [attr.minlength]=\"minlength\"\r\n      [rows]=\"rows\"\r\n      [class]=\"inputClasses\"\r\n      [attr.aria-invalid]=\"hasError\"\r\n      [attr.aria-describedby]=\"ariaDescribedBy || null\"\r\n      [style.resize]=\"resizable ? 'vertical' : 'none'\"\r\n      (input)=\"onInput($event)\"\r\n      (focus)=\"onFocus($event)\"\r\n      (blur)=\"onBlur($event)\"\r\n      (change)=\"onChange_($event)\"\r\n    ></textarea>\r\n    <!-- Icons Bar (stacked below textarea) -->\r\n    <div class=\"ava-textarea__iconsbar\">\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--start\">\r\n        <ng-content select=\"[slot=icon-start]\"></ng-content>\r\n      </div>\r\n      <div class=\"ava-textarea__iconsbar-spacer\"></div>\r\n      <div class=\"ava-textarea__icons ava-textarea__icons--end\">\r\n        <ng-content select=\"[slot=icon-end]\"></ng-content>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"hasError\"\r\n    [id]=\"errorId\"\r\n    class=\"ava-textarea__error\"\r\n    role=\"alert\"\r\n    aria-live=\"polite\"\r\n  >\r\n    <ava-icon\r\n      iconName=\"alert-circle\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__error-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n      [iconColor]=\"'red'\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__error-text\">{{ error }}</span>\r\n  </div>\r\n\r\n  <!-- Helper Message -->\r\n  <div *ngIf=\"hasHelper\" [id]=\"helperId\" class=\"ava-textarea__helper\">\r\n    <ava-icon\r\n      iconName=\"info\"\r\n      [iconSize]=\"14\"\r\n      class=\"ava-textarea__helper-icon\"\r\n      [cursor]=\"false\"\r\n      [disabled]=\"false\"\r\n    ></ava-icon>\r\n    <span class=\"ava-textarea__helper-text\">{{ helper }}</span>\r\n  </div>\r\n</div>\r\n", styles: [".ava-textarea{display:flex;flex-direction:column;gap:var(--textbox-gap);width:100%}.ava-textarea--full-width{width:100%}.ava-textarea--sm{gap:var(--textbox-gap-sm)}.ava-textarea--lg{gap:var(--textbox-gap-lg)}.ava-textarea__label{display:block;font:var(--textbox-label-font);color:var(--textbox-label-color);font-weight:var(--textbox-label-weight)}.ava-textarea__label--required:after{content:\"\"}.ava-textarea__required{color:var(--textbox-required-color);margin-left:.25rem}.ava-textarea__container{padding:.5rem 1rem;position:relative;display:flex;align-items:flex-start;background:var(--textbox-background);border:var(--textbox-border-width) solid var(--textbox-border-color);border-radius:var(--textbox-border-radius);transition:border-color .2s,box-shadow .2s;flex-direction:column}.ava-textarea__container:hover{border-color:var(--textbox-border-hover-color)}.ava-textarea--focused .ava-textarea__container{border-color:var(--textbox-border-focus-color);box-shadow:var(--textbox-focus-shadow)}.ava-textarea--error .ava-textarea__container{border-color:var(--textbox-border-error-color)}.ava-textarea--disabled .ava-textarea__container{background:var(--textbox-background-disabled);border-color:var(--textbox-border-disabled-color);cursor:not-allowed}.ava-textarea--readonly .ava-textarea__container{background:var(--textbox-background-readonly);border-color:var(--textbox-border-readonly-color)}.ava-textarea__input{border:none;outline:none;background:transparent;font:var(--textbox-input-font);color:var(--textbox-input-color);padding:var(--textbox-input-padding);min-height:var(--textbox-textarea-min-height);line-height:1.5;resize:vertical;font-weight:var(--textbox-label-weight);width:100%;box-sizing:border-box}.ava-textarea__input::placeholder{color:var(--textbox-placeholder-color);opacity:1}.ava-textarea__input:disabled{color:var(--textbox-input-disabled-color);cursor:not-allowed}.ava-textarea__input:read-only{color:var(--textbox-input-readonly-color);cursor:default}.ava-textarea__input--sm{padding:var(--textbox-input-padding-sm);min-height:var(--textbox-textarea-min-height-sm, var(--textbox-input-min-height-sm));font-size:var(--textbox-input-font-size-sm)}.ava-textarea__input--lg{padding:var(--textbox-input-padding-lg);min-height:var(--textbox-textarea-min-height-lg, var(--textbox-input-min-height-lg));font-size:var(--textbox-input-font-size-lg)}.ava-textarea__input--full-width{width:100%}.ava-textarea__iconsbar{display:flex;flex-direction:row;align-items:center;width:100%;margin-top:.25rem;margin-bottom:0}.ava-textarea__iconsbar-spacer{flex:1 1 auto}.ava-textarea__icons{display:flex;align-items:center;gap:.5rem}.ava-textarea__icons--start{justify-content:flex-start}.ava-textarea__icons--end{justify-content:flex-end}.ava-textarea__icons ava-icon{cursor:pointer;transition:color .18s}.ava-textarea__icons ava-icon:active{opacity:.7}.ava-textarea__prefix,.ava-textarea__suffix{display:flex;align-items:center;padding:var(--textbox-affix-padding);color:var(--textbox-affix-color);font-size:var(--textbox-affix-font-size);background:var(--textbox-affix-background);border-radius:var(--textbox-affix-border-radius)}.ava-textarea--disabled .ava-textarea__prefix,.ava-textarea--disabled .ava-textarea__suffix{color:var(--textbox-affix-disabled-color);background:var(--textbox-affix-disabled-background)}.ava-textarea__prefix{border-top-left-radius:var(--textbox-border-radius);border-bottom-left-radius:var(--textbox-border-radius)}.ava-textarea__suffix{border-top-right-radius:var(--textbox-border-radius);border-bottom-right-radius:var(--textbox-border-radius)}.ava-textarea__error{display:flex;align-items:flex-start;gap:var(--textbox-error-gap);color:var(--textbox-error-color);font-size:var(--textbox-error-font-size);line-height:1.4}.ava-textarea__error-icon{flex-shrink:0;margin-top:.125rem}.ava-textarea__error-text{flex:1}.ava-textarea__helper{display:flex;align-items:flex-start;gap:var(--textbox-helper-gap);color:var(--textbox-helper-color);font-size:var(--textbox-helper-font-size);line-height:1.4}.ava-textarea__helper-icon{flex-shrink:0;margin-top:.125rem}.ava-textarea__helper-text{flex:1}.ava-textarea--primary .ava-textarea__container{border-color:var(--textbox-border-primary-color)}.ava-textarea--success .ava-textarea__container{border-color:var(--textbox-border-success-color)}.ava-textarea--warning .ava-textarea__container{border-color:var(--textbox-border-warning-color)}.ava-textarea--info .ava-textarea__container{border-color:var(--textbox-border-info-color)}\n"] }]
        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { label: [{
                type: Input
            }], placeholder: [{
                type: Input
            }], variant: [{
                type: Input
            }], size: [{
                type: Input
            }], disabled: [{
                type: Input
            }], readonly: [{
                type: Input
            }], error: [{
                type: Input
            }], helper: [{
                type: Input
            }], rows: [{
                type: Input
            }], id: [{
                type: Input
            }], name: [{
                type: Input
            }], maxlength: [{
                type: Input
            }], minlength: [{
                type: Input
            }], required: [{
                type: Input
            }], fullWidth: [{
                type: Input
            }], style: [{
                type: Input
            }], resizable: [{
                type: Input
            }], textareaBlur: [{
                type: Output
            }], textareaFocus: [{
                type: Output
            }], textareaInput: [{
                type: Output
            }], textareaChange: [{
                type: Output
            }], iconStartClick: [{
                type: Output
            }], iconEndClick: [{
                type: Output
            }] } });

class ConfirmationPopupComponent {
    // Input: Confirm button label text (default: 'Yes')
    confirmationLabel = 'Yes';
    // Input: Title text of the popup
    title = 'title';
    // Input: Message/body text of the popup
    message = 'message';
    // Input: Controls visibility of the popup
    show = false;
    // Output: Emits when the popup is closed (via close button or confirm)
    closed = new EventEmitter();
    // Called when confirm button is clicked
    handleConfirm(text) {
        console.log(text);
        this.closed.emit(); // Emit closed event
    }
    // Called when cancel button is clicked
    handleCancel() {
        console.log('Cancel clicked');
        this.show = false; // Hide the popup
    }
    // Called when close icon is clicked
    handleClose() {
        this.show = false; // Hide the popup
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ConfirmationPopupComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "19.2.14", type: ConfirmationPopupComponent, isStandalone: true, selector: "ava-confirmation-popup", inputs: { confirmationLabel: "confirmationLabel", title: "title", message: "message", show: "show" }, outputs: { closed: "closed" }, ngImport: i0, template: "<div class=\"ava-confirmation-popup-container\">\r\n    <ava-popup [show]=\"show\" [popupWidth]=\"'500px'\" [title]=\"title\" [message]=\"message\" [showHeaderIcon]=\"false\"\r\n        [showClose]=\"true\" [showConfirm]=\"true\" [showCancel]=\"true\" [confirmButtonLabel]=\"confirmationLabel\"\r\n        (closed)=\"handleClose()\" (confirm)=\"handleConfirm(feedbackText.value)\" (cancel)=\"handleCancel()\" role=\"dialog\"\r\n        aria-modal=\"true\" aria-labelledby=\"popup-title\" aria-describedby=\"popup-message\">\r\n\r\n        <form class=\"popup-form\">\r\n            <ava-textarea label=\"Your Feedback\" placeholder=\"Type here...\" name=\"feedback\" #feedbackText>\r\n            </ava-textarea>\r\n        </form>\r\n\r\n    </ava-popup>\r\n</div>", styles: ["::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-title,::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-message{color:var(--popup-black-color);margin:var(--popup-heading-margin)}::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .ava-textarea__label{text-align:left;font-size:var(--popup-description-size);font-weight:var(--popup-heading-weight)}::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-form{width:100%;max-width:100%}\n"], dependencies: [{ kind: "ngmodule", type: CommonModule }, { kind: "component", type: PopupComponent, selector: "ava-popup", inputs: ["show", "messageAlignment", "title", "message", "showTitle", "showHeaderIcon", "headerIconName", "iconColor", "iconSize", "showClose", "closeIconName", "closeIconColor", "closeIconSize", "showInlineMessage", "inlineIconName", "inlineIconSize", "inlineIconColor", "inlineMessage", "popupWidth", "showConfirm", "showCancel", "cancelButtonLabel", "cancelButtonSize", "cancelButtonVariant", "cancelButtonBackground", "confirmButtonLabel", "confirmButtonSize", "confirmButtonVariant", "confirmButtonBackground"], outputs: ["confirm", "cancel", "closed"] }, { kind: "component", type: AvaTextareaComponent, selector: "ava-textarea", inputs: ["label", "placeholder", "variant", "size", "disabled", "readonly", "error", "helper", "rows", "id", "name", "maxlength", "minlength", "required", "fullWidth", "style", "resizable"], outputs: ["textareaBlur", "textareaFocus", "textareaInput", "textareaChange", "iconStartClick", "iconEndClick"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "19.2.14", ngImport: i0, type: ConfirmationPopupComponent, decorators: [{
            type: Component,
            args: [{ selector: 'ava-confirmation-popup', standalone: true, imports: [CommonModule, PopupComponent, AvaTextareaComponent], changeDetection: ChangeDetectionStrategy.OnPush, template: "<div class=\"ava-confirmation-popup-container\">\r\n    <ava-popup [show]=\"show\" [popupWidth]=\"'500px'\" [title]=\"title\" [message]=\"message\" [showHeaderIcon]=\"false\"\r\n        [showClose]=\"true\" [showConfirm]=\"true\" [showCancel]=\"true\" [confirmButtonLabel]=\"confirmationLabel\"\r\n        (closed)=\"handleClose()\" (confirm)=\"handleConfirm(feedbackText.value)\" (cancel)=\"handleCancel()\" role=\"dialog\"\r\n        aria-modal=\"true\" aria-labelledby=\"popup-title\" aria-describedby=\"popup-message\">\r\n\r\n        <form class=\"popup-form\">\r\n            <ava-textarea label=\"Your Feedback\" placeholder=\"Type here...\" name=\"feedback\" #feedbackText>\r\n            </ava-textarea>\r\n        </form>\r\n\r\n    </ava-popup>\r\n</div>", styles: ["::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-title,::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-message{color:var(--popup-black-color);margin:var(--popup-heading-margin)}::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .ava-textarea__label{text-align:left;font-size:var(--popup-description-size);font-weight:var(--popup-heading-weight)}::ng-deep .ava-confirmation-popup-container ava-popup .popup-backdrop .popup-container .popup-form{width:100%;max-width:100%}\n"] }]
        }], propDecorators: { confirmationLabel: [{
                type: Input
            }], title: [{
                type: Input
            }], message: [{
                type: Input
            }], show: [{
                type: Input
            }], closed: [{
                type: Output
            }] } });

/*
 * Public API Surface of play-comp-library
 */
// src/public-api.ts
/*

export * from './lib/components/slider/slider.component';

export * from './lib/components/input/input.component';
export * from './lib/components/link/link.component';

export * from './lib/components/heading/heading.component';
export * from './lib/components/radio-button/radio-button.component';
export * from './lib/components/labels/labels.component';
export * from './lib/components/caption/caption.component';
export * from './lib/components/dividers/dividers.component';
export * from './lib/components/tooltip/tooltip.component';
export * from './lib/components/spinner/spinner.component';
export * from './lib/components/datepicker/datepicker.component';
export * from './lib/components/dropdown/dropdown.component';
export * from './lib/components/pop-up/pop-up.component';

export * from './lib/components/search-fields/search-fields.component';
export * from './lib/components/progress/progress.component';
export * from './lib/components/toast-messages/toast-messages.component';
export * from './lib/components/inline/inline.component';
export * from './lib/components/time-picker/time-picker.component';
export * from './lib/components/breadcrumbs/breadcrumbs.component';
export * from './lib/components/header/header.component';
export * from './lib/components/snackbar/snackbar.component';
export * from './lib/components/footer/footer.component';
export * from './lib/components/input-groups/input-groups.component';
export * from './lib/components/table-header/table-header.component';
export * from './lib/components/table-content/table-content.component';
export * from './lib/components/fileupload/fileupload.component';
// export * from './lib/components/grid/grid.component';
export * from './lib/components/cards/cards.component';

export * from './lib/components/authentication/authentication.component';
export * from './lib/components/body-text/body-text.component';
export * from './lib/components/modal/modal.component';
export * from './lib/components/prompt-bar/prompt-bar.component';
export * from './lib/components/stepper/stepper.component';

export * from './lib/components/chat-window/chat-window.component';
export * from './lib/components/navbar/navbar.component';
export * from "./lib/components/experience-history-cards/experience-history-cards.component";
export * from './lib/components/split-screen/split-screen.component';
export * from './lib/components/split-screen/panel-components/left-panel.component';
export * from './lib/components/split-screen/panel-components/right-panel.component';
export * from './lib/components/icon-pill/icon-pill.component';
export * from './lib/components/file-attach-pill/file-attach-pill.component';
export * from './lib/components/charts/charts.component';
export * from './lib/components/tags/tags.component';
*/

/**
 * Generated bundle index. Do not edit.
 */

export { AccordionComponent, AdvancedCardComponent, ApprovalCardComponent, AvaTextboxComponent, AvatarsComponent, BadgesComponent, ButtonComponent, CardComponent, CheckboxComponent, ConfirmationPopupComponent, DropdownComponent, FeatureCardComponent, IconComponent, ImageCardComponent, LinkComponent, PaginationControlsComponent, PopupComponent, SidebarComponent, SpinnerComponent, TabsComponent, TextCardComponent, ToggleComponent };
//# sourceMappingURL=ava-play-comp-library.mjs.map
