// import { Component } from '@angular/core';
// import { TooltipComponent } from "../../../../../play-comp-library/src/lib/components/tooltip/tooltip.component";

// @Component({
//   selector: 'app-app-tooltip',
//   imports: [TooltipComponent],
//   templateUrl: './app-tooltip.component.html',
//   styleUrl: './app-tooltip.component.scss'
// })
// export class AppTooltipComponent {

// }

import { Component, signal, ViewEncapsulation, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipComponent } from "../../../../../play-comp-library/src/lib/components/tooltip/tooltip.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";

interface TooltipDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-tooltip',
  standalone: true,
  imports: [CommonModule, TooltipComponent, IconsComponent],
  templateUrl: './app-tooltip.component.html',
  styleUrls: ['./app-tooltip.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppTooltipComponent {

  // Documentation Sections
  sections: TooltipDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Basic usage of the tooltip component with a simple tooltip text.',
      showCode: false
    },
    {
      title: 'Tooltip Positions',
      description: 'How to set the position of the tooltip: top, bottom, left, or right.',
      showCode: false
    },
    {
      title: 'Tooltip Sizes',
      description: 'How to adjust the size of the tooltip: small, medium, or large.',
      showCode: false
    },
    {
      title: 'Tooltip with Animation',
      description: 'How to enable or disable animation effects when displaying the tooltip.',
      showCode: false
    },
    {
      title: 'Tooltip with Different Behaviors',
      description: 'Tooltip behavior, like showing on hover or focus.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    { name: 'text', type: 'string', default: '""', description: 'The text content of the tooltip.' },
    { name: 'position', type: "'top' | 'bottom' | 'left' | 'right'", default: "'top'", description: 'Defines the position of the tooltip relative to the element.' },
    { name: 'size', type: "'small' | 'medium' | 'large'", default: "'medium'", description: 'Sets the size of the tooltip.' },
    { name: 'enableAnimation', type: 'boolean', default: 'false', description: 'Enables or disables animation when the tooltip appears or disappears.' },
    { name: 'behavior', type: "'hover' | 'focus'", default: "'hover'", description: 'Defines how the tooltip behaves when shown (hover or focus).' }
  ];

  // Events
  events = [
    { name: 'onShow', type: 'EventEmitter<Event>', description: 'Emitted when the tooltip is shown.' },
    { name: 'onHide', type: 'EventEmitter<Event>', description: 'Emitted when the tooltip is hidden.' }
  ];

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  
  // Tooltip section expansion
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Example Code Generator
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': 
        `        <awe-tooltip [text]="'This is a tooltip '" [position]="'right'">
          This is a tooltip textPosition
        </awe-tooltip>`,
    
      'tooltip positions': 
        `        <awe-tooltip [text]="'This is a tooltip is top'" [position]="'top'">
          This is a tooltip textPosition is top
        </awe-tooltip>
        <awe-tooltip [text]="'This is a tooltip is bottom'" [position]="'bottom'">
          This is a tooltip textPosition is bottom
        </awe-tooltip>
        <awe-tooltip [text]="'This is a tooltip is right'" [position]="'right'">
          This is a tooltip textPosition is right
        </awe-tooltip>
        <awe-tooltip [text]="'This is a tooltip is left'" [position]="'left'">
          This is a tooltip textPosition is left
        </awe-tooltip>`,
    
      'tooltip sizes': 
        `        <awe-tooltip [text]="'This is a small tooltip'" [position]="'right'" [size]="'small'">
          This is a small tooltip
        </awe-tooltip>
        <awe-tooltip [text]="'This is a medium tooltip'" [position]="'right'" [size]="'medium'">
          This is a medium tooltip
        </awe-tooltip>
        <awe-tooltip [text]="'This is a large tooltip'" [position]="'right'" [size]="'large'">
          This is a large tooltip
        </awe-tooltip>`,
    
      'tooltip with animation': 
        `        <awe-tooltip [text]="'Tooltip with animation'" [position]="'bottom'" [enableAnimation]="true">
          This tooltip has animation
        </awe-tooltip>
        <awe-tooltip [text]="'Tooltip without animation'" [position]="'bottom'" [enableAnimation]="false">
          This tooltip has no animation
        </awe-tooltip>`,
    
      'tooltip with different behaviors': 
        `        <awe-tooltip [text]="'This is a tooltip on hover'" [position]="'top'" [behavior]="'hover'">
          This is a tooltip that shows on hover.
        </awe-tooltip>
        <awe-tooltip [text]="'This is a tooltip on focus'" [position]="'bottom'" [behavior]="'focus'">
          This is a tooltip that stays focused when clicked.
        </awe-tooltip>`
    };
    

    return examples[section] || '';
  }

  // Copy Code to Clipboard
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
