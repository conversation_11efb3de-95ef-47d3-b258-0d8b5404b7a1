.ava-text-card-container {

    .default {
        .ava-card-container .ava-card {
            padding: 30px;
        }

        .card {
            padding: 30px;
        }

        .card-header {
            align-items: left;
            display: flex;
            justify-content: left;

            .text-card-header {
                display: flex;
                align-items: left;
                gap: 8px;

                .icon-circle {
                    border-radius: 50%;
                }

                h3 {
                    font-size: 1.5rem;
                    margin: 0 0 4px 0;
                }
            }
        }

        .card-content {
            margin-bottom: 1rem;
            margin-top: 4rem;
            text-align: left;

            h1 {
                margin: 0;
                font-weight: 900;
                font-size: 68px;
            }
        }

        .card-footer {
            text-align: left;
        }
    }

    // Create Type Style
    .create-type {
        .ava-card {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 304px;
        }

        .card-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 16px;

            h3 {
                font-family: Mulish;
                font-weight: 700;
                font-size: 24px;
                line-height: 150%;
                color: #fff;
                margin: 0;
            }
        }

        .icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 64px;
            height: 64px;
            border-radius: 50%;
            padding: 17px;
            gap: 17px;
            background-color: #fff;
        }
    }

    // Prompt Type Style
    .prompt-type {
        .card-wrapper {
            display: flex;
            flex-direction: column;
            gap: 21px;
        }

        .card-content {
            margin: 0 !important;
            display: flex;
            align-items: flex-start;
        }

        .text-card-header {
            display: flex;
            flex-direction: column;
            width: 100%;
            text-align: left;
            gap: 16px;

            .top-icons {
                width: 100%;
                display: flex;
                justify-content: space-between;

                span {
                    font-family: 'PP Neue Machina', sans-serif;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 150%;
                    color: #19356C;
                    background: #F3F8FC;
                    opacity: 0.75;
                    border-radius: 4px;
                    padding: 4px 8px;
                    display: flex;
                    align-items: center;
                    gap: 5px;

                    &:first-of-type {
                        color: #000;

                        ava-icon {
                            transform: scaleX(-1);
                            display: inline-block;
                        }
                    }
                }
            }

            h3 {
                font-family: Mulish;
                font-weight: 700;
                font-size: 24px;
                line-height: 150%;
                color: #1F3868;
                margin: 0;
            }
        }

        ul {
            padding: 0;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            list-style: none;
        }

        .description {
            text-align: left;
            font-family: Mulish;
            font-weight: 400;
            font-size: 14px;
            line-height: 150%;
            margin: 0;
        }

        .footer {
            display: flex;
            justify-content: space-between;

            .name-date-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                font-weight: 400;
                font-size: 12px;
                line-height: 150%;
                text-align: left;

                p {
                    font-family: Mulish;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    margin: 0;

                    span {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        padding: 8px;
                        background: #FEFEFEBF;
                    }
                }
            }

            .action-icon-container {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                column-gap: 10px;
                margin-top: 25px;


                .play-icon-wrapper {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 40px;
                    height: 32px;
                    opacity: 1;
                    border-radius: 8px;
                    background: #124390;

                    .ava-icon-container svg {
                        fill: #fff;
                    }
                }
            }
        }
    }




}