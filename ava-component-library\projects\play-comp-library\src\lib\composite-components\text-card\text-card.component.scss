:host {
    display: block;
    min-width: 375px;
    min-height: 200px;
}

.ava-card {

    .card-header {
        display: flex;
        align-items: center;
        padding: 1rem;
        gap: 12px;

        .icon-circle {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }


    }

    .card-content {
        h2 {
            margin: 0;
            font-size: 40px;
            font-weight: bold;
        }
    }

    .card-footer {
        p {
            font-size: 14px;
            margin: 0;
        }
    }
}

.text-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 10px;

    h3 {
        margin: 0 0 4px 0;
        font-size: 1.1rem;
        font-weight: 600;
    }
}