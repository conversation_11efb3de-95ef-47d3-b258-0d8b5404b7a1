import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PopupComponent } from './popup.component';
import { By } from '@angular/platform-browser';

describe('ConfirmationPopupComponent', () => {
  let component: PopupComponent;
  let fixture: ComponentFixture<PopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PopupComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(PopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit confirm event and close', () => {
    spyOn(component.confirm, 'emit');
    spyOn(component.closed, 'emit');

    component.showConfirm = true;
    fixture.detectChanges();

    const confirmBtn = fixture.debugElement.query(By.css('ava-button[label="Confirm"]'));
    confirmBtn.triggerEventHandler('click', {});
    expect(component.confirm.emit).toHaveBeenCalled();
    expect(component.closed.emit).toHaveBeenCalled();
  });

  it('should emit cancel event and close', () => {
    spyOn(component.cancel, 'emit');
    spyOn(component.closed, 'emit');

    component.showCancel = true;
    fixture.detectChanges();

    const cancelBtn = fixture.debugElement.query(By.css('ava-button[label="Cancel"]'));
    cancelBtn.triggerEventHandler('click', {});
    expect(component.cancel.emit).toHaveBeenCalled();
    expect(component.closed.emit).toHaveBeenCalled();
  });

  it('should emit closed event when close icon clicked', () => {
    spyOn(component.closed, 'emit');
    const closeBtn = fixture.debugElement.query(By.css('.close-btn'));
    closeBtn.triggerEventHandler('click', {});
    expect(component.closed.emit).toHaveBeenCalled();
  });
});
