/* =======================
   MIXINS & HELPERS
   ======================= */

// Mixin for generating metaphor intensity modifiers
@mixin metaphor-surface-modifier($level) {
  .ava-textbox--surface-#{$level} .ava-textbox__container {
    --textbox-background: var(--textbox-surface-#{$level});
    backdrop-filter: var(--textbox-surface-blur-#{$level});
  }
}

@mixin metaphor-light-modifier($level) {
  .ava-textbox--light-#{$level} .ava-textbox__container {
    --textbox-focus-shadow: var(--textbox-light-#{$level});
  }
}

@mixin metaphor-motion-modifier($level) {
  .ava-textbox--motion-#{$level} .ava-textbox__container {
    transition: 
      box-shadow var(--textbox-motion-duration-#{$level}) var(--textbox-motion-#{$level}), 
      border-color var(--textbox-motion-duration-#{$level}) var(--textbox-motion-#{$level});
  }
}

@mixin metaphor-gradient-modifier($level) {
  .ava-textbox--gradient-#{$level} .ava-textbox__container {
    background-image: var(--textbox-gradient-#{$level});
  }
}

@mixin gradient-glass-overlay($level) {
  .ava-textbox--gradient-glass-#{$level} .ava-textbox__container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--textbox-gradient-glass-#{$level});
    border-radius: inherit;
    pointer-events: none;
    z-index: 1;
  }
}

// Mixin for state-based styles
@mixin textbox-state($state, $bg-var: null, $border-var: null) {
  .ava-textbox--#{$state} & {
    @if $bg-var {
      background: var(--textbox-background-#{$state});
    }
    @if $border-var {
      border-color: var(--textbox-border-#{$state}-color);
    }
    @if $state == disabled {
      cursor: not-allowed;
    }
  }
}

// Mixin for size variants
@mixin size-variant($size) {
  &--#{$size} {
    @if $size == sm {
      gap: var(--textbox-gap-sm);
    } @else if $size == lg {
      gap: var(--textbox-gap-lg);
    }
  }
}

// Mixin for input size variants
@mixin input-size-variant($size) {
  &--#{$size} {
    padding: var(--textbox-input-padding-#{$size});
    min-height: var(--textbox-input-min-height-#{$size});
    font-size: var(--textbox-input-font-size-#{$size});
  }
}

// Mixin for icon positioning
@mixin icon-position($position) {
  &--#{$position} {
    #{$position}: var(--textbox-icon-position-#{$position});
  }
}

/* =======================
   BASE COMPONENT STYLES
   ======================= */

.ava-textbox {
  display: flex;
  flex-direction: column;
  gap: var(--textbox-gap);
  width: 100%;

  &--full-width {
    width: 100%;
  }

  @include size-variant(sm);
  @include size-variant(lg);
}

.ava-textbox__label {
  display: block;
  font: var(--textbox-label-font, 500 1rem/1.5 'Inter', sans-serif);
  color: var(--textbox-label-color, var(--color-text-primary));

  &--required::after {
    content: '';
  }
}

.ava-textbox__required {
  color: var(--textbox-required-color, var(--color-text-error));
  margin-left: 0.25rem;
}

.ava-textbox__container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--textbox-background);
  border: var(--textbox-border-width) solid var(--textbox-border-color);
  border-radius: var(--textbox-border-radius);
  transition: 
    border-color var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30)),
    box-shadow var(--textbox-transition-duration, var(--motion-duration-30)) var(--textbox-transition-easing, var(--motion-30));

  &:focus-within {
    border-color: var(--textbox-focus-border-color);
    box-shadow: var(--textbox-focus-shadow);
  }

  &:hover:not(:focus-within) {
    border-color: var(--textbox-hover-border-color);
  }

  // State styles using mixin
  @include textbox-state(disabled, true, true);
  @include textbox-state(readonly, true, true);
  
  // Variant styles - simplified with loop-like approach
  @each $variant in primary, success, warning, info {
    .ava-textbox--#{$variant} & {
      border-color: var(--textbox-border-#{$variant}-color);
    }
  }
}

.ava-textbox__input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: var(--textbox-input-font-family, 'Inter', sans-serif);
  font-size: var(--textbox-input-font-size);
  color: var(--textbox-input-color);
  padding: var(--textbox-input-padding);
  min-height: var(--textbox-input-min-height);
  line-height: 1.5;
  resize: none;
  font-weight: var(--textbox-label-weight);
  height: 40px;
  box-sizing: border-box;

  &::placeholder {
    color: var(--textbox-placeholder-color);
    opacity: 1;
  }

  &:disabled {
    color: var(--textbox-input-disabled-color);
    cursor: not-allowed;
  }

  &:read-only {
    color: var(--textbox-input-readonly-color);
    cursor: default;
  }

  // Size variants using mixin
  @include input-size-variant(sm);
  @include input-size-variant(lg);

  // Icon spacing
  &--icon-start {
    padding-left: var(--textbox-input-icon-padding-start);
  }

  &--icon-end {
    padding-right: var(--textbox-input-icon-padding-end);
  }

  &--full-width {
    width: 100%;
  }
}

.ava-textbox__icon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--textbox-icon-color);
  z-index: 1;
  cursor: pointer;
  transition: color 0.2s;
  height: 100%;
  top: 0;

  &:hover,
  &:focus {
    color: var(--textbox-icon-focus-color);
    outline: none;
  }

  .ava-textbox--disabled &,
  .ava-textbox--readonly & {
    color: var(--textbox-icon-disabled-color);
    cursor: not-allowed;
  }

  // Icon positioning using mixin
  @include icon-position(start);
  @include icon-position(end);

  .ava-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0;
    margin: 0;
    vertical-align: middle;
  }
}

.ava-textbox__prefix,
.ava-textbox__suffix {
  display: flex;
  align-items: center;
  padding: var(--textbox-affix-padding);
  color: var(--textbox-affix-color);
  font-size: var(--textbox-affix-font-size);
  background: var(--textbox-affix-background);
  border-radius: var(--textbox-affix-border-radius);

  .ava-textbox--disabled & {
    color: var(--textbox-affix-disabled-color);
    background: var(--textbox-affix-disabled-background);
  }
}

.ava-textbox__prefix {
  border-top-left-radius: var(--textbox-border-radius);
  border-bottom-left-radius: var(--textbox-border-radius);
}

.ava-textbox__suffix {
  border-top-right-radius: var(--textbox-border-radius);
  border-bottom-right-radius: var(--textbox-border-radius);
}

// Helper and error text styles - using shared pattern
@each $type in error, helper {
  .ava-textbox__#{$type} {
    display: flex;
    align-items: flex-start;
    gap: var(--textbox-#{$type}-gap);
    color: var(--textbox-#{$type}-color);
    font-size: var(--textbox-#{$type}-font-size);
    line-height: 1.4;
  }

  .ava-textbox__#{$type}-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .ava-textbox__#{$type}-text {
    flex: 1;
  }
}

.ava-textbox__icons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  height: 100%;

  &--start {
    margin-right: 0.5rem;
  }

  &--end {
    margin-left: 0.5rem;
    gap: 0.5rem;
  }

  ava-icon {
    cursor: pointer;
    transition: color 0.18s;

    &:active {
      opacity: 0.7;
    }
  }
}

// Multiline-specific icon positioning
.ava-textbox__container.multiline {
  @each $position in start, end {
    .ava-textbox__icons--#{$position} {
      position: absolute;
      #{if($position == start, left, right)}: 0.75rem;
      bottom: 0.5rem;
      z-index: 2;
      background: transparent;
      align-items: flex-end;
    }
  }
}

/* =======================
   PLAY+ METAPHOR INTENSITY MODIFIERS
   ======================= */

// Generate all surface intensity modifiers (0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100)
@each $level in 0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100 {
  @include metaphor-surface-modifier($level);
  @include metaphor-light-modifier($level);
  @include metaphor-motion-modifier($level);
}

// Generate gradient modifiers (selective levels)
@each $level in 0, 20, 50, 80, 100 {
  @include metaphor-gradient-modifier($level);
}

// Generate gradient glass overlays
@each $level in 50, 100 {
  @include gradient-glass-overlay($level);
}

// Light ring modifiers (selective levels)
@each $level in 20, 50, 100 {
  .ava-textbox--light-ring-#{$level} .ava-textbox__container:focus-within,
  .ava-textbox--light-ring-#{$level}.ava-textbox--focused .ava-textbox__container {
    box-shadow: var(--textbox-light-ring-#{$level});
  }
}

// Ensure input is above gradient overlays
.ava-textbox__container .ava-textbox__input {
  position: relative;
  z-index: 2;
}

/* =======================
   LEGACY METAPHOR SUPPORT
   ======================= */

// Legacy metaphor classes that map to specific intensity levels
$legacy-metaphors: (
  glass: (surface: 50),
  light: (light: 50),
  liquid: (motion: 60),
  gradient: (gradient: 50)
);

@each $metaphor, $config in $legacy-metaphors {
  .ava-textbox--#{$metaphor} .ava-textbox__container {
    @if map-get($config, surface) {
      --textbox-background: var(--textbox-surface-#{map-get($config, surface)});
      backdrop-filter: var(--textbox-surface-blur-#{map-get($config, surface)});
    }
    @if map-get($config, light) {
      --textbox-focus-shadow: var(--textbox-light-#{map-get($config, light)});
    }
    @if map-get($config, motion) {
      $level: map-get($config, motion);
      transition: 
        box-shadow var(--textbox-motion-duration-#{$level}) var(--textbox-motion-#{$level}), 
        border-color var(--textbox-motion-duration-#{$level}) var(--textbox-motion-#{$level});
    }
    @if map-get($config, gradient) {
      background-image: var(--textbox-gradient-#{map-get($config, gradient)});
    }
  }
}
