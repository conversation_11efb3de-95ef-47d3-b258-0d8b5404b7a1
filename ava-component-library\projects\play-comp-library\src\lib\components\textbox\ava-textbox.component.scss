/* =======================
   AVA TEXTBOX - PLAY+ METAPHOR SYSTEM
   Uses semantic tokens from _textbox.css
   ======================= */

   .ava-textbox {
    display: flex;
    flex-direction: column;
    gap: var(--textbox-gap);
    width: 100%;
  
    &--full-width {
      width: 100%;
    }
  
    // Size variants
    &--sm {
      gap: var(--textbox-gap-sm);
    }
  
    &--lg {
      gap: var(--textbox-gap-lg);
    }
  }
  
  .ava-textbox__label {
    display: block;
    font: var(--textbox-label-font);
    color: var(--textbox-label-color);
    margin-bottom: var(--textbox-label-margin);
    font-weight: var(--textbox-label-weight);
  
    &--required::after {
      content: '';
    }
  }
  
  .ava-textbox__required {
    color: var(--textbox-required-color);
    margin-left: 0.25rem;
  }
  
  .ava-textbox__container {
    position: relative;
    display: flex;
    align-items: center;
    
    // Play+ Glass Metaphor - Using semantic tokens
    background: var(--textbox-glass-background);
    backdrop-filter: var(--textbox-glass-backdrop-filter);
    -webkit-backdrop-filter: var(--textbox-glass-backdrop-filter);
    border: var(--textbox-glass-border);
    border-radius: var(--textbox-border-radius);
    
    // Play+ Light Metaphor - Using semantic tokens
    box-shadow: var(--textbox-light-shadow);
    
    // Play+ Liquid Metaphor - Using semantic tokens
    transition: var(--textbox-liquid-transition-border), var(--textbox-liquid-transition-shadow), var(--textbox-liquid-transition-background);
  
    // Interactive states using semantic tokens
    &:hover:not(:focus-within) {
      background: var(--textbox-glass-background-hover);
      backdrop-filter: var(--textbox-glass-backdrop-filter-hover);
      -webkit-backdrop-filter: var(--textbox-glass-backdrop-filter-hover);
      border: var(--textbox-glass-border-hover);
      box-shadow: var(--textbox-light-shadow-hover);
      transform: var(--textbox-liquid-transform-hover);
    }
  
    &:focus-within {
      background: var(--textbox-glass-background-focus);
      backdrop-filter: var(--textbox-glass-backdrop-filter-focus);
      -webkit-backdrop-filter: var(--textbox-glass-backdrop-filter-focus);
      border: var(--textbox-glass-border-focus);
      box-shadow: var(--textbox-light-shadow-focus), var(--textbox-light-ring-focus);
      transform: var(--textbox-liquid-transform-focus);
    }
  
    // State styles
    .ava-textbox--disabled & {
      background: var(--textbox-background-disabled);
      border-color: var(--textbox-border-disabled-color);
      cursor: not-allowed;
      opacity: 0.6;
    }
  
    .ava-textbox--readonly & {
      background: var(--textbox-background-readonly);
      border-color: var(--textbox-border-readonly-color);
    }
  
    // Error state
    .ava-textbox--error & {
      border-color: var(--textbox-border-error-color);
      box-shadow: var(--textbox-light-glow-error);
    }
  
    // Success state (when no error and has value)
    .ava-textbox--success & {
      border-color: var(--textbox-border-success-color);
      box-shadow: var(--textbox-light-glow-success);
    }
  
    // Variant styles
    .ava-textbox--primary & {
      border-color: var(--textbox-border-primary-color);
    }
  
    .ava-textbox--warning & {
      border-color: var(--textbox-border-warning-color);
    }
  
    .ava-textbox--info & {
      border-color: var(--textbox-border-info-color);
    }
  
  
  }
  
  .ava-textbox__input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font: var(--textbox-input-font);
    color: var(--textbox-input-color);
    padding: var(--textbox-input-padding);
    min-height: var(--textbox-input-min-height);
    line-height: 1.5;
    resize: none;
    font-weight: var(--textbox-label-weight);
    z-index: 2; // Above any overlay effects
  
    &::placeholder {
      color: var(--textbox-placeholder-color);
      opacity: 1;
    }
  
    &:disabled {
      color: var(--textbox-input-disabled-color);
      cursor: not-allowed;
    }
  
    &:read-only {
      color: var(--textbox-input-readonly-color);
      cursor: default;
    }
  
    // Size variants
    &--sm {
      padding: var(--textbox-input-padding-sm);
      min-height: var(--textbox-input-min-height-sm);
      font-size: var(--textbox-input-font-size-sm);
    }
  
    &--lg {
      padding: var(--textbox-input-padding-lg);
      min-height: var(--textbox-input-min-height-lg);
      font-size: var(--textbox-input-font-size-lg);
    }
  
    // Icon spacing classes are now applied dynamically based on projected content
  
    // Legacy icon spacing classes (kept for backward compatibility)
    &--icon-start {
      padding-left: var(--textbox-input-icon-padding-start);
    }
  
    &--icon-end {
      padding-right: var(--textbox-input-icon-padding-end);
    }
  
    // Additional spacing when both icons and prefix/suffix are present
    &--icon-start.ava-textbox__input--with-prefix {
      padding-left: calc(var(--textbox-input-icon-padding-start) + 0.5rem);
    }
  
    &--icon-end.ava-textbox__input--with-suffix {
      padding-right: calc(var(--textbox-input-icon-padding-end) + 0.5rem);
    }
  
    &--full-width {
      width: 100%;
    }
  }
  
  .ava-textbox__icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: 100%;
    z-index: 3; // Above input and effects
  
    // Hide empty icon containers
    &:empty {
      display: none;
    }
  
    &--start {
      position: absolute;
      left: var(--textbox-icon-position-start);
      color: var(--textbox-icon-color);
      margin-right: 0.25rem; // Minimal spacing between icon and text
    }
  
    &--end {
      position: absolute;
      right: var(--textbox-icon-position-end);
      color: var(--textbox-icon-color);
      margin-left: 0.25rem; // Minimal spacing between icon and text
    }
  
    ava-icon {
      cursor: pointer;
      transition: var(--textbox-liquid-transition);
  
      &:hover,
      &:focus {
        color: var(--textbox-icon-focus-color);
        transform: var(--textbox-liquid-transform-hover);
      }
  
      &:active {
        opacity: 0.7;
        transform: scale(0.95);
      }
    }
  
    .ava-textbox--disabled &,
    .ava-textbox--readonly & {
      color: var(--textbox-icon-disabled-color);
      cursor: not-allowed;
    }
  
    .ava-textbox--focused & {
      color: var(--textbox-icon-focus-color);
    }
  }
  
  .ava-textbox__prefix,
  .ava-textbox__suffix {
    display: flex;
    align-items: center;
    padding: var(--textbox-affix-padding);
    color: var(--textbox-affix-color);
    font-size: var(--textbox-affix-font-size);
    background: var(--textbox-affix-background);
    border-radius: var(--textbox-affix-border-radius);
    z-index: 2;
    white-space: nowrap; // Prevent wrapping of prefix/suffix text
  
    // Hide empty prefix/suffix containers
    &:empty {
      display: none;
      padding: 0;
      margin: 0;
    }
  
    .ava-textbox--disabled & {
      color: var(--textbox-affix-disabled-color);
      background: var(--textbox-affix-disabled-background);
    }
  }
  
  .ava-textbox__prefix {
    border-top-left-radius: var(--textbox-border-radius);
    border-bottom-left-radius: var(--textbox-border-radius);
    margin-right: 0.25rem; // Minimal spacing between prefix and input text
  
    &:empty {
      margin-right: 0;
    }
  }
  
  .ava-textbox__suffix {
    border-top-right-radius: var(--textbox-border-radius);
    border-bottom-right-radius: var(--textbox-border-radius);
    margin-left: 0.25rem; // Minimal spacing between suffix and input text
  
    &:empty {
      margin-left: 0;
    }
  }
  
  .ava-textbox__error {
    display: flex;
    align-items: flex-start;
    gap: var(--textbox-error-gap);
    color: var(--textbox-error-color);
    font-size: var(--textbox-error-font-size);
    line-height: 1.4;
  }
  
  .ava-textbox__error-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
  }
  
  .ava-textbox__error-text {
    flex: 1;
  }
  
  .ava-textbox__helper {
    display: flex;
    align-items: flex-start;
    gap: var(--textbox-helper-gap);
    color: var(--textbox-helper-color);
    font-size: var(--textbox-helper-font-size);
    line-height: 1.4;
  }
  
  .ava-textbox__helper-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
  }
  
  .ava-textbox__helper-text {
    flex: 1;
  }
  
  /* =======================
     PLAY+ METAPHOR INTENSITY OVERRIDES
     Direct intensity control using semantic tokens
     ======================= */
  
  // Glass intensity overrides
  .ava-textbox--glass-0 .ava-textbox__container {
    background: var(--textbox-surface-0);
    backdrop-filter: var(--textbox-surface-blur-0);
    -webkit-backdrop-filter: var(--textbox-surface-blur-0);
  }
  
  .ava-textbox--glass-10 .ava-textbox__container {
    background: var(--textbox-surface-10);
    backdrop-filter: var(--textbox-surface-blur-10);
    -webkit-backdrop-filter: var(--textbox-surface-blur-10);
  }
  
  .ava-textbox--glass-25 .ava-textbox__container {
    background: var(--textbox-surface-25);
    backdrop-filter: var(--textbox-surface-blur-25);
    -webkit-backdrop-filter: var(--textbox-surface-blur-25);
  }
  
  .ava-textbox--glass-50 .ava-textbox__container {
    background: var(--textbox-surface-50);
    backdrop-filter: var(--textbox-surface-blur-50);
    -webkit-backdrop-filter: var(--textbox-surface-blur-50);
  }
  
  .ava-textbox--glass-75 .ava-textbox__container {
    background: var(--textbox-surface-75);
    backdrop-filter: var(--textbox-surface-blur-75);
    -webkit-backdrop-filter: var(--textbox-surface-blur-75);
  }
  
  .ava-textbox--glass-100 .ava-textbox__container {
    background: var(--textbox-surface-100);
    backdrop-filter: var(--textbox-surface-blur-100);
    -webkit-backdrop-filter: var(--textbox-surface-blur-100);
  }
  
  // Light intensity overrides
  .ava-textbox--light-0 .ava-textbox__container {
    box-shadow: var(--textbox-light-0);
    &:focus-within {
      box-shadow: var(--textbox-light-0), var(--textbox-light-ring-0);
    }
  }
  
  .ava-textbox--light-10 .ava-textbox__container {
    box-shadow: var(--textbox-light-10);
    &:focus-within {
      box-shadow: var(--textbox-light-10), var(--textbox-light-ring-10);
    }
  }
  
  .ava-textbox--light-25 .ava-textbox__container {
    box-shadow: var(--textbox-light-25);
    &:focus-within {
      box-shadow: var(--textbox-light-25), var(--textbox-light-ring-25);
    }
  }
  
  .ava-textbox--light-50 .ava-textbox__container {
    box-shadow: var(--textbox-light-50);
    &:focus-within {
      box-shadow: var(--textbox-light-50), var(--textbox-light-ring-50);
    }
  }
  
  .ava-textbox--light-75 .ava-textbox__container {
    box-shadow: var(--textbox-light-75);
    &:focus-within {
      box-shadow: var(--textbox-light-75), var(--textbox-light-ring-75);
    }
  }
  
  .ava-textbox--light-100 .ava-textbox__container {
    box-shadow: var(--textbox-light-100);
    &:focus-within {
      box-shadow: var(--textbox-light-100), var(--textbox-light-ring-100);
    }
  }
  
  // Liquid motion intensity overrides
  .ava-textbox--liquid-0 .ava-textbox__container {
    transition: var(--textbox-motion-duration-0) var(--textbox-motion-0);
  }
  
  .ava-textbox--liquid-10 .ava-textbox__container {
    transition: var(--textbox-motion-duration-10) var(--textbox-motion-10);
  }
  
  .ava-textbox--liquid-25 .ava-textbox__container {
    transition: var(--textbox-motion-duration-25) var(--textbox-motion-25);
  }
  
  .ava-textbox--liquid-50 .ava-textbox__container {
    transition: var(--textbox-motion-duration-50) var(--textbox-motion-50);
  }
  
  .ava-textbox--liquid-75 .ava-textbox__container {
    transition: var(--textbox-motion-duration-75) var(--textbox-motion-75);
  }
  
  .ava-textbox--liquid-100 .ava-textbox__container {
    transition: var(--textbox-motion-duration-100) var(--textbox-motion-100);
  }
  
  // Gradient intensity overrides
  .ava-textbox--gradient-0 .ava-textbox__container {
    background-image: var(--textbox-gradient-0);
  }
  
  .ava-textbox--gradient-25 .ava-textbox__container {
    background-image: var(--textbox-gradient-25);
  }
  
  .ava-textbox--gradient-50 .ava-textbox__container {
    background-image: var(--textbox-gradient-50);
  }
  
  .ava-textbox--gradient-75 .ava-textbox__container {
    background-image: var(--textbox-gradient-75);
  }
  
  .ava-textbox--gradient-100 .ava-textbox__container {
    background-image: var(--textbox-gradient-100);
  }
  
  /* =======================
     LEGACY METAPHOR SUPPORT
     For backward compatibility
     ======================= */
  
  .ava-textbox--glass .ava-textbox__container {
    background: var(--textbox-surface-50);
    backdrop-filter: var(--textbox-surface-blur-50);
    -webkit-backdrop-filter: var(--textbox-surface-blur-50);
  }
  
  .ava-textbox--light .ava-textbox__container {
    box-shadow: var(--textbox-light-50);
    &:focus-within {
      box-shadow: var(--textbox-light-50), var(--textbox-light-ring-50);
    }
  }
  
  .ava-textbox--liquid .ava-textbox__container {
    transition: var(--textbox-motion-duration-50) var(--textbox-motion-50);
  }
  
  .ava-textbox--gradient .ava-textbox__container {
    background-image: var(--textbox-gradient-50);
  }
  