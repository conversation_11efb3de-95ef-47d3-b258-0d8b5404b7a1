import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { FooterComponent } from "../../../../../play-comp-library/src/lib/components/footer/footer.component";
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-app-footer',
  imports: [FooterComponent, CommonModule,IconsComponent],
  templateUrl: './app-footer.component.html',
  styleUrl: './app-footer.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class AppFooterComponent {
  sections = [
    {
      title: 'Side Position',
      description: 'Demonstrates the footer in the side position with custom content.',
      showCode: false
    },
    {
      title: 'Top Position',
      description: 'Shows the footer in the top position with columns, links, and custom content.',
      showCode: false
    },
    {
      title: 'Only Position',
      description: 'Illustrates the footer in the "only" position with custom content.',
      showCode: false
    },
    {
      title: 'Animations',
      description: 'Demonstrates animations in the footer component.',
      showCode: false
    }
  ];

  apiProps = [
    { name: 'position', type: "'side' | 'top' | 'only'", default: "'side'", description: 'The position of the footer.' },
    { name: 'footerText', type: 'string[]', default: '[]', description: 'The text items to display in the footer.' },
    { name: 'footerColumns', type: '{ subtitle: string; texts: string[]; routes?: string[] }[]', default: '[]', description: 'The columns to display in the footer, each with a subtitle and text items.' },
    { name: 'fontColor', type: "'light' | 'dark'", default: "'light'", description: 'The font color theme of the footer.' },
    { name: 'bgColor', type: "'light' | 'dark' | 'neutral'", default: "'neutral'", description: 'The background color theme of the footer.' },
    { name: 'logoUrl', type: 'string', default: 'undefined', description: 'The URL of the logo to display in the footer.' },
    { name: 'middleLinks', type: 'FooterLink[]', default: '[]', description: 'The links to display in the middle section of the footer.' },
    { name: 'animation', type: "'fade' | 'slide'", default: 'undefined', description: 'The animation to apply to the footer.' }
  ];

  events = [
    { name: 'handleTextClick', type: 'void', description: 'Emitted when a text item in the footer is clicked.' },
    { name: 'handleLinkClick', type: 'void', description: 'Emitted when a link in the footer is clicked.' }
  ];

  @ViewChild('codeBlock') codeBlock!: ElementRef;

  currentYear: number = new Date().getFullYear();

  footerColumns = [
    {
      subtitle: 'What we do',
      texts: ['Applied AI', 'Data', 'Experience', 'Platform Engineering', 'Product Engineering', 'Quality Engineering', 'Innovate With Saleforce'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route','/route']
    },
    {
      subtitle: 'Industries',
      texts: ['Banking and Financial Service', 'Communication, Media, Entertainment', 'Healthcare', 'High-Tech', 'Retail and Consumer Goods', 'Travel and Hospitality'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route']
    },
    {
      subtitle: 'Quick Links',
      texts: ['Home', 'Who we are', 'Insight and impact', 'Careers India', 'Careers North America', 'Careers Europe', 'Careers Asia Pacific', 'Global Delivery Hubs', 'Contact Us'],
      routes: ['/route', '/route', 'https://ascendion.com']
    }
  ];

  middleLinks = [
    { text: 'Terms of use', url: '/privacy' },
    { text: 'Privacy Policy', url: '/privacy' },
    { text: 'Accessibility Disclosure', url: '/terms' },
    { text: 'AO-enhances content notice', url: 'https://ascendion.com' }
  ];

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getExampleCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'side position': `
import { Component } from '@angular/core';
import { FooterComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-side-footer',
  standalone: true,
  imports: [FooterComponent],
  template: \`
    <awe-footer
      [position]="'side'"
      [footerText]="['Footer Text 1', 'Footer Text 2']">
      <div class="side-content">
        <h3>This is custom content for the side position of the footer.</h3>
        <button (click)="customAction()">Custom Side Action</button>
      </div>
    </awe-footer>
  \`
})
export class SideFooterComponent {
  customAction() {
    alert('Custom button clicked!');
  }
}`,
      'top position': `
import { Component } from '@angular/core';
import { FooterComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-top-footer',
  standalone: true,
  imports: [FooterComponent],
  template: \`
    <awe-footer
      [position]="'top'"
      [footerColumns]="footerColumns"
      [logoUrl]="'assets/ascendion_logo.svg'"
      [middleLinks]="middleLinks">
      <div class="top-content">
        <h3>This is custom content for the top position of the footer.</h3>
        <button (click)="customAction()">Custom Top Action</button>
      </div>
      <div class="middle-content">
        <h3>This is custom content for the middle position of the footer.</h3>
        <button (click)="customAction()">Custom Middle Action</button>
      </div>
      <div class="bottom-content">
        <div class="footer-bottom">
          <div class="footer-ascendion">
            <span>&copy; {{ currentYear }} Ascendion. All Rights Reserved.</span>
          </div>
        </div>
      </div>
    </awe-footer>
  \`
})
export class TopFooterComponent {
  currentYear: number = new Date().getFullYear();

  footerColumns = [
    {
      subtitle: 'What we do',
      texts: ['Applied AI', 'Data', 'Experience', 'Platform Engineering', 'Product Engineering', 'Quality Engineering', 'Innovate With Saleforce'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route','/route']
    },
    {
      subtitle: 'Industries',
      texts: ['Banking and Financial Service', 'Communication, Media, Entertainment', 'Healthcare', 'High-Tech', 'Retail and Consumer Goods', 'Travel and Hospitality'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route']
    },
    {
      subtitle: 'Quick Links',
      texts: ['Home', 'Who we are', 'Insight and impact', 'Careers India', 'Careers North America', 'Careers Europe', 'Careers Asia Pacific', 'Global Delivery Hubs', 'Contact Us'],
      routes: ['/route', '/route', 'https://ascendion.com']
    }
  ];

  middleLinks = [
    { text: 'Terms of use', url: '/privacy' },
    { text: 'Privacy Policy', url: '/privacy' },
    { text: 'Accessibility Disclosure', url: '/terms' },
    { text: 'AO-enhances content notice', url: 'https://ascendion.com' }
  ];

  customAction() {
    alert('Custom button clicked!');
  }
}`,
      'only position': `
import { Component } from '@angular/core';
import { FooterComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-only-footer',
  standalone: true,
  imports: [FooterComponent],
  template: \`
    <awe-footer
      [position]="'only'"
      fontColor="dark"
      bgColor="light">
      <div class="footer-only-content">
        <img src="assets/ascendion_logo.svg" alt="Custom Image">
        <h3>This is custom content for the "only" position of the footer.</h3>
        <button (click)="customAction()">Custom Action</button>
      </div>
    </awe-footer>
  \`
})
export class OnlyFooterComponent {
  customAction() {
    alert('Custom button clicked!');
  }
}`,
      'animations': `
import { Component } from '@angular/core';
import { FooterComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-animated-footer',
  standalone: true,
  imports: [FooterComponent],
  template: \`
    <h6>Slide</h6>
    <awe-footer
      position="top"
      [footerColumns]="footerColumns"
      [middleLinks]="middleLinks"
      [logoUrl]="'assets/ascendion_logo.svg'"
      fontColor="light"
      bgColor="dark"
      [animation]="'slide'">
    </awe-footer>
    <h6>Fade</h6>
    <awe-footer
      position="top"
      [footerColumns]="footerColumns"
      [middleLinks]="middleLinks"
      [logoUrl]="'assets/ascendion_logo.svg'"
      fontColor="light"
      bgColor="dark"
      [animation]="'fade'">
    </awe-footer>
  \`
})
export class AnimatedFooterComponent {
  currentYear: number = new Date().getFullYear();

  footerColumns = [
    {
      subtitle: 'What we do',
      texts: ['Applied AI', 'Data', 'Experience', 'Platform Engineering', 'Product Engineering', 'Quality Engineering', 'Innovate With Saleforce'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route','/route']
    },
    {
      subtitle: 'Industries',
      texts: ['Banking and Financial Service', 'Communication, Media, Entertainment', 'Healthcare', 'High-Tech', 'Retail and Consumer Goods', 'Travel and Hospitality'],
      routes: ['/route', '/route', '/route','/route', '/route', '/route']
    },
    {
      subtitle: 'Quick Links',
      texts: ['Home', 'Who we are', 'Insight and impact', 'Careers India', 'Careers North America', 'Careers Europe', 'Careers Asia Pacific', 'Global Delivery Hubs', 'Contact Us'],
      routes: ['/route', '/route', 'https://ascendion.com']
    }
  ];

  middleLinks = [
    { text: 'Terms of use', url: '/privacy' },
    { text: 'Privacy Policy', url: '/privacy' },
    { text: 'Accessibility Disclosure', url: '/terms' },
    { text: 'AO-enhances content notice', url: 'https://ascendion.com' }
  ];
}
`
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (!this.codeBlock?.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  customAction() {
    alert('Custom button clicked!');
  }

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
