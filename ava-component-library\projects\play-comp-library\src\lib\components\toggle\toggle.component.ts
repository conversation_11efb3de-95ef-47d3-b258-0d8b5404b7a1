// toggle.component.ts
import { Component, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export type ToggleSize = 'small' | 'medium' | 'large';
export type TogglePosition = 'left' | 'right';

@Component({
  selector: 'ava-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toggle.component.html',
  styleUrls: ['./toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToggleComponent {
  @Input() size: ToggleSize = 'medium';
  @Input() title: string = '';
  @Input() position: TogglePosition = 'left';
  @Input() disabled: boolean = false;
  @Input() checked: boolean = false;
  @Input() animation: boolean = true;
  
  @Output() checkedChange = new EventEmitter<boolean>();

  onToggle(): void {
    if (this.disabled) return;
    
    this.checked = !this.checked;
    this.checkedChange.emit(this.checked);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault();
      this.onToggle();
    }
  }

  get titleName(): string | null {
    return this.title 
      ? `toggle-title-${this.title.replace(/\s+/g, '-').toLowerCase()}` 
      : null;
  }
}