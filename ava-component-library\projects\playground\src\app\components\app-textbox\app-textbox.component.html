<div class="textbox-demo">
  <div class="container">
    <header class="demo-header">
      <h1>Textbox Component</h1>
      <p class="subtitle">
        A versatile input component with multiple variants, sizes, and states
      </p>
    </header>

    <!-- Basic Examples -->
    <section class="demo-section">
      <h2>Basic Examples</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <ava-textbox
            label="Basic Input"
            placeholder="Enter text here"
            [(ngModel)]="basicValue"
          >
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            [(ngModel)]="emailValue"
          >
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Disabled"
            placeholder="Cannot edit"
            value="Disabled input"
            [disabled]="true"
          >
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Read Only"
            placeholder="Cannot edit"
            value="Read only value"
            [readonly]="true"
          >
          </ava-textbox>
        </div>
      </div>
    </section>

    <!-- Variants -->
    <section class="demo-section">
      <h2>Variants</h2>
      <div class="demo-grid">
        <div class="demo-item" *ngFor="let variant of variants">
          <ava-textbox
            [label]="variant | titlecase"
            [placeholder]="'Enter ' + variant + ' text'"
            [variant]="variant"
            [helper]="
              variant === 'success'
                ? 'This looks good!'
                : variant === 'warning'
                ? 'Please check this'
                : ''
            "
            [error]="variant === 'error' ? 'This field has an error' : ''"
          >
          </ava-textbox>
        </div>
      </div>
    </section>

    <!-- Sizes -->
    <section class="demo-section">
      <h2>Sizes</h2>
      <div class="demo-grid">
        <div class="demo-item" *ngFor="let size of sizes">
          <ava-textbox
            [label]="size.toUpperCase() + ' Size'"
            [placeholder]="'Enter text (' + size + ')'"
            [size]="size"
          >
          </ava-textbox>
        </div>
      </div>
    </section>

    <!-- With Icons -->
    <section class="demo-section">
      <h2>With Icons</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <ava-textbox
            label="Search"
            placeholder="Search..."
            [(ngModel)]="searchValue"
          >
            <ava-icon
              slot="icon-start"
              iconName="search"
              [iconSize]="16"
              iconColor="var(--color-brand-primary)"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Clear Search"
            placeholder="Type to search..."
            [(ngModel)]="searchValue"
          >
            <ava-icon
              slot="icon-end"
              iconName="x"
              [iconSize]="16"
              iconColor="var(--color-brand-primary)"
              [cursor]="true"
              (userClick)="onClearClick()"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Email with Icon"
            type="email"
            placeholder="Enter your email"
            [(ngModel)]="emailValue"
          >
            <ava-icon
              slot="icon-start"
              iconName="mail"
              [iconSize]="16"
              iconColor="var(--color-brand-primary)"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox
            label="Password"
            [type]="showPassword ? 'text' : 'password'"
            placeholder="Enter password"
            [(ngModel)]="passwordValue"
          >
            <ava-icon
              slot="icon-end"
              [iconName]="showPassword ? 'eye-off' : 'eye'"
              [iconSize]="16"
              iconColor="var(--color-brand-primary)"
              [cursor]="true"
              (userClick)="togglePasswordVisibility()"
            >
            </ava-icon>
          </ava-textbox>
        </div>
      </div>
    </section>

    <!-- Prefix/Suffix -->
    <section class="demo-section">
      <h2>Prefix & Suffix</h2>
      <div class="demo-grid">
        <div class="demo-item">
          <ava-textbox label="Website URL" placeholder="domain.com">
            <span slot="prefix">https://</span>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox label="Price" placeholder="0.00" type="number">
            <span slot="prefix">$</span>
            <span slot="suffix">USD</span>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox label="Weight" placeholder="Enter weight">
            <span slot="suffix">kg</span>
          </ava-textbox>
        </div>
        <div class="demo-item">
          <ava-textbox label="Phone" placeholder="************" type="tel">
            <ava-icon
              slot="icon-start"
              iconName="phone"
              [iconSize]="16"
              iconColor="var(--color-brand-primary)"
            >
            </ava-icon>
          </ava-textbox>
        </div>
      </div>
    </section>

    <!-- Form Example -->
    <section class="demo-section">
      <h2>Form Integration</h2>
      <form [formGroup]="demoForm" (ngSubmit)="onSubmit()" class="demo-form">
        <div class="form-grid">
          <div class="form-item">
            <ava-textbox
              label="Username"
              placeholder="Enter username"
              formControlName="username"
              [error]="getFieldError('username')"
              [required]="true"
            >
              <ava-icon
                slot="icon-start"
                iconName="user"
                [iconSize]="16"
                iconColor="var(--color-brand-primary)"
              >
              </ava-icon>
            </ava-textbox>
          </div>
          <div class="form-item">
            <ava-textbox
              label="Email"
              type="email"
              placeholder="Enter email"
              formControlName="email"
              [error]="getFieldError('email')"
              [required]="true"
            >
              <ava-icon
                slot="icon-start"
                iconName="mail"
                [iconSize]="16"
                iconColor="var(--color-brand-primary)"
              >
              </ava-icon>
            </ava-textbox>
          </div>
          <div class="form-item full-width">
            <ava-textbox
              label="Message"
              placeholder="Enter your message"
              formControlName="message"
            >
              <ava-icon
                slot="icon-start"
                iconName="message-square"
                [iconSize]="16"
                iconColor="var(--color-brand-primary)"
              >
              </ava-icon>
            </ava-textbox>
          </div>
          <div class="form-item full-width">
            <ava-button
              label="Submit Form"
              type="submit"
              variant="primary"
              [disabled]="demoForm.invalid"
            >
            </ava-button>
          </div>
        </div>
      </form>
    </section>
  </div>
</div>
