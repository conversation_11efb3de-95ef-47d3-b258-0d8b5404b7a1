<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>ava-textbox Component</h1>
        <p class="description">
          A comprehensive textbox component supporting all input types,
          variants, sizes, states, icons, prefix/suffix, and reactive forms.
        </p>
      </header>
    </div>
  </div>

  <!-- Play+ Metaphor Showcase -->
  <div style="max-width: 1200px; margin: 48px auto 32px auto">
    <h2
      style="
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        letter-spacing: -1px;
      "
    >
      Play+ Metaphor Intensity Showcase
    </h2>
    <p style="margin-bottom: 2rem; color: #888; font-size: 1.1rem">
      Experience the <b>Glass</b>, <b>Light</b>, and <b>Liquid</b> metaphors
      with intensity scales (0-100). Each metaphor can be dialed from subtle to
      dramatic, giving you complete control over the visual experience.
    </p>

    <!-- Special Glass Effect Showcase -->
    <div style="margin-bottom: 4rem">
      <h3 style="margin-bottom: 1rem; color: #666; text-align: center">
        ✨ Glass Effect Showcase ✨
      </h3>
      <div class="glass-showcase">
        <div
          style="
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
          "
        >
          <div style="text-align: center">
            <ava-textbox
              [metaphor]="'surface-30'"
              label="Light Glass (30)"
              placeholder="Subtle transparency"
              [ngStyle]="{
                '--textbox-input-color': 'white',
                '--textbox-border-color': 'white',
                '--textbox-label-color': 'white'
              }"
            ></ava-textbox>
          </div>
          <div style="text-align: center">
            <ava-textbox
              [metaphor]="'surface-60'"
              label="Medium Glass (60)"
              placeholder="Frosted effect"
              [ngStyle]="{
                '--textbox-input-color': 'white',
                '--textbox-border-color': 'white',
                '--textbox-label-color': 'white'
              }"
            ></ava-textbox>
          </div>
          <div style="text-align: center">
            <ava-textbox
              [metaphor]="'surface-90'"
              label="Heavy Glass (90)"
              placeholder="Deep translucency"
              [ngStyle]="{
                '--textbox-input-color': 'white',
                '--textbox-border-color': 'white',
                '--textbox-label-color': 'white',
                '--textbox-placeholder-color': 'black'
              }"
            ></ava-textbox>
          </div>
        </div>
      </div>
    </div>

    <!-- Glass Surface Intensity Scale -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">
        Glass (Surface) Intensity
      </h3>
      <div
        class="app-textbox-demo-bg-test"
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1.5rem;
        "
      >
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'surface-0'"
            label="Surface 0"
            placeholder="Fully solid"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'surface-25'"
            label="Surface 25"
            placeholder="Light glass"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'surface-50'"
            label="Surface 50"
            placeholder="Medium glass"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'surface-75'"
            label="Surface 75"
            placeholder="Heavy glass"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'surface-100'"
            label="Surface 100"
            placeholder="Maximum glass"
          ></ava-textbox>
        </div>
      </div>
    </div>

    <!-- Light Feedback Intensity Scale -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">
        Light (Feedback) Intensity
      </h3>
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1.5rem;
        "
      >
        <div class="light-demo-bg">
          <ava-textbox
            [metaphor]="'light-0'"
            label="Light 0"
            placeholder="No glow"
          ></ava-textbox>
        </div>
        <div class="light-demo-bg">
          <ava-textbox
            [metaphor]="'light-25'"
            label="Light 25"
            placeholder="Subtle glow"
          ></ava-textbox>
        </div>
        <div class="light-demo-bg">
          <ava-textbox
            [metaphor]="'light-50'"
            label="Light 50"
            placeholder="Medium glow"
          ></ava-textbox>
        </div>
        <div class="light-demo-bg">
          <ava-textbox
            [metaphor]="'light-75'"
            label="Light 75"
            placeholder="Strong glow"
          ></ava-textbox>
        </div>
        <div class="light-demo-bg">
          <ava-textbox
            [metaphor]="'light-100'"
            label="Light 100"
            placeholder="Maximum glow"
          ></ava-textbox>
        </div>
      </div>
    </div>

    <!-- Liquid Motion Intensity Scale -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">
        Liquid (Motion) Intensity
      </h3>
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1.5rem;
        "
      >
        <div class="motion-demo-bg">
          <ava-textbox
            [metaphor]="'motion-0'"
            label="Motion 0"
            placeholder="No animation"
          ></ava-textbox>
        </div>
        <div class="motion-demo-bg">
          <ava-textbox
            [metaphor]="'motion-25'"
            label="Motion 25"
            placeholder="Gentle ease"
          ></ava-textbox>
        </div>
        <div class="motion-demo-bg">
          <ava-textbox
            [metaphor]="'motion-50'"
            label="Motion 50"
            placeholder="Smooth flow"
          ></ava-textbox>
        </div>
        <div class="motion-demo-bg">
          <ava-textbox
            [metaphor]="'motion-75'"
            label="Motion 75"
            placeholder="Elastic bounce"
          ></ava-textbox>
        </div>
        <div class="motion-demo-bg">
          <ava-textbox
            [metaphor]="'motion-100'"
            label="Motion 100"
            placeholder="Extreme fluid"
          ></ava-textbox>
        </div>
      </div>
    </div>

    <!-- Combined Metaphor Intensities -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">
        Combined Metaphor Intensities
      </h3>
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        "
      >
        <div class="combined-demo-bg">
          <ava-textbox
            [metaphor]="['surface-30', 'light-20', 'motion-10']"
            label="Enterprise (Subtle)"
            placeholder="Conservative, professional"
          ></ava-textbox>
        </div>
        <div class="combined-demo-bg">
          <ava-textbox
            [metaphor]="['surface-50', 'light-50', 'motion-50']"
            label="Consumer (Balanced)"
            placeholder="Modern, approachable"
          ></ava-textbox>
        </div>
        <div class="combined-demo-bg">
          <ava-textbox
            [metaphor]="['surface-80', 'light-90', 'motion-80', 'gradient-100']"
            label="Marketing (Dramatic)"
            placeholder="Bold, expressive"
          ></ava-textbox>
        </div>
      </div>
    </div>

    <!-- Legacy Support -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">Legacy Metaphor Support</h3>
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1.5rem;
        "
      >
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'glass'"
            label="Legacy Glass"
            placeholder="Maps to surface-50"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'light'"
            label="Legacy Light"
            placeholder="Maps to light-50"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="'liquid'"
            label="Legacy Liquid"
            placeholder="Maps to motion-60"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            [metaphor]="['glass', 'light', 'liquid', 'gradient']"
            label="Legacy Combo"
            placeholder="All effects combined"
          ></ava-textbox>
        </div>
      </div>
    </div>

    <!-- Combined Effects Demo -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">Combined Effects</h3>
      <div class="combined-demo-bg">
        <ava-textbox
          [metaphor]="['surface-50', 'light-70', 'motion-80', 'gradient-60']"
          label="Ultimate Play+ Experience"
          placeholder="Glass + Light + Liquid + Gradient combo"
        ></ava-textbox>
      </div>
    </div>

    <!-- Custom Color Styling Demo -->
    <div style="margin-bottom: 3rem">
      <h3 style="margin-bottom: 1rem; color: #666">Custom Color Styling</h3>
      <p style="margin-bottom: 1rem; color: #888; font-size: 0.9rem">
        Use [ngStyle] or CSS custom properties to override colors dynamically.
      </p>
      <div
        style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 1.5rem;
        "
      >
        <div class="app-textbox-demo-bg">
          <ava-textbox
            label="Custom Pink Theme"
            placeholder="Pink borders & label"
            [ngStyle]="{
              '--textbox-label-color': '#e91e63',
              '--textbox-border-color': '#f8bbd9',
              '--textbox-focus-border-color': '#e91e63',
              '--textbox-placeholder-color': '#f48fb1'
            }"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            label="Ocean Blue Theme"
            placeholder="Blue ocean vibes"
            [ngStyle]="{
              '--textbox-label-color': '#2196f3',
              '--textbox-border-color': '#bbdefb',
              '--textbox-focus-border-color': '#1976d2',
              '--textbox-placeholder-color': '#90caf9'
            }"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            label="Violet Dream"
            placeholder="Purple magic"
            [ngStyle]="{
              '--textbox-label-color': '#9c27b0',
              '--textbox-border-color': '#e1bee7',
              '--textbox-focus-border-color': '#7b1fa2',
              '--textbox-placeholder-color': '#ce93d8'
            }"
          ></ava-textbox>
        </div>
        <div class="app-textbox-demo-bg">
          <ava-textbox
            label="Emerald Forest"
            placeholder="Green nature"
            [ngStyle]="{
              '--textbox-label-color': '#4caf50',
              '--textbox-border-color': '#c8e6c9',
              '--textbox-focus-border-color': '#388e3c',
              '--textbox-placeholder-color': '#a5d6a7'
            }"
          ></ava-textbox>
        </div>
      </div>
    </div>
  </div>

  <!-- Basic Examples -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Basic Examples</h2>
    <div class="app-textbox-demo-bg">
      <div
        style="
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          margin-bottom: 2rem;
        "
      >
        <ava-textbox
          label="Basic Input"
          placeholder="Enter text here"
          [(ngModel)]="basicValue"
          (textboxInput)="onTextboxInput($event)"
        ></ava-textbox>
        <ava-textbox
          label="Email Input"
          type="email"
          placeholder="<EMAIL>"
          [(ngModel)]="emailValue"
          iconPosition="start"
        ></ava-textbox>
      </div>
    </div>
  </div>

  <!-- Sizes -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Sizes</h2>
    <div class="app-textbox-demo-bg">
      <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
        <ava-textbox
          label="Small Size"
          placeholder="Small textbox"
          size="sm"
        ></ava-textbox>

        <ava-textbox
          label="Medium Size (Default)"
          placeholder="Medium textbox"
          size="md"
        ></ava-textbox>

        <ava-textbox
          label="Large Size"
          placeholder="Large textbox"
          size="lg"
        ></ava-textbox>
      </div>
    </div>
  </div>

  <!-- Variants -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Variants</h2>
    <div class="app-textbox-demo-bg">
      <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
        <ava-textbox
          label="Default"
          placeholder="Default variant"
          variant="default"
        ></ava-textbox>

        <ava-textbox
          label="Primary"
          placeholder="Primary variant"
          variant="primary"
        ></ava-textbox>

        <ava-textbox
          label="Success"
          placeholder="Success variant"
          variant="success"
          helper="This field looks good!"
        ></ava-textbox>

        <ava-textbox
          label="Warning"
          placeholder="Warning variant"
          variant="warning"
          helper="Please double-check this field"
        ></ava-textbox>

        <ava-textbox
          label="Error"
          placeholder="Error variant"
          variant="error"
          error="This field has an error"
        ></ava-textbox>
      </div>
    </div>
  </div>

  <!-- States -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>States</h2>
    <div class="app-textbox-demo-bg">
      <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
        <ava-textbox
          label="Disabled"
          placeholder="Disabled textbox"
          [disabled]="true"
          value="Disabled value"
        ></ava-textbox>

        <ava-textbox
          label="Read-only"
          placeholder="Read-only textbox"
          [readonly]="true"
          value="Read-only value"
        ></ava-textbox>

        <ava-textbox
          label="Required"
          placeholder="Required field"
          [required]="true"
        ></ava-textbox>
      </div>
    </div>
  </div>

  <!-- Icons -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>With Icons</h2>
    <div class="app-textbox-demo-bg">
      <div
        style="
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          margin-bottom: 2rem;
        "
      >
        <ava-textbox label="Search" placeholder="Search...">
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconColor]="'blue'"
            [iconSize]="16"
          ></ava-icon>
        </ava-textbox>

        <ava-textbox
          label="Password"
          [type]="showPassword ? 'text' : 'password'"
          placeholder="Enter password"
          [(ngModel)]="passwordValue"
        >
          <ava-icon
            slot="icon-end"
            [iconName]="showPassword ? 'eye' : 'eye-off'"
            (click)="togglePasswordVisibility()"
          ></ava-icon>
        </ava-textbox>
      </div>
    </div>
  </div>

  <!-- Prefix/Suffix -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Prefix & Suffix</h2>
    <div class="app-textbox-demo-bg">
      <div style="display: grid; gap: 1rem; margin-bottom: 2rem">
        <ava-textbox label="Website URL" placeholder="example">
          <span slot="prefix">https://</span>
          <span slot="suffix">.com</span>
        </ava-textbox>

        <ava-textbox label="Price" placeholder="0.00" type="number">
          <span slot="prefix">$</span>
        </ava-textbox>
      </div>
    </div>
  </div>

  <!-- Reactive Forms Demo -->
  <div style="max-width: 800px; margin: 40px auto">
    <h2>Reactive Forms Integration</h2>
    <form
      [formGroup]="demoForm"
      (ngSubmit)="onSubmit()"
      style="display: grid; gap: 1rem"
    >
      <ava-textbox
        label="Username"
        placeholder="Enter username"
        formControlName="username"
        [error]="getFieldError('username')"
        [required]="true"
      >
        <ava-icon
          slot="icon-start"
          iconName="user"
          [iconColor]="'purple'"
        ></ava-icon>
      </ava-textbox>

      <ava-textbox
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        formControlName="email"
        [error]="getFieldError('email')"
        [required]="true"
      >
        <ava-icon
          slot="icon-start"
          iconName="mail"
          [iconColor]="'purple'"
        ></ava-icon>
      </ava-textbox>

      <ava-textbox
        label="Phone"
        type="tel"
        placeholder="+****************"
        formControlName="phone"
      >
        <ava-icon
          slot="icon-start"
          iconName="phone"
          [iconColor]="'purple'"
        ></ava-icon>
      </ava-textbox>

      <ava-textbox
        label="Website"
        placeholder="your-site"
        formControlName="website"
      >
        <span slot="prefix">https://</span>
        <span slot="suffix">.com</span>
      </ava-textbox>

      <ava-button
        label="Submit Form"
        variant="primary"
        buttonSize="lg"
        type="submit"
      ></ava-button>
    </form>

    <div style="margin-top: 1rem">
      <strong>Form Status:</strong> {{ demoForm.valid ? "Valid" : "Invalid" }}
      <br />
      <strong>Form Value:</strong> {{ demoForm.value | json }}
    </div>
  </div>
</div>
