<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Slider Component</h1>
        <p class="description">
          A versatile slider component that supports multiple variants, custom ranges, 
          ticks, labels, and responsive design. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} SliderComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-slider
                  label="Basic Slider"
                  [value]="basicValue"
                  variant="primary"
                  tooltipPosition="bottom"
                ></awe-slider>
                </div>
              </div>
            </ng-container>

            <!-- Slider Variants -->
            <ng-container *ngSwitchCase="'Slider Variants'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-slider
                  label="Default Variant"
                  [value]="defaultVariantValue" 
                  (valueChange)="onSliderChange($event)"
                  variant="default"
                  tooltipPosition="top">
                </awe-slider>
                
                </div>
                <div class="col-12">
                  <awe-slider
                    label="Primary Variant"
                    [value]="basicValue"
                    variant="primary"
                    tooltipPosition="bottom"
                  ></awe-slider>
                </div>
              </div>
            </ng-container>

            <!-- Custom Range -->
            <ng-container *ngSwitchCase="'Custom Range'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-slider
                    label="Temperature (°C)"
                    [value]="temperatureValue"
                    [min]="0"
                    [max]="40"
                    [step]="0.5"
                    [formatLabel]="formatTemperature"
                    tooltipPosition="top"
                  ></awe-slider>
                </div>
                <div class="col-12">
                  <awe-slider
                  label="Volume Control (dB)"
                  [value]="volumeValue"
                  [min]="0"
                  [max]="100"
                  [step]="1"
                  [formatLabel]="formatVolume"
                  variant="primary"
                  tooltipPosition="bottom">
                </awe-slider>
                </div>
              </div>
            </ng-container>

            <!-- Responsive Behavior -->
            <ng-container *ngSwitchCase="'Responsive Behavior'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-slider
                    label="Responsive Slider"
                    [value]="responsiveValue"
                    mobileSize="small"
                    tabletSize="medium"
                    desktopSize="large"
                    touchTargetSize="44px"
                    [showTicks]="true"
                    [customTickValues]="[0, 25, 50, 75, 100]"
                    variant="primary"
                    (dragStart)="onDragStart()"
                    (dragEnd)="onDragEnd()"
                  ></awe-slider>
                </div>
              </div>
            </ng-container>

        

            <!-- Disabled State -->
            <ng-container *ngSwitchCase="'Disabled State'">
              <div class="row g-3">
                <div class="col-12">
                  <awe-slider
                    label="Disabled Default"
                    [value]="disabledValue"
                    [disabled]="true"
                    variant="default"
                  ></awe-slider>
                </div>
                <div class="col-12">
                  <awe-slider
                  class="custom-disabled-slider"
                label="Disabled Custom Range"
                [value]="customRangeValue"
                [disabled]="true"
                [min]="0"
                [max]="200"
                 [step]="10"
                variant="primary"
                ></awe-slider>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>valueChange</code></td>
              <td><code>EventEmitter&lt;number&gt;</code></td>
              <td>Emitted when the slider value changes</td>
            </tr>
            <tr>
              <td><code>dragStart</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when user starts dragging the slider</td>
            </tr>
            <tr>
              <td><code>dragEnd</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when user stops dragging the slider</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>