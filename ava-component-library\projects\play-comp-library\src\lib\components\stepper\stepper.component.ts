import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-stepper',
  standalone: true,
  imports: [CommonModule,IconComponent],
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'],
})
export class AvaStepperComponent {
  @Input() steps: string[] = [];
  @Input() currentStep = 0;
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() showNavigation: boolean = true;
  @Input() interactive: boolean = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() disabledSteps: number[] = [];
  @Input() iconColor: string = '#fff'; 
  @Input() iconSize: string = '20'; 
  

  @Output() stepChange = new EventEmitter<number>();
  @Output() submit = new EventEmitter<void>();

  isDisabled(i: number): boolean {
    return this.disabledSteps.includes(i);
  }

  goToStep(index: number): void {
    if (this.interactive && !this.isDisabled(index)) {
      this.stepChange.emit(index);
    }
  }

}



