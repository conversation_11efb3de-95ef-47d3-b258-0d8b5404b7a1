.ava-card-container {
    .ava-card {
        width: 100%;
        border-radius: 5px;
        padding: 20px;
        text-align: center;
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.05),
            6px 6px 12px rgba(0, 0, 0, 0.4),
            inset 1px 1px 2px rgba(255, 255, 255, 0.05),
            inset -1px -1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        background: linear-gradient(122.23deg, rgba(229, 247, 255, 0.8) 0%, rgba(211, 236, 255, 0.8) 12.2%, rgba(196, 224, 255, 0.8) 98.62%);
        box-shadow: 0px 4px 12px 0px #87A1971F;
        backdrop-filter: blur(6px);
        box-shadow: 0px 4px 12px 0px #87A1971F;
        backdrop-filter: blur(6px);

        .card-content {
            display: flex;
            flex-direction: column;
            gap: 0.5rem; // Optional: adds space between h3 and p
        }

        h3 {
            font-weight: normal;
            margin: 0;
        }

        p {
            margin: 0;
        }
    }
}