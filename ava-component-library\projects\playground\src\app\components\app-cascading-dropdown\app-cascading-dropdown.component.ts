import { Component, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownOption } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-cascading-dropdown',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './app-cascading-dropdown.component.html',
  styleUrl: './app-cascading-dropdown.component.scss'
})
export class AppCascadingDropdownComponent {

  constructor(private cdr: ChangeDetectorRef) {}
  // First dropdown options - Categories
  categoryOptions: DropdownOption[] = [
    { name: 'Car', value: 'car', icon: 'car' },
    { name: 'Fruits', value: 'fruits', icon: 'apple' },
    { name: 'Vegetables', value: 'vegetables', icon: 'carrot' }
  ];

  // Second dropdown options - will be populated based on category selection
  subCategoryOptions: DropdownOption[] = [];

  // Track selections
  selectedCategory: string = '';
  selectedSubCategory: string = '';

  // Control second dropdown state
  isSecondDropdownDisabled: boolean = true;

  // Data mapping for each category
  private categoryData: { [key: string]: DropdownOption[] } = {
    car: [
      { name: 'Toyota', value: 'toyota', icon: 'car' },
      { name: 'Honda', value: 'honda', icon: 'car' },
      { name: 'Ford', value: 'ford', icon: 'car' },
      { name: 'BMW', value: 'bmw', icon: 'car' },
      { name: 'Mercedes', value: 'mercedes', icon: 'car' },
      { name: 'Audi', value: 'audi', icon: 'car' }
    ],
    fruits: [
      { name: 'Apple', value: 'apple', icon: 'apple' },
      { name: 'Banana', value: 'banana', icon: 'banana' },
      { name: 'Orange', value: 'orange', icon: 'orange' },
      { name: 'Grapes', value: 'grapes', icon: 'grape' },
      { name: 'Strawberry', value: 'strawberry', icon: 'strawberry' },
      { name: 'Mango', value: 'mango', icon: 'mango' }
    ],
    vegetables: [
      { name: 'Carrot', value: 'carrot', icon: 'carrot' },
      { name: 'Broccoli', value: 'broccoli', icon: 'broccoli' },
      { name: 'Spinach', value: 'spinach', icon: 'leaf' },
      { name: 'Tomato', value: 'tomato', icon: 'tomato' },
      { name: 'Potato', value: 'potato', icon: 'potato' },
      { name: 'Onion', value: 'onion', icon: 'onion' }
    ]
  };

  // Handle first dropdown (category) selection
  onCategoryChange(selection: any) {
    console.log('Category selected:', selection);

    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      console.log('Selected option:', selectedOption);
      console.log('Selected option value:', selectedOption.value);
      console.log('Selected option name:', selectedOption.name);

      this.selectedCategory = selectedOption.name;

      // Load sub-category options based on selected category
      this.loadSubCategoryOptions(selectedOption.value);

      // Enable second dropdown
      this.isSecondDropdownDisabled = false;

      // Reset second dropdown selection
      this.selectedSubCategory = '';
    } else {
      // If no category selected, disable second dropdown
      this.selectedCategory = '';
      this.selectedSubCategory = '';
      this.subCategoryOptions = [];
      this.isSecondDropdownDisabled = true;
    }
  }

  // Handle second dropdown (sub-category) selection
  onSubCategoryChange(selection: any) {
    console.log('Sub-category selected:', selection);

    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedSubCategory = selectedOption.name;
    } else {
      this.selectedSubCategory = '';
    }
  }

  // Load sub-category options based on category
  private loadSubCategoryOptions(categoryValue: string) {
    console.log('Loading sub-category options for:', categoryValue);
    console.log('Available category data keys:', Object.keys(this.categoryData));
    console.log('Category data for', categoryValue, ':', this.categoryData[categoryValue]);

    if (this.categoryData[categoryValue]) {
      this.subCategoryOptions = [...this.categoryData[categoryValue]];
      console.log('Sub-category options loaded:', this.subCategoryOptions);
      this.cdr.detectChanges(); // Force change detection
    } else {
      this.subCategoryOptions = [];
      console.log('No data found for category:', categoryValue);
    }
  }

  // Get dynamic title for second dropdown
  getSecondDropdownTitle(): string {
    if (this.selectedCategory) {
      return `Select ${this.selectedCategory}`;
    }
    return 'Select Sub-category';
  }

  // Reset both dropdowns
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;
  }

  // Get code example for display
  getCodeExample(): string {
    return `// First Dropdown - Category Selection
<ava-dropdown
  dropdownTitle="Select Category"
  [options]="categoryOptions"
  [search]="true"
  (selectionChange)="onCategoryChange($event)">
</ava-dropdown>

// Second Dropdown - Filtered based on first selection
<ava-dropdown
  [dropdownTitle]="getSecondDropdownTitle()"
  [options]="subCategoryOptions"
  [disabled]="isSecondDropdownDisabled"
  [search]="true"
  (selectionChange)="onSubCategoryChange($event)">
</ava-dropdown>

// TypeScript Logic
onCategoryChange(selection: any) {
  if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
    const selectedOption = selection.selectedOptions[0];
    this.selectedCategory = selectedOption.name;
    this.loadSubCategoryOptions(selectedOption.value);
    this.isSecondDropdownDisabled = false;
  }
}`;
  }
}
