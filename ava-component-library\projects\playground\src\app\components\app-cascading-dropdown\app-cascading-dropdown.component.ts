import { Component, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownOption } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-cascading-dropdown',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './app-cascading-dropdown.component.html',
  styleUrl: './app-cascading-dropdown.component.scss'
})
export class AppCascadingDropdownComponent {

  constructor(private cdr: ChangeDetectorRef) {}
  // First dropdown options - Categories
  categoryOptions: DropdownOption[] = [
    { name: 'Car', value: 'car' },
    { name: 'Fruits', value: 'fruits' },
    { name: 'Vegetables', value: 'vegetables' }
  ];

  // Second dropdown options - will be populated based on category selection
  subCategoryOptions: DropdownOption[] = [];

  // Track selections - These values can be used by developers
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';

  // Control second dropdown state
  isSecondDropdownDisabled: boolean = true;

  // Complete selection data for API calls or form submissions
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Data mapping for each category
  private categoryData: { [key: string]: DropdownOption[] } = {
    car: [
      { name: 'Toyota', value: 'toyota' },
      { name: 'Honda', value: 'honda' },
      { name: 'Ford', value: 'ford' },
      { name: 'BMW', value: 'bmw' },
      { name: 'Mercedes', value: 'mercedes' },
      { name: 'Audi', value: 'audi' }
    ],
    fruits: [
      { name: 'Apple', value: 'apple' },
      { name: 'Banana', value: 'banana' },
      { name: 'Orange', value: 'orange' },
      { name: 'Grapes', value: 'grapes' },
      { name: 'Strawberry', value: 'strawberry' },
      { name: 'Mango', value: 'mango' }
    ],
    vegetables: [
      { name: 'Carrot', value: 'carrot' },
      { name: 'Broccoli', value: 'broccoli' },
      { name: 'Spinach', value: 'spinach' },
      { name: 'Tomato', value: 'tomato' },
      { name: 'Potato', value: 'potato' },
      { name: 'Onion', value: 'onion' }
    ]
  };

  // Handle first dropdown (category) selection
  onCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      // Update selection data object
      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
        this.cdr.detectChanges();
      }, 100);
    } else {
      this.resetSelections();
    }
  }

  // Handle second dropdown (sub-category) selection
  onSubCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      // Update complete selection data object
      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      // Here developers can add their logic for API calls, form submissions, etc.
      this.onSelectionComplete();
    } else {
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';
      this.selectionData.subCategory = { name: '', value: '' };
      this.selectionData.isComplete = false;
    }
  }

  // Load sub-category options based on category
  private loadSubCategoryOptions(categoryValue: string) {
    if (this.categoryData[categoryValue]) {
      this.subCategoryOptions = [...this.categoryData[categoryValue]];
      this.cdr.detectChanges(); // Force change detection
    } else {
      this.subCategoryOptions = [];
    }
  }

  // Get dynamic title for second dropdown
  getSecondDropdownTitle(): string {
    if (this.selectedCategory) {
      return `Select ${this.selectedCategory}`;
    }
    return 'Select Sub-category';
  }

  // Reset both dropdowns
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;

    // Reset selection data object
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };

    this.cdr.detectChanges();
  }

  // Called when both selections are complete - developers can customize this
  onSelectionComplete() {
    // Example: You can make API calls, update forms, navigate, etc.
    // console.log('Selection completed:', this.selectionData);

    // Example API call structure:
    // this.apiService.getFilteredData(this.selectionData.category.value, this.selectionData.subCategory.value);

    // Example form update:
    // this.form.patchValue({ category: this.selectionData.category.value, item: this.selectionData.subCategory.value });
  }

  // Get current selection for external use
  getCurrentSelection() {
    return this.selectionData;
  }

  // ===== 5-LEVEL CASCADING DROPDOWN EXAMPLE =====
  // Country → State → District → Area → Pincode

  // 5-Level dropdown options
  countryOptions: DropdownOption[] = [
    { name: 'India', value: 'india' },
    { name: 'United States', value: 'usa' },
    { name: 'United Kingdom', value: 'uk' },
    { name: 'Canada', value: 'canada' },
    { name: 'Australia', value: 'australia' }
  ];

  stateOptions: DropdownOption[] = [];
  districtOptions: DropdownOption[] = [];
  areaOptions: DropdownOption[] = [];
  pincodeOptions: DropdownOption[] = [];

  // 5-Level selections
  selectedCountry: string = '';
  selectedState: string = '';
  selectedDistrict: string = '';
  selectedArea: string = '';
  selectedPincode: string = '';

  selectedCountryValue: string = '';
  selectedStateValue: string = '';
  selectedDistrictValue: string = '';
  selectedAreaValue: string = '';
  selectedPincodeValue: string = '';

  // Control dropdown states
  isStateDisabled: boolean = true;
  isDistrictDisabled: boolean = true;
  isAreaDisabled: boolean = true;
  isPincodeDisabled: boolean = true;

  // Complete 5-level selection data
  locationData = {
    country: { name: '', value: '' },
    state: { name: '', value: '' },
    district: { name: '', value: '' },
    area: { name: '', value: '' },
    pincode: { name: '', value: '' },
    isComplete: false
  };

  // 5-Level data mapping
  private locationDataMapping: any = {
    india: {
      states: [
        { name: 'Maharashtra', value: 'maharashtra' },
        { name: 'Karnataka', value: 'karnataka' },
        { name: 'Tamil Nadu', value: 'tamilnadu' }
      ],
      districts: {
        maharashtra: [
          { name: 'Mumbai', value: 'mumbai' },
          { name: 'Pune', value: 'pune' },
          { name: 'Nashik', value: 'nashik' }
        ],
        karnataka: [
          { name: 'Bangalore', value: 'bangalore' },
          { name: 'Mysore', value: 'mysore' },
          { name: 'Hubli', value: 'hubli' }
        ],
        tamilnadu: [
          { name: 'Chennai', value: 'chennai' },
          { name: 'Coimbatore', value: 'coimbatore' },
          { name: 'Madurai', value: 'madurai' }
        ]
      },
      areas: {
        mumbai: [
          { name: 'Andheri', value: 'andheri' },
          { name: 'Bandra', value: 'bandra' },
          { name: 'Powai', value: 'powai' }
        ],
        pune: [
          { name: 'Koregaon Park', value: 'koregaonpark' },
          { name: 'Hinjewadi', value: 'hinjewadi' },
          { name: 'Wakad', value: 'wakad' }
        ],
        bangalore: [
          { name: 'Whitefield', value: 'whitefield' },
          { name: 'Koramangala', value: 'koramangala' },
          { name: 'Electronic City', value: 'electroniccity' }
        ]
      },
      pincodes: {
        andheri: [
          { name: '400053', value: '400053' },
          { name: '400058', value: '400058' },
          { name: '400059', value: '400059' }
        ],
        bandra: [
          { name: '400050', value: '400050' },
          { name: '400051', value: '400051' },
          { name: '400052', value: '400052' }
        ],
        whitefield: [
          { name: '560066', value: '560066' },
          { name: '560067', value: '560067' },
          { name: '560048', value: '560048' }
        ]
      }
    },
    usa: {
      states: [
        { name: 'California', value: 'california' },
        { name: 'New York', value: 'newyork' },
        { name: 'Texas', value: 'texas' }
      ],
      districts: {
        california: [
          { name: 'Los Angeles', value: 'losangeles' },
          { name: 'San Francisco', value: 'sanfrancisco' },
          { name: 'San Diego', value: 'sandiego' }
        ],
        newyork: [
          { name: 'Manhattan', value: 'manhattan' },
          { name: 'Brooklyn', value: 'brooklyn' },
          { name: 'Queens', value: 'queens' }
        ]
      },
      areas: {
        losangeles: [
          { name: 'Hollywood', value: 'hollywood' },
          { name: 'Beverly Hills', value: 'beverlyhills' },
          { name: 'Santa Monica', value: 'santamonica' }
        ],
        manhattan: [
          { name: 'Times Square', value: 'timessquare' },
          { name: 'Central Park', value: 'centralpark' },
          { name: 'Wall Street', value: 'wallstreet' }
        ]
      },
      pincodes: {
        hollywood: [
          { name: '90028', value: '90028' },
          { name: '90038', value: '90038' },
          { name: '90068', value: '90068' }
        ],
        timessquare: [
          { name: '10036', value: '10036' },
          { name: '10018', value: '10018' },
          { name: '10019', value: '10019' }
        ]
      }
    }
  };

  // 5-Level Event Handlers
  onCountryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedCountry = selectedOption.name;
      this.selectedCountryValue = selectedOption.value;

      // Reset all subsequent selections
      this.resetSubsequentSelections('country');

      // Load states for selected country
      this.loadStates(selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onStateChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedState = selectedOption.name;
      this.selectedStateValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('state');

      // Load districts for selected state
      this.loadDistricts(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onDistrictChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedDistrict = selectedOption.name;
      this.selectedDistrictValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('district');

      // Load areas for selected district
      this.loadAreas(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onAreaChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedArea = selectedOption.name;
      this.selectedAreaValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('area');

      // Load pincodes for selected area
      this.loadPincodes(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onPincodeChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedPincode = selectedOption.name;
      this.selectedPincodeValue = selectedOption.value;

      // Update location data and mark as complete
      this.updateLocationData();
      this.locationData.isComplete = true;

      // Call completion handler
      this.onLocationSelectionComplete();
    }
  }

  // Helper methods for 5-level cascading
  private loadStates(countryValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.stateOptions = countryData?.states || [];
    this.isStateDisabled = false;
    this.cdr.detectChanges();
  }

  private loadDistricts(countryValue: string, stateValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.districtOptions = countryData?.districts?.[stateValue] || [];
    this.isDistrictDisabled = false;
    this.cdr.detectChanges();
  }

  private loadAreas(countryValue: string, districtValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.areaOptions = countryData?.areas?.[districtValue] || [];
    this.isAreaDisabled = false;
    this.cdr.detectChanges();
  }

  private loadPincodes(countryValue: string, areaValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.pincodeOptions = countryData?.pincodes?.[areaValue] || [];
    this.isPincodeDisabled = false;
    this.cdr.detectChanges();
  }

  private resetSubsequentSelections(level: string) {
    switch (level) {
      case 'country':
        this.selectedState = '';
        this.selectedStateValue = '';
        this.stateOptions = [];
        this.isStateDisabled = true;
        // Fall through to reset all subsequent levels
      case 'state':
        this.selectedDistrict = '';
        this.selectedDistrictValue = '';
        this.districtOptions = [];
        this.isDistrictDisabled = true;
        // Fall through to reset all subsequent levels
      case 'district':
        this.selectedArea = '';
        this.selectedAreaValue = '';
        this.areaOptions = [];
        this.isAreaDisabled = true;
        // Fall through to reset all subsequent levels
      case 'area':
        this.selectedPincode = '';
        this.selectedPincodeValue = '';
        this.pincodeOptions = [];
        this.isPincodeDisabled = true;
        break;
    }
  }

  private updateLocationData() {
    this.locationData = {
      country: { name: this.selectedCountry, value: this.selectedCountryValue },
      state: { name: this.selectedState, value: this.selectedStateValue },
      district: { name: this.selectedDistrict, value: this.selectedDistrictValue },
      area: { name: this.selectedArea, value: this.selectedAreaValue },
      pincode: { name: this.selectedPincode, value: this.selectedPincodeValue },
      isComplete: false
    };
  }

  // Called when all 5 levels are selected
  onLocationSelectionComplete() {
    // Developers can add their logic here:
    // - API calls with complete address
    // - Form submissions
    // - Address validation
    // - Shipping calculations
  }

  // Reset all 5-level selections
  resetLocationSelections() {
    this.selectedCountry = '';
    this.selectedState = '';
    this.selectedDistrict = '';
    this.selectedArea = '';
    this.selectedPincode = '';

    this.selectedCountryValue = '';
    this.selectedStateValue = '';
    this.selectedDistrictValue = '';
    this.selectedAreaValue = '';
    this.selectedPincodeValue = '';

    this.stateOptions = [];
    this.districtOptions = [];
    this.areaOptions = [];
    this.pincodeOptions = [];

    this.isStateDisabled = true;
    this.isDistrictDisabled = true;
    this.isAreaDisabled = true;
    this.isPincodeDisabled = true;

    this.locationData = {
      country: { name: '', value: '' },
      state: { name: '', value: '' },
      district: { name: '', value: '' },
      area: { name: '', value: '' },
      pincode: { name: '', value: '' },
      isComplete: false
    };

    this.cdr.detectChanges();
  }

  // Get code example for display
  getCodeExample(): string {
    return `// HTML Template
<div class="dropdown-container">
  <!-- First Dropdown - Category Selection -->
  <ava-dropdown
    dropdownTitle="Select Category"
    [options]="categoryOptions"
    [search]="true"
    (selectionChange)="onCategoryChange($event)">
  </ava-dropdown>

  <!-- Second Dropdown - Filtered based on first selection -->
  <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="subCategoryOptions"
      [disabled]="false"
      [search]="true"
      [selectedValue]="''"
      (selectionChange)="onSubCategoryChange($event)">
    </ava-dropdown>
  </ng-container>

  <ng-template #disabledDropdown>
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="[]"
      [disabled]="true"
      [search]="true">
    </ava-dropdown>
  </ng-template>
</div>

// TypeScript Component
export class CascadingDropdownComponent {
  // Data properties
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';
  isSecondDropdownDisabled: boolean = true;
  subCategoryOptions: DropdownOption[] = [];

  // Complete selection data for developers
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Category options
  categoryOptions: DropdownOption[] = [
    { name: 'Car', value: 'car' },
    { name: 'Fruits', value: 'fruits' },
    { name: 'Vegetables', value: 'vegetables' }
  ];

  // Data mapping
  private categoryData: { [key: string]: DropdownOption[] } = {
    car: [
      { name: 'Toyota', value: 'toyota' },
      { name: 'Honda', value: 'honda' },
      { name: 'Ford', value: 'ford' }
    ],
    fruits: [
      { name: 'Apple', value: 'apple' },
      { name: 'Banana', value: 'banana' },
      { name: 'Orange', value: 'orange' }
    ]
  };

  // Handle category selection
  onCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
      }, 100);
    }
  }

  // Handle sub-category selection
  onSubCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      this.onSelectionComplete();
    }
  }

  // Load filtered options
  private loadSubCategoryOptions(categoryValue: string) {
    this.subCategoryOptions = this.categoryData[categoryValue] || [];
  }

  // Called when selection is complete
  onSelectionComplete() {
    // Add your custom logic here:
    // - API calls
    // - Form updates
    // - Navigation
    // - Data processing
  }

  // Reset all selections
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };
  }
}`;
  }
}
