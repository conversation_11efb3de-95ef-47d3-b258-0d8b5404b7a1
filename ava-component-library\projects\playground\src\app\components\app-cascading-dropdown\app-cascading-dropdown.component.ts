import { Component, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownOption } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-cascading-dropdown',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './app-cascading-dropdown.component.html',
  styleUrl: './app-cascading-dropdown.component.scss'
})
export class AppCascadingDropdownComponent {

  constructor(private cdr: ChangeDetectorRef) {}
  // First dropdown options - Categories
  categoryOptions: DropdownOption[] = [
    { name: 'Car', value: 'car' },
    { name: 'Fruits', value: 'fruits' },
    { name: 'Vegetables', value: 'vegetables' }
  ];

  // Second dropdown options - will be populated based on category selection
  subCategoryOptions: DropdownOption[] = [];

  // Track selections - These values can be used by developers
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';

  // Control second dropdown state
  isSecondDropdownDisabled: boolean = true;

  // Complete selection data for API calls or form submissions
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Data mapping for each category
  private categoryData: { [key: string]: DropdownOption[] } = {
    car: [
      { name: 'Toyota', value: 'toyota' },
      { name: 'Honda', value: 'honda' },
      { name: 'Ford', value: 'ford' },
      { name: 'BMW', value: 'bmw' },
      { name: 'Mercedes', value: 'mercedes' },
      { name: 'Audi', value: 'audi' }
    ],
    fruits: [
      { name: 'Apple', value: 'apple' },
      { name: 'Banana', value: 'banana' },
      { name: 'Orange', value: 'orange' },
      { name: 'Grapes', value: 'grapes' },
      { name: 'Strawberry', value: 'strawberry' },
      { name: 'Mango', value: 'mango' }
    ],
    vegetables: [
      { name: 'Carrot', value: 'carrot' },
      { name: 'Broccoli', value: 'broccoli' },
      { name: 'Spinach', value: 'spinach' },
      { name: 'Tomato', value: 'tomato' },
      { name: 'Potato', value: 'potato' },
      { name: 'Onion', value: 'onion' }
    ]
  };

  // Handle first dropdown (category) selection
  onCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      // Update selection data object
      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
        this.cdr.detectChanges();
      }, 100);
    } else {
      this.resetSelections();
    }
  }

  // Handle second dropdown (sub-category) selection
  onSubCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      // Update complete selection data object
      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      // Here developers can add their logic for API calls, form submissions, etc.
      this.onSelectionComplete();
    } else {
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';
      this.selectionData.subCategory = { name: '', value: '' };
      this.selectionData.isComplete = false;
    }
  }

  // Load sub-category options based on category
  private loadSubCategoryOptions(categoryValue: string) {
    if (this.categoryData[categoryValue]) {
      this.subCategoryOptions = [...this.categoryData[categoryValue]];
      this.cdr.detectChanges(); // Force change detection
    } else {
      this.subCategoryOptions = [];
    }
  }

  // Get dynamic title for second dropdown
  getSecondDropdownTitle(): string {
    if (this.selectedCategory) {
      return `Select ${this.selectedCategory}`;
    }
    return 'Select Sub-category';
  }

  // Reset both dropdowns
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;

    // Reset selection data object
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };

    this.cdr.detectChanges();
  }

  // Called when both selections are complete - developers can customize this
  onSelectionComplete() {
    // Example: You can make API calls, update forms, navigate, etc.
    // console.log('Selection completed:', this.selectionData);

    // Example API call structure:
    // this.apiService.getFilteredData(this.selectionData.category.value, this.selectionData.subCategory.value);

    // Example form update:
    // this.form.patchValue({ category: this.selectionData.category.value, item: this.selectionData.subCategory.value });
  }

  // Get current selection for external use
  getCurrentSelection() {
    return this.selectionData;
  }

  // ===== 5-LEVEL CASCADING DROPDOWN EXAMPLE =====
  // Country → State → District → Area → Pincode

  // 5-Level dropdown options
  countryOptions: DropdownOption[] = [
    { name: 'India', value: 'india' },
    { name: 'United States', value: 'usa' },
    { name: 'United Kingdom', value: 'uk' },
    { name: 'Canada', value: 'canada' },
    { name: 'Australia', value: 'australia' }
  ];

  stateOptions: DropdownOption[] = [];
  districtOptions: DropdownOption[] = [];
  areaOptions: DropdownOption[] = [];
  pincodeOptions: DropdownOption[] = [];

  // 5-Level selections
  selectedCountry: string = '';
  selectedState: string = '';
  selectedDistrict: string = '';
  selectedArea: string = '';
  selectedPincode: string = '';

  selectedCountryValue: string = '';
  selectedStateValue: string = '';
  selectedDistrictValue: string = '';
  selectedAreaValue: string = '';
  selectedPincodeValue: string = '';

  // Control dropdown states
  isStateDisabled: boolean = true;
  isDistrictDisabled: boolean = true;
  isAreaDisabled: boolean = true;
  isPincodeDisabled: boolean = true;

  // Complete 5-level selection data
  locationData = {
    country: { name: '', value: '' },
    state: { name: '', value: '' },
    district: { name: '', value: '' },
    area: { name: '', value: '' },
    pincode: { name: '', value: '' },
    isComplete: false
  };

  // 5-Level data mapping
  private locationDataMapping: any = {
    india: {
      states: [
        { name: 'Maharashtra', value: 'maharashtra' },
        { name: 'Karnataka', value: 'karnataka' },
        { name: 'Tamil Nadu', value: 'tamilnadu' }
      ],
      districts: {
        maharashtra: [
          { name: 'Mumbai', value: 'mumbai' },
          { name: 'Pune', value: 'pune' },
          { name: 'Nashik', value: 'nashik' }
        ],
        karnataka: [
          { name: 'Bangalore', value: 'bangalore' },
          { name: 'Mysore', value: 'mysore' },
          { name: 'Hubli', value: 'hubli' }
        ],
        tamilnadu: [
          { name: 'Chennai', value: 'chennai' },
          { name: 'Coimbatore', value: 'coimbatore' },
          { name: 'Madurai', value: 'madurai' }
        ]
      },
      areas: {
        mumbai: [
          { name: 'Andheri', value: 'andheri' },
          { name: 'Bandra', value: 'bandra' },
          { name: 'Powai', value: 'powai' }
        ],
        pune: [
          { name: 'Koregaon Park', value: 'koregaonpark' },
          { name: 'Hinjewadi', value: 'hinjewadi' },
          { name: 'Wakad', value: 'wakad' }
        ],
        bangalore: [
          { name: 'Whitefield', value: 'whitefield' },
          { name: 'Koramangala', value: 'koramangala' },
          { name: 'Electronic City', value: 'electroniccity' }
        ]
      },
      pincodes: {
        andheri: [
          { name: '400053', value: '400053' },
          { name: '400058', value: '400058' },
          { name: '400059', value: '400059' }
        ],
        bandra: [
          { name: '400050', value: '400050' },
          { name: '400051', value: '400051' },
          { name: '400052', value: '400052' }
        ],
        whitefield: [
          { name: '560066', value: '560066' },
          { name: '560067', value: '560067' },
          { name: '560048', value: '560048' }
        ]
      }
    },
    usa: {
      states: [
        { name: 'California', value: 'california' },
        { name: 'New York', value: 'newyork' },
        { name: 'Texas', value: 'texas' }
      ],
      districts: {
        california: [
          { name: 'Los Angeles', value: 'losangeles' },
          { name: 'San Francisco', value: 'sanfrancisco' },
          { name: 'San Diego', value: 'sandiego' }
        ],
        newyork: [
          { name: 'Manhattan', value: 'manhattan' },
          { name: 'Brooklyn', value: 'brooklyn' },
          { name: 'Queens', value: 'queens' }
        ],
        texas: [
          { name: 'Houston', value: 'houston' },
          { name: 'Dallas', value: 'dallas' },
          { name: 'Austin', value: 'austin' }
        ]
      },
      areas: {
        losangeles: [
          { name: 'Hollywood', value: 'hollywood' },
          { name: 'Beverly Hills', value: 'beverlyhills' },
          { name: 'Santa Monica', value: 'santamonica' }
        ],
        manhattan: [
          { name: 'Times Square', value: 'timessquare' },
          { name: 'Central Park', value: 'centralpark' },
          { name: 'Wall Street', value: 'wallstreet' }
        ],
        brooklyn: [
          { name: 'Williamsburg', value: 'williamsburg' },
          { name: 'Park Slope', value: 'parkslope' },
          { name: 'DUMBO', value: 'dumbo' }
        ],
        houston: [
          { name: 'Downtown', value: 'downtown' },
          { name: 'Galleria', value: 'galleria' },
          { name: 'Medical Center', value: 'medicalcenter' }
        ]
      },
      pincodes: {
        hollywood: [
          { name: '90028', value: '90028' },
          { name: '90038', value: '90038' },
          { name: '90068', value: '90068' }
        ],
        timessquare: [
          { name: '10036', value: '10036' },
          { name: '10018', value: '10018' },
          { name: '10019', value: '10019' }
        ],
        williamsburg: [
          { name: '11211', value: '11211' },
          { name: '11206', value: '11206' },
          { name: '11249', value: '11249' }
        ],
        downtown: [
          { name: '77002', value: '77002' },
          { name: '77010', value: '77010' },
          { name: '77046', value: '77046' }
        ]
      }
    },
    uk: {
      states: [
        { name: 'England', value: 'england' },
        { name: 'Scotland', value: 'scotland' },
        { name: 'Wales', value: 'wales' }
      ],
      districts: {
        england: [
          { name: 'London', value: 'london' },
          { name: 'Manchester', value: 'manchester' },
          { name: 'Birmingham', value: 'birmingham' }
        ],
        scotland: [
          { name: 'Edinburgh', value: 'edinburgh' },
          { name: 'Glasgow', value: 'glasgow' },
          { name: 'Aberdeen', value: 'aberdeen' }
        ],
        wales: [
          { name: 'Cardiff', value: 'cardiff' },
          { name: 'Swansea', value: 'swansea' },
          { name: 'Newport', value: 'newport' }
        ]
      },
      areas: {
        london: [
          { name: 'Westminster', value: 'westminster' },
          { name: 'Camden', value: 'camden' },
          { name: 'Kensington', value: 'kensington' }
        ],
        manchester: [
          { name: 'City Centre', value: 'citycentre' },
          { name: 'Salford', value: 'salford' },
          { name: 'Trafford', value: 'trafford' }
        ],
        edinburgh: [
          { name: 'Old Town', value: 'oldtown' },
          { name: 'New Town', value: 'newtown' },
          { name: 'Leith', value: 'leith' }
        ]
      },
      pincodes: {
        westminster: [
          { name: 'SW1A 1AA', value: 'sw1a1aa' },
          { name: 'SW1A 2AA', value: 'sw1a2aa' },
          { name: 'W1A 0AX', value: 'w1a0ax' }
        ],
        citycentre: [
          { name: 'M1 1AA', value: 'm11aa' },
          { name: 'M2 1BB', value: 'm21bb' },
          { name: 'M3 1CC', value: 'm31cc' }
        ],
        oldtown: [
          { name: 'EH1 1RF', value: 'eh11rf' },
          { name: 'EH1 2NG', value: 'eh12ng' },
          { name: 'EH8 8EE', value: 'eh88ee' }
        ]
      }
    },
    canada: {
      states: [
        { name: 'Ontario', value: 'ontario' },
        { name: 'Quebec', value: 'quebec' },
        { name: 'British Columbia', value: 'britishcolumbia' }
      ],
      districts: {
        ontario: [
          { name: 'Toronto', value: 'toronto' },
          { name: 'Ottawa', value: 'ottawa' },
          { name: 'Hamilton', value: 'hamilton' }
        ],
        quebec: [
          { name: 'Montreal', value: 'montreal' },
          { name: 'Quebec City', value: 'quebeccity' },
          { name: 'Laval', value: 'laval' }
        ],
        britishcolumbia: [
          { name: 'Vancouver', value: 'vancouver' },
          { name: 'Victoria', value: 'victoria' },
          { name: 'Surrey', value: 'surrey' }
        ]
      },
      areas: {
        toronto: [
          { name: 'Downtown', value: 'downtown' },
          { name: 'North York', value: 'northyork' },
          { name: 'Scarborough', value: 'scarborough' }
        ],
        montreal: [
          { name: 'Old Montreal', value: 'oldmontreal' },
          { name: 'Plateau', value: 'plateau' },
          { name: 'Westmount', value: 'westmount' }
        ],
        vancouver: [
          { name: 'Downtown', value: 'downtown' },
          { name: 'Kitsilano', value: 'kitsilano' },
          { name: 'Richmond', value: 'richmond' }
        ]
      },
      pincodes: {
        downtown: [
          { name: 'M5H 2N2', value: 'm5h2n2' },
          { name: 'M5V 3A8', value: 'm5v3a8' },
          { name: 'M5J 2R8', value: 'm5j2r8' }
        ],
        oldmontreal: [
          { name: 'H2Y 1C6', value: 'h2y1c6' },
          { name: 'H2Y 2E2', value: 'h2y2e2' },
          { name: 'H2Y 3Y5', value: 'h2y3y5' }
        ],
        kitsilano: [
          { name: 'V6K 1A1', value: 'v6k1a1' },
          { name: 'V6K 2H1', value: 'v6k2h1' },
          { name: 'V6K 3L5', value: 'v6k3l5' }
        ]
      }
    },
    australia: {
      states: [
        { name: 'New South Wales', value: 'newsouthwales' },
        { name: 'Victoria', value: 'victoria' },
        { name: 'Queensland', value: 'queensland' }
      ],
      districts: {
        newsouthwales: [
          { name: 'Sydney', value: 'sydney' },
          { name: 'Newcastle', value: 'newcastle' },
          { name: 'Wollongong', value: 'wollongong' }
        ],
        victoria: [
          { name: 'Melbourne', value: 'melbourne' },
          { name: 'Geelong', value: 'geelong' },
          { name: 'Ballarat', value: 'ballarat' }
        ],
        queensland: [
          { name: 'Brisbane', value: 'brisbane' },
          { name: 'Gold Coast', value: 'goldcoast' },
          { name: 'Cairns', value: 'cairns' }
        ]
      },
      areas: {
        sydney: [
          { name: 'CBD', value: 'cbd' },
          { name: 'Bondi', value: 'bondi' },
          { name: 'Manly', value: 'manly' }
        ],
        melbourne: [
          { name: 'CBD', value: 'cbd' },
          { name: 'St Kilda', value: 'stkilda' },
          { name: 'Fitzroy', value: 'fitzroy' }
        ],
        brisbane: [
          { name: 'CBD', value: 'cbd' },
          { name: 'South Bank', value: 'southbank' },
          { name: 'Fortitude Valley', value: 'fortitudevalley' }
        ]
      },
      pincodes: {
        cbd: [
          { name: '2000', value: '2000' },
          { name: '2001', value: '2001' },
          { name: '2002', value: '2002' }
        ],
        bondi: [
          { name: '2026', value: '2026' },
          { name: '2028', value: '2028' },
          { name: '2030', value: '2030' }
        ],
        stkilda: [
          { name: '3182', value: '3182' },
          { name: '3183', value: '3183' },
          { name: '3184', value: '3184' }
        ]
      }
    }
  };

  // 5-Level Event Handlers
  onCountryChange(selection: any) {
    console.log('Country selection received:', selection);

    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      console.log('Selected country option:', selectedOption);

      this.selectedCountry = selectedOption.name;
      this.selectedCountryValue = selectedOption.value;

      console.log('Set selectedCountry:', this.selectedCountry);
      console.log('Set selectedCountryValue:', this.selectedCountryValue);

      // Reset all subsequent selections
      this.resetSubsequentSelections('country');

      // Load states for selected country
      this.loadStates(selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onStateChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedState = selectedOption.name;
      this.selectedStateValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('state');

      // Load districts for selected state
      this.loadDistricts(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onDistrictChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedDistrict = selectedOption.name;
      this.selectedDistrictValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('district');

      // Load areas for selected district
      this.loadAreas(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onAreaChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedArea = selectedOption.name;
      this.selectedAreaValue = selectedOption.value;

      // Reset subsequent selections
      this.resetSubsequentSelections('area');

      // Load pincodes for selected area
      this.loadPincodes(this.selectedCountryValue, selectedOption.value);

      // Update location data
      this.updateLocationData();
    }
  }

  onPincodeChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedPincode = selectedOption.name;
      this.selectedPincodeValue = selectedOption.value;

      // Update location data and mark as complete
      this.updateLocationData();
      this.locationData.isComplete = true;

      // Call completion handler
      this.onLocationSelectionComplete();
    }
  }

  // Helper methods for 5-level cascading
  private loadStates(countryValue: string) {
    console.log('Loading states for country:', countryValue);
    console.log('Available countries in mapping:', Object.keys(this.locationDataMapping));

    const countryData = this.locationDataMapping[countryValue];
    console.log('Country data found:', countryData);

    this.stateOptions = countryData?.states || [];
    console.log('State options loaded:', this.stateOptions);

    this.isStateDisabled = false;
    this.cdr.detectChanges();
  }

  private loadDistricts(countryValue: string, stateValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.districtOptions = countryData?.districts?.[stateValue] || [];
    this.isDistrictDisabled = false;
    this.cdr.detectChanges();
  }

  private loadAreas(countryValue: string, districtValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.areaOptions = countryData?.areas?.[districtValue] || [];
    this.isAreaDisabled = false;
    this.cdr.detectChanges();
  }

  private loadPincodes(countryValue: string, areaValue: string) {
    const countryData = this.locationDataMapping[countryValue];
    this.pincodeOptions = countryData?.pincodes?.[areaValue] || [];
    this.isPincodeDisabled = false;
    this.cdr.detectChanges();
  }

  private resetSubsequentSelections(level: string) {
    if (level === 'country' || level === 'state' || level === 'district' || level === 'area') {
      if (level === 'country') {
        this.selectedState = '';
        this.selectedStateValue = '';
        this.stateOptions = [];
        this.isStateDisabled = true;
      }

      if (level === 'country' || level === 'state') {
        this.selectedDistrict = '';
        this.selectedDistrictValue = '';
        this.districtOptions = [];
        this.isDistrictDisabled = true;
      }

      if (level === 'country' || level === 'state' || level === 'district') {
        this.selectedArea = '';
        this.selectedAreaValue = '';
        this.areaOptions = [];
        this.isAreaDisabled = true;
      }

      if (level === 'country' || level === 'state' || level === 'district' || level === 'area') {
        this.selectedPincode = '';
        this.selectedPincodeValue = '';
        this.pincodeOptions = [];
        this.isPincodeDisabled = true;
      }
    }
  }

  private updateLocationData() {
    this.locationData = {
      country: { name: this.selectedCountry, value: this.selectedCountryValue },
      state: { name: this.selectedState, value: this.selectedStateValue },
      district: { name: this.selectedDistrict, value: this.selectedDistrictValue },
      area: { name: this.selectedArea, value: this.selectedAreaValue },
      pincode: { name: this.selectedPincode, value: this.selectedPincodeValue },
      isComplete: false
    };
  }

  // Called when all 5 levels are selected
  onLocationSelectionComplete() {
    // Developers can add their logic here:
    // - API calls with complete address
    // - Form submissions
    // - Address validation
    // - Shipping calculations
  }

  // Reset all 5-level selections
  resetLocationSelections() {
    this.selectedCountry = '';
    this.selectedState = '';
    this.selectedDistrict = '';
    this.selectedArea = '';
    this.selectedPincode = '';

    this.selectedCountryValue = '';
    this.selectedStateValue = '';
    this.selectedDistrictValue = '';
    this.selectedAreaValue = '';
    this.selectedPincodeValue = '';

    this.stateOptions = [];
    this.districtOptions = [];
    this.areaOptions = [];
    this.pincodeOptions = [];

    this.isStateDisabled = true;
    this.isDistrictDisabled = true;
    this.isAreaDisabled = true;
    this.isPincodeDisabled = true;

    this.locationData = {
      country: { name: '', value: '' },
      state: { name: '', value: '' },
      district: { name: '', value: '' },
      area: { name: '', value: '' },
      pincode: { name: '', value: '' },
      isComplete: false
    };

    this.cdr.detectChanges();
  }

  // Get code example for display
  getCodeExample(): string {
    return `// HTML Template
<div class="dropdown-container">
  <!-- First Dropdown - Category Selection -->
  <ava-dropdown
    dropdownTitle="Select Category"
    [options]="categoryOptions"
    [search]="true"
    (selectionChange)="onCategoryChange($event)">
  </ava-dropdown>

  <!-- Second Dropdown - Filtered based on first selection -->
  <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="subCategoryOptions"
      [disabled]="false"
      [search]="true"
      [selectedValue]="''"
      (selectionChange)="onSubCategoryChange($event)">
    </ava-dropdown>
  </ng-container>

  <ng-template #disabledDropdown>
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="[]"
      [disabled]="true"
      [search]="true">
    </ava-dropdown>
  </ng-template>
</div>

// TypeScript Component
export class CascadingDropdownComponent {
  // Data properties
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';
  isSecondDropdownDisabled: boolean = true;
  subCategoryOptions: DropdownOption[] = [];

  // Complete selection data for developers
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Category options
  categoryOptions: DropdownOption[] = [
    { name: 'Car', value: 'car' },
    { name: 'Fruits', value: 'fruits' },
    { name: 'Vegetables', value: 'vegetables' }
  ];

  // Data mapping
  private categoryData: { [key: string]: DropdownOption[] } = {
    car: [
      { name: 'Toyota', value: 'toyota' },
      { name: 'Honda', value: 'honda' },
      { name: 'Ford', value: 'ford' }
    ],
    fruits: [
      { name: 'Apple', value: 'apple' },
      { name: 'Banana', value: 'banana' },
      { name: 'Orange', value: 'orange' }
    ]
  };

  // Handle category selection
  onCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
      }, 100);
    }
  }

  // Handle sub-category selection
  onSubCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      this.onSelectionComplete();
    }
  }

  // Load filtered options
  private loadSubCategoryOptions(categoryValue: string) {
    this.subCategoryOptions = this.categoryData[categoryValue] || [];
  }

  // Called when selection is complete
  onSelectionComplete() {
    // Add your custom logic here:
    // - API calls
    // - Form updates
    // - Navigation
    // - Data processing
  }

  // Reset all selections
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };
  }
}`;
  }
}
