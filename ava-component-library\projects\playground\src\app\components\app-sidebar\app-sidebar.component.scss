// App sidebar styles - handles the content styling based on sidebar state
.sidebar-demo {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
  
  h2 {
    margin-bottom: 32px;
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
  }
  
  .demo-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    max-width: 1200px;
  }
  
  .demo-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    
    h3 {
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #374151;
    }
  }
}

// Default sidebar content styling
.demo-header {
  .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .logo-text {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
    }
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  
  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }
  
  &.active {
    background-color: #3b82f6;
    color: white;
    
    .nav-icon {
      color: white;
    }
  }
  
  .nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
  
  .nav-text {
    font-weight: 500;
    font-size: 14px;
  }
}

.nav-section {
  margin-bottom: 24px;
  gap:60px;
  
  .nav-section-title {
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 8px;
    padding: 0 16px;
  }
}

.demo-footer {
  .user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    background-color: #f9fafb;
    
    .profile-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #3b82f6;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    .profile-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .profile-name {
        font-weight: 600;
        font-size: 14px;
        color: #1e293b;
      }
      
      .profile-role {
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

// CRITICAL: Styles for when sidebar is collapsed
// The parent component handles how the content should look when collapsed
ava-sidebar.collapsed {
  // Hide text elements when collapsed
  .nav-text,
  .nav-section-title,
  .logo-text,
  .profile-name,
  .profile-role {
    display: none;
  }
  
  // Center nav items when collapsed
  .nav-item {
    justify-content: center;
    padding: 12px 8px;
    
    .nav-icon {
      margin: 0;
    }
  }
  
  // Center user profile when collapsed
  .user-profile {
    justify-content: center;
    
    .profile-info {
      display: none;
    }
  }
  
  // Hide section titles when collapsed
  .nav-section .nav-section-title {
    display: none;
  }
}

// Dark theme sidebar styling (keeping existing styles)
.dark-sidebar-wrapper {
  .dark-sidebar {
    background-color: #1f2937;
    border-right: 1px solid #374151;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    
    .sidebar-header {
      background-color: #111827;
      border-bottom: 1px solid #374151;
    }
    
    .sidebar-footer {
      background-color: #111827;
      border-top: 1px solid #374151;
    }
    
    .collapse-btn {
      color: #9ca3af !important;
      
      &:hover {
        background-color: #374151 !important;
        color: #f9fafb !important;
      }
    }
  }
  
  // Dark theme collapsed state
  .dark-sidebar.collapsed {
    .dark-nav-item {
      justify-content: center;
      padding: 12px 8px;
      
      .dark-item-content {
        display: none;
      }
      
      .dark-item-icon {
        margin: 0;
      }
    }
    
    .dark-section-title {
      display: none;
    }
    
    .dark-header .brand-text {
      display: none;
    }
    
    .dark-upgrade-card .dark-upgrade-content {
      display: none;
    }
    
    .dark-upgrade-card {
      justify-content: center;
    }
  }
}

// Continue with existing dark theme styles...
.dark-header {
  .brand {
    display: flex;
    align-items: center;
    gap: 12px;
    
    ava-icon {
      color: #3b82f6;
      font-size: 24px;
    }
    
    .brand-text {
      font-size: 20px;
      font-weight: 700;
      color: #f9fafb;
    }
  }
}

.dark-content {
  .dark-nav-section {
    margin-bottom: 24px;
    
    .dark-section-title {
      font-size: 12px;
      font-weight: 600;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 8px;
      padding: 0 4px;
    }
    
    .dark-nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      margin-bottom: 4px;
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #d1d5db;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 3px;
        background: linear-gradient(180deg, #3b82f6, #1d4ed8);
        transform: scaleY(0);
        transition: transform 0.3s ease;
      }
      
      &:hover {
        background-color: #374151;
        color: #f9fafb;
        transform: translateX(4px);
        
        &::before {
          transform: scaleY(1);
        }
        
        .dark-item-icon {
          background-color: #3b82f6;
          color: white;
        }
      }
      
      &.active {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        
        &::before {
          transform: scaleY(1);
        }
        
        .dark-item-icon {
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
        }
      }
      
      .dark-item-icon {
        width: 36px;
        height: 36px;
        border-radius: 10px;
        background-color: #374151;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.3s ease;
        
        ava-icon {
          width: 18px;
          height: 18px;
        }
      }
      
      .dark-item-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        
        .dark-item-text {
          font-weight: 500;
          font-size: 14px;
        }
        
        .dark-item-badge {
          background: linear-gradient(135deg, #ef4444, #dc2626);
          color: white;
          font-size: 11px;
          font-weight: 700;
          padding: 4px 8px;
          border-radius: 12px;
          min-width: 20px;
          text-align: center;
          box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }
      }
    }
  }
}

.dark-footer {
  .dark-upgrade-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #1f2937, #111827);
    border: 1px solid #374151;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    }
    
    &:hover {
      background: linear-gradient(135deg, #374151, #1f2937);
      border-color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }
    
    .dark-upgrade-icon {
      width: 32px;
      height: 32px;
      color: #fbbf24;
      flex-shrink: 0;
    }
    
    .dark-upgrade-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .dark-upgrade-title {
        font-weight: 600;
        font-size: 14px;
        color: #f9fafb;
      }
      
      .dark-upgrade-subtitle {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
}
// Multi Sidebar Container
.multi-sidebar-container {
  display: flex;
  gap: 24px;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
  
  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.basic-sidebar-wrapper,
.premium-sidebar-wrapper {
  flex: 1;
}
// Multi Sidebar Container
.multi-sidebar-container {
  display: inline-block;
  gap: 24px;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
  
  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.basic-sidebar-wrapper,
.premium-sidebar-wrapper {
  flex: 1;
}

// =====================================================
// BASIC SIDEBAR THEME - Simple and Clean
// =====================================================
// Higher specificity by targeting the actual ava-sidebar element
ava-sidebar.basic-sidebar-theme {
  background-color: #ffffff;
  border-right: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  
  // Override any existing sidebar styles with higher specificity
  &.ava-sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
  }
  
  .sidebar-header {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .sidebar-content {
    background-color: #ffffff;
  }
  
  .sidebar-footer {
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
  }
  
  .collapse-btn {
    color: #6b7280;
    
    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

// Basic Header Styles
.basic-demo-header {
  .basic-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .basic-logo-text {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }
}

// Basic Navigation Styles
.basic-nav-section {
  margin-bottom: 20px;
  
  .basic-nav-section-title {
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 8px;
    padding: 0 12px;
  }
  
  .basic-nav-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    
    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
    
    &.basic-active {
      background-color: #dbeafe;
      color: #1d4ed8;
      
      .basic-nav-icon {
        color: #1d4ed8;
      }
    }
    
    .basic-nav-icon {
      width: 18px;
      height: 18px;
      flex-shrink: 0;
    }
    
    .basic-nav-text {
      font-weight: 500;
      font-size: 14px;
    }
  }
}

// Basic Footer Styles
.basic-demo-footer {
  .basic-user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 6px;
    background-color: #f3f4f6;
    
    .basic-profile-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #6b7280;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    .basic-profile-info {
      display: flex;
      flex-direction: column;
      gap: 1px;
      
      .basic-profile-name {
        font-weight: 500;
        font-size: 13px;
        color: #1f2937;
      }
      
      .basic-profile-role {
        font-size: 11px;
        color: #6b7280;
      }
    }
  }
}

// Basic Collapsed State
ava-sidebar.basic-sidebar-theme.basic-collapsed {
  .basic-nav-text,
  .basic-nav-section-title,
  .basic-logo-text,
  .basic-profile-name,
  .basic-profile-role {
    display: none;
  }
  
  .basic-nav-item {
    justify-content: center;
    padding: 10px 6px;
    
    .basic-nav-icon {
      margin: 0;
    }
  }
  
  .basic-user-profile {
    justify-content: center;
    
    .basic-profile-info {
      display: none;
    }
  }
}

// =====================================================
// PREMIUM SIDEBAR THEME - Advanced and Feature-Rich
// =====================================================
// Higher specificity by targeting the actual ava-sidebar element
ava-sidebar.premium-sidebar-theme {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-right: 1px solid #334155;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
  
  // Override any existing sidebar styles with higher specificity
  &.ava-sidebar {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-right: 1px solid #334155;
  }
  
  .sidebar-header {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-bottom: 1px solid #475569;
    border-radius: 12px 12px 0 0;
  }
  
  .sidebar-content {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
  
  .sidebar-footer {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-top: 1px solid #475569;
    border-radius: 0 0 12px 12px;
  }
  
  .collapse-btn {
    color: #cbd5e1;
    
    &:hover {
      background-color: #475569;
      color: #f1f5f9;
    }
  }
}

// Premium Header Styles
.premium-demo-header {
  .premium-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    
    ava-icon {
      color: #3b82f6;
      font-size: 24px;
    }
    
    .premium-brand-text {
      font-size: 20px;
      font-weight: 700;
      color: #f1f5f9;
      background: linear-gradient(45deg, #3b82f6, #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

// Premium Navigation Styles
.premium-nav-section {
  margin-bottom: 24px;
  
  .premium-nav-section-title {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 12px;
    padding: 0 4px;
  }
  
  .premium-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 6px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #cbd5e1;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: linear-gradient(180deg, #3b82f6, #1d4ed8);
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }
    
    &:hover {
      background: linear-gradient(135deg, #475569, #334155);
      color: #f1f5f9;
      transform: translateX(4px);
      
      &::before {
        transform: scaleY(1);
      }
      
      .premium-item-icon {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        transform: scale(1.1);
      }
    }
    
    &.premium-active {
      background: linear-gradient(135deg, #3b82f6, #2563eb);
      color: white;
      
      &::before {
        transform: scaleY(1);
      }
      
      .premium-item-icon {
        background: linear-gradient(135deg, #60a5fa, #3b82f6);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }
    }
    
    .premium-item-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      background: linear-gradient(135deg, #475569, #334155);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      transition: all 0.3s ease;
      
      ava-icon {
        width: 20px;
        height: 20px;
        color: #cbd5e1;
      }
    }
    
    .premium-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      
      .premium-item-text {
        font-weight: 500;
        font-size: 14px;
      }
      
      .premium-item-badge {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        font-size: 10px;
        font-weight: 700;
        padding: 4px 8px;
        border-radius: 12px;
        min-width: 18px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        animation: pulse 2s infinite;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

// Premium Footer Styles
.premium-demo-footer {
  .premium-upgrade-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #475569, #334155);
    border: 1px solid #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    }
    
    &:hover {
      background: linear-gradient(135deg, #64748b, #475569);
      border-color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
    }
    
    .premium-upgrade-icon {
      width: 32px;
      height: 32px;
      color: #fbbf24;
      flex-shrink: 0;
      animation: glow 2s ease-in-out infinite alternate;
    }
    
    .premium-upgrade-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .premium-upgrade-title {
        font-weight: 600;
        font-size: 14px;
        color: #f1f5f9;
      }
      
      .premium-upgrade-subtitle {
        font-size: 12px;
        color: #94a3b8;
      }
    }
  }
}

@keyframes glow {
  from { filter: drop-shadow(0 0 5px #fbbf24); }
  to { filter: drop-shadow(0 0 15px #fbbf24); }
}

// Premium Collapsed State
ava-sidebar.premium-sidebar-theme.premium-collapsed {
  .premium-item-content,
  .premium-nav-section-title,
  .premium-brand-text,
  .premium-upgrade-content {
    display: none;
  }
  
  .premium-nav-item {
    justify-content: center;
    padding: 12px 8px;
    
    .premium-item-icon {
      margin: 0;
    }
  }
  
  .premium-upgrade-card {
    justify-content: center;
    
    .premium-upgrade-icon {
      margin: 0;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .multi-sidebar-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .basic-sidebar-wrapper,
  .premium-sidebar-wrapper {
    flex: none;
  }
}
