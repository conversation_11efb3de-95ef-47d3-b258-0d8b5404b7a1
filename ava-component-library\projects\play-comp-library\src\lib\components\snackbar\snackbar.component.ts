import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { SnackbarService } from './snackbar.service';

@Component({
  selector: 'ava-snackbar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './snackbar.component.html',
  styleUrls: ['./snackbar.component.scss'],
})
export class SnackbarComponent {
  snackbarService = inject(SnackbarService);

  // Directly expose the readonly signal to the template
  snackbar$ = this.snackbarService.snackbar$;
}
