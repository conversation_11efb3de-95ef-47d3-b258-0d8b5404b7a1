<h2>Inline Text Elements</h2>

<p>
  Here is a <awe-inline-element label="Hyperlink" inputId="link">
    <a href="https://google.co.in" class="link">useful link</a>
  </awe-inline-element> to explore more details.
</p>

<p>
  You can style text like this: 
  <awe-inline-element label="Styled Text" inputId="styled-text">
    <span class="styled-span">important styled text</span>
  </awe-inline-element> for better emphasis.
</p>

<p>
  If you need to highlight something, consider using 
  <awe-inline-element label="Bold Text" inputId="bold">
    <b class="bold-text">bold text</b>
  </awe-inline-element> or 
  <awe-inline-element label="Important Text" inputId="important">
    <strong class="important-text">strong emphasis</strong>
  </awe-inline-element>.
</p>

<p>
  Italicizing words can be useful, like 
  <awe-inline-element label="Italic Text" inputId="italic">
    <i class="italic-text">this text</i>
  </awe-inline-element>, to show subtle emphasis.
</p>

<p>
  Underlined content can be styled with:
  <awe-inline-element label="Underlined Text" inputId="underline">
    <u class="underlined-text">underlined text</u>
  </awe-inline-element> for additional importance.
</p>

<p>
  To highlight something, try 
  <awe-inline-element label="Highlighted Text" inputId="highlight">
    <mark class="highlighted-text">this highlighted text</mark>
  </awe-inline-element>.
</p>

<p>
  When you want to display a smaller font size, use 
  <awe-inline-element label="Smaller Text" inputId="small">
    <small class="small-text">smaller text</small>
  </awe-inline-element>.
</p>

<p>
  In scientific notation, we often write water as 
  <awe-inline-element label="Subscript" inputId="subscript">
    H<sub class="subscript">2</sub>O
  </awe-inline-element> or Einstein's equation as 
  <awe-inline-element label="Superscript" inputId="superscript">
    E = mc<sup class="superscript">2</sup>
  </awe-inline-element>.
</p>

<p>
  Writing code in text can be done using 
  <awe-inline-element label="Inline Code" inputId="code">
    <code class="inline-code">console.log('Hello, World!');</code>
  </awe-inline-element>.
</p>

<p>
  Keyboard shortcuts like 
  <awe-inline-element label="Keyboard Input" inputId="keyboard">
    <kbd class="keyboard-input">Ctrl</kbd> + <kbd class="keyboard-input">S</kbd>
  </awe-inline-element> help you save your work.
</p>

<p>
  Variables in math equations, such as 
  <awe-inline-element label="Variable" inputId="variable">
    <var class="variable">x</var>
  </awe-inline-element>, often represent unknown values.
</p>

<p>
  The meeting is scheduled for 
  <awe-inline-element label="Time Format" inputId="time">
    <time class="time-format" datetime="2025-02-11">February 11, 2025</time>
  </awe-inline-element>.
</p>

<p>
  You can enter text in this input field: 
  <awe-inline-element label="Form Input" inputId="input">
    <input class="form-input" type="text" placeholder="Enter text here">
  </awe-inline-element>.
</p>

<p>
  Click the following button: 
  <awe-inline-element label="Button" inputId="button">
    <button class="custom-button">Click Me</button>
  </awe-inline-element>.
</p>

<p>
  This label goes with the input field: 
  <awe-inline-element label="Label" inputId="label">
    <label class="custom-label" for="customInput">Custom Label</label>
    <input class="form-input" id="customInput" type="text">
  </awe-inline-element>.
</p>

<p>
  Did you know that 
  <awe-inline-element label="Abbreviation" inputId="abbr">
    <abbr class="abbreviation" title="Hypertext Markup Language">HTML</abbr>
  </awe-inline-element> stands for HyperText Markup Language?
</p>