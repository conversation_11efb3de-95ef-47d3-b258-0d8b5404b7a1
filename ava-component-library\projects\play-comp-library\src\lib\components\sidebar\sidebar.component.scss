.ava-sidebar-container {
  display: flex;
  position: relative;
}
.ava-sidebar {
  display: flex;
  flex-direction: column;
  min-width: 260px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  &.collapsed {
    min-width: 50px;
  }
  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    min-height: 64px;
    flex-shrink: 0;
    .header-content {
      flex: 1;
      margin-right: 12px;
      overflow: hidden;
    }
    .header-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      .collapse-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px 12px;
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #d1d5db;
      border-radius: 2px;
      &:hover {
        background-color: #9ca3af;
      }
    }
  }
  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
    flex-shrink: 0;
  }
  &.collapsed {
    .sidebar-header {
      justify-content: center;
      padding: 16px 8px;
    }
    .sidebar-content {
      padding: 16px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .sidebar-footer {
      padding: 16px 8px;
      display: flex;
      justify-content: center;
    }
  }
  @media (max-width: 768px) {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    &.collapsed {
      transform: translateX(-100%);
    }
  }
}
.hover-area {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  .hover-area-content {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    height: 100%;
    padding-top: 20px;
    opacity: 0;
    transition: opacity 0.2s ease;
    .collapse-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      &:active {
        transform: scale(0.95);
      }
    }
  }
  &:hover {
    .hover-area-content {
      opacity: 1;
    }
  }
}
.ava-sidebar:hover + .hover-area .hover-area-content {
  opacity: 1;
}
