.ava-sidebar {
    display: flex;
    flex-direction: column;
    min-width: 260px;
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    &.collapsed {
        min-width: 50px;
    }
    
    // Header section
    .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        background-color: #f9fafb;
        min-height: 64px;
        flex-shrink: 0;
        
        .header-content {
            flex: 1;
            margin-right: 12px;
            overflow: hidden;
        }
        
        .header-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            
            .collapse-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
                
                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }
    
    // Content section
    .sidebar-content {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 16px 12px;
        
        // Custom scrollbar
        &::-webkit-scrollbar {
            width: 4px;
        }
        
        &::-webkit-scrollbar-track {
            background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 2px;
            
            &:hover {
                background-color: #9ca3af;
            }
        }
    }
    
    // Footer section
    .sidebar-footer {
        padding: 16px;
        border-top: 1px solid #e5e7eb;
        background-color: #f9fafb;
        flex-shrink: 0;
    }
    
    // Collapsed state - only layout changes
    &.collapsed {
        .sidebar-header {
            justify-content: center;
            padding: 16px 8px;
            
        }
        
        .sidebar-content {
            padding: 16px 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .sidebar-footer {
            padding: 16px 8px;
            display: flex;
            justify-content: center;
        }
    }
    
    // Responsive design
    @media (max-width: 768px) {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        height: 100vh;
        
        &.collapsed {
            transform: translateX(-100%);
        }
    }
}