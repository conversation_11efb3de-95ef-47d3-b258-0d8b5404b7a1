import { Component, OnInit } from '@angular/core';
import { CascadingDropdownComponent, CascadingDropdownConfig, CascadingDropdownData } from 'ava-component-library';

@Component({
  selector: 'app-example',
  standalone: true,
  imports: [CascadingDropdownComponent],
  template: `
    <div class="example-container">
      <h3>Cascading Dropdown Example</h3>
      
      <!-- Your cascading dropdown component -->
      <ava-cascading-dropdown
        [dropdowns]="cascadingDropdowns"
        [autoDisableNext]="true"
        (dropdownChange)="onDropdownChange($event)"
        (allSelections)="onAllSelectionsChange($event)">
      </ava-cascading-dropdown>
      
      <!-- Display current selections -->
      <div class="selections-display" *ngIf="currentSelections.length > 0">
        <h4>Current Selections:</h4>
        <div *ngFor="let selection of currentSelections">
          <strong>{{getDropdownTitle(selection.dropdownId)}}:</strong> 
          {{selection.selectedOption?.name || 'None'}}
        </div>
      </div>
    </div>
  `,
  styles: [`
    .example-container {
      padding: 20px;
      max-width: 1200px;
    }
    
    .selections-display {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }
    
    .selections-display div {
      margin-bottom: 8px;
    }
  `]
})
export class CascadingDropdownExampleComponent implements OnInit {
  
  // Your cascading dropdown configuration
  cascadingDropdowns: CascadingDropdownConfig[] = [
    {
      id: 'country',
      title: 'Select Country',
      options: [
        { name: 'United States', value: 'us' },
        { name: 'Canada', value: 'ca' },
        { name: 'United Kingdom', value: 'uk' },
        { name: 'India', value: 'in' }
      ],
      disabled: false,
      search: true
    },
    {
      id: 'state',
      title: 'Select State',
      options: [], // Will be populated based on country selection
      disabled: true,
      search: true
    },
    {
      id: 'city',
      title: 'Select City',
      options: [], // Will be populated based on state selection
      disabled: true,
      search: true
    },
    {
      id: 'area',
      title: 'Select Area',
      options: [], // Will be populated based on city selection
      disabled: true,
      search: false
    }
  ];

  currentSelections: CascadingDropdownData[] = [];

  ngOnInit() {
    // Initialize if needed
  }

  // Handle individual dropdown changes
  onDropdownChange(changeData: CascadingDropdownData) {
    console.log('Dropdown changed:', changeData);
    
    // Based on which dropdown changed, fetch data for next dropdown
    switch (changeData.dropdownId) {
      case 'country':
        this.loadStatesForCountry(changeData.selectedValue);
        break;
      case 'state':
        this.loadCitiesForState(changeData.selectedValue);
        break;
      case 'city':
        this.loadAreasForCity(changeData.selectedValue);
        break;
    }
  }

  // Handle all selections change
  onAllSelectionsChange(allSelections: CascadingDropdownData[]) {
    this.currentSelections = allSelections.filter(s => s.selectedValue !== null);
    console.log('All selections:', allSelections);
  }

  // Example API calls - Replace these with your actual API calls
  private loadStatesForCountry(countryCode: string) {
    // Simulate API call
    setTimeout(() => {
      let states: any[] = [];
      
      switch (countryCode) {
        case 'us':
          states = [
            { name: 'California', value: 'ca' },
            { name: 'New York', value: 'ny' },
            { name: 'Texas', value: 'tx' },
            { name: 'Florida', value: 'fl' }
          ];
          break;
        case 'ca':
          states = [
            { name: 'Ontario', value: 'on' },
            { name: 'Quebec', value: 'qc' },
            { name: 'British Columbia', value: 'bc' }
          ];
          break;
        case 'uk':
          states = [
            { name: 'England', value: 'eng' },
            { name: 'Scotland', value: 'sco' },
            { name: 'Wales', value: 'wal' }
          ];
          break;
        case 'in':
          states = [
            { name: 'Maharashtra', value: 'mh' },
            { name: 'Karnataka', value: 'ka' },
            { name: 'Tamil Nadu', value: 'tn' }
          ];
          break;
      }
      
      // Update the state dropdown options
      this.updateDropdownOptions('state', states);
    }, 500); // Simulate API delay
  }

  private loadCitiesForState(stateCode: string) {
    // Simulate API call
    setTimeout(() => {
      let cities: any[] = [];
      
      switch (stateCode) {
        case 'ca': // California
          cities = [
            { name: 'Los Angeles', value: 'la' },
            { name: 'San Francisco', value: 'sf' },
            { name: 'San Diego', value: 'sd' }
          ];
          break;
        case 'ny': // New York
          cities = [
            { name: 'New York City', value: 'nyc' },
            { name: 'Buffalo', value: 'buf' },
            { name: 'Albany', value: 'alb' }
          ];
          break;
        case 'mh': // Maharashtra
          cities = [
            { name: 'Mumbai', value: 'mum' },
            { name: 'Pune', value: 'pun' },
            { name: 'Nagpur', value: 'nag' }
          ];
          break;
        // Add more cases as needed
        default:
          cities = [
            { name: 'City 1', value: 'city1' },
            { name: 'City 2', value: 'city2' }
          ];
      }
      
      // Update the city dropdown options
      this.updateDropdownOptions('city', cities);
    }, 500);
  }

  private loadAreasForCity(cityCode: string) {
    // Simulate API call
    setTimeout(() => {
      let areas: any[] = [];
      
      switch (cityCode) {
        case 'mum': // Mumbai
          areas = [
            { name: 'Andheri', value: 'and' },
            { name: 'Bandra', value: 'ban' },
            { name: 'Powai', value: 'pow' }
          ];
          break;
        case 'pun': // Pune
          areas = [
            { name: 'Koregaon Park', value: 'kp' },
            { name: 'Hinjewadi', value: 'hin' },
            { name: 'Viman Nagar', value: 'vn' }
          ];
          break;
        // Add more cases as needed
        default:
          areas = [
            { name: 'Area 1', value: 'area1' },
            { name: 'Area 2', value: 'area2' }
          ];
      }
      
      // Update the area dropdown options
      this.updateDropdownOptions('area', areas);
    }, 500);
  }

  // Helper method to update dropdown options
  private updateDropdownOptions(dropdownId: string, options: any[]) {
    const dropdown = this.cascadingDropdowns.find(d => d.id === dropdownId);
    if (dropdown) {
      dropdown.options = options;
      dropdown.disabled = false;
    }
  }

  // Helper method to get dropdown title by ID
  getDropdownTitle(dropdownId: string): string {
    const dropdown = this.cascadingDropdowns.find(d => d.id === dropdownId);
    return dropdown ? dropdown.title : dropdownId;
  }

  // Additional helper methods you can use:
  
  // Get current selection for a specific dropdown
  getSelectionForDropdown(dropdownId: string): CascadingDropdownData | null {
    return this.currentSelections.find(s => s.dropdownId === dropdownId) || null;
  }

  // Clear a specific dropdown and all subsequent ones
  clearFromDropdown(dropdownId: string) {
    const dropdownIndex = this.cascadingDropdowns.findIndex(d => d.id === dropdownId);
    if (dropdownIndex >= 0) {
      // Clear this dropdown and all subsequent ones
      for (let i = dropdownIndex; i < this.cascadingDropdowns.length; i++) {
        this.cascadingDropdowns[i].options = [];
        if (i > 0) {
          this.cascadingDropdowns[i].disabled = true;
        }
      }
    }
  }

  // Reset all dropdowns
  resetAllDropdowns() {
    this.cascadingDropdowns.forEach((dropdown, index) => {
      if (index > 0) {
        dropdown.options = [];
        dropdown.disabled = true;
      }
    });
    this.currentSelections = [];
  }
}
