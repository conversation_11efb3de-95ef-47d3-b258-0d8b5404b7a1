<!-- <h1>Light Themes</h1>
<section>
  <h2>Standard Modals</h2>
  <awe-modal [isOpen]="isSmallLightOpen" theme="light" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallLightOpen = false">
    <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
    <div content>
      <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
    </div>
    <div footer>
      <awe-button label="Label" variant="secondary" (click)="isSmallLightOpen = false"></awe-button>
      <awe-button label="Label" variant="primary" (click)="isSmallLightOpen = false"></awe-button>
    </div>
  </awe-modal>
   <awe-modal [isOpen]="isMediumLightOpen" theme="light" size="medium" position="center" [hasHeader]="true"  [hasFooter]="true" (close)="isMediumLightOpen = false">
    <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
    <div content>
      <awe-body-text type="body-test">
        <ng-container>
          "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
          <ul>
            <li>Generate content in seconds.</li>
              <li>Analyze data effortlessly.</li>
             <li>Get personalized recommendations tailored to your needs."</li>
          </ul>
        </ng-container>
      </awe-body-text>
    </div>
    <div footer class="footerbutton">
      <awe-button label="Label" variant="secondary" (click)="isMediumLightOpen = false"></awe-button>
      <awe-button label="Label" variant="primary" (click)="isMediumLightOpen = false"></awe-button>
    </div>
  </awe-modal>
    <awe-modal [isOpen]="isLargeLightOpen" theme="light" size="large" position="center" [hasHeader]="true"  [hasFooter]="true" (close)="isLargeLightOpen = false">
      <div content>
        <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
        <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-powered dashboard. </awe-heading>
        <awe-body-text type="body-test">
        <ng-container>
          <ul>
            <li>Real-time data visualization.</li>
             <li>Customizable widgets for tailored insights.</li>
              <li>AI-driven recommendations to optimize performance.</li>
          </ul>
        </ng-container>
      </awe-body-text>
      </div>
      <div footer class="footerbutton">
        <awe-button label="Label" variant="secondary" (click)="isLargeLightOpen = false"></awe-button>
        <awe-button label="Label" variant="primary" (click)="isLargeLightOpen = false"></awe-button>
      </div>
    </awe-modal>
  <awe-modal [isOpen]="isExtraLargeLightOpen" theme="light" size="extra-large" position="center"  [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeLightOpen = false">
    <div header>
      <awe-heading variant="h2" type="regular">From validation</awe-heading>
    </div>
    <div content>
      <awe-input label="Username" placeholder="Enter your username"></awe-input>
      <awe-input label="name" placeholder="Enter your name"></awe-input>
      <awe-input label="email" placeholder="Enter your email"></awe-input>
    </div>
    <div footer>
      <awe-button label="Close" variant="primary" (click)="isExtraLargeLightOpen = false"></awe-button>
    </div>
  </awe-modal>
  <button (click)="isSmallLightOpen = true">Open Small Modal</button>
  <button (click)="isMediumLightOpen = true">Open Medium Modal</button>
  <button (click)="isLargeLightOpen = true">Open Large Modal </button>
  <button (click)="isExtraLargeLightOpen = true">Open Extra Large Modal</button>
</section>



<h1>Dark Themes</h1>
<section>
  <h2>Standard Modals</h2>
  <awe-modal [isOpen]="isSmallDarkOpen" theme="dark" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallDarkOpen = false">
    <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
    <div content>
      <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
    </div>
    <div footer>
      <awe-button label="Label" variant="primary" (click)="isSmallDarkOpen = false"></awe-button>
      <awe-button label="Label" variant="secondary" (click)="isSmallDarkOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isMediumDarkOpen" theme="dark" size="medium" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isMediumDarkOpen = false">
    <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
    <div content>
      <awe-body-text type="body-test">
        <ng-container>
          "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
          <ul>
            <li>Generate content in seconds.</li>
              <li>Analyze data effortlessly.</li>
             <li>Get personalized recommendations tailored to your needs."</li>
          </ul>
        </ng-container>
      </awe-body-text>
    </div>
    <div footer class="footerbutton">
      <awe-button label="Label" variant="secondary" (click)="isMediumDarkOpen = false"></awe-button>
      <awe-button label="Label" variant="primary" (click)="isMediumDarkOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isLargeDarkOpen" theme="dark" size="large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isLargeDarkOpen = false">
    <div content>
      <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
      <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-<br>powered dashboard. </awe-heading>
      <awe-body-text type="body-test">
      <ng-container>
        <ul>
          <li>Real-time data visualization.</li>
           <li>Customizable widgets for tailored insights.</li>
            <li>AI-driven recommendations to optimize performance.</li>
        </ul>
      </ng-container>
    </awe-body-text>
    </div>
    <div footer class="footerbutton">
      <awe-button label="Label" variant="secondary" (click)="isLargeDarkOpen = false"></awe-button>
      <awe-button label="Label" variant="primary" (click)="isLargeDarkOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isExtraLargeDarkOpen" theme="dark" size="extra-large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeDarkOpen = false">
    <div header>
      <awe-heading variant="h2" type="regular">From validation</awe-heading>
    </div>
      <div content>
        <awe-input label="Username" placeholder="Enter your username"></awe-input>
        <awe-input label="name" placeholder="Enter your name"></awe-input>
        <awe-input label="email" placeholder="Enter your email"></awe-input>
      </div>
    <div footer>
      <awe-button label="Close" variant="primary" (click)="isExtraLargeDarkOpen = false"></awe-button>
    </div>
  </awe-modal>

  
  <button (click)="isSmallDarkOpen = true">Open Small Modal</button>
  <button (click)="isMediumDarkOpen = true">Open Medium Modal</button>
  <button (click)="isLargeDarkOpen = true">Open Large Modal </button>
  <button (click)="isExtraLargeDarkOpen = true">Open Extra Large Modal</button>

</section>

<h1>Animations</h1>
<section>
  <h2>Corner Modals</h2>
  <awe-modal [isOpen]="isTopLeftModalOpen" size="medium"  position="top-left" [animation]="true"  animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopLeftModalOpen = false">
    <div header>
      <awe-heading variant="h2" type="regular">Be always up to date</awe-heading>
    </div>
    <div content>
      <awe-body-text type="body-test">
        Do you want to receive the push notification about the newest posts?
      </awe-body-text>
    </div>
    <div footer>
      <awe-button label="No" variant="secondary" (click)="isTopLeftModalOpen = false"></awe-button>
      <awe-button label="Yes" variant="primary" (click)="isTopLeftModalOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isTopRightModalOpen" size="medium"  position="top-right" [animation]="true"  animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopRightModalOpen = false">
    <div header>
      <awe-heading variant="h2" type="regular">Product in the cart</awe-heading>
    </div>
    <div content>
      <awe-body-text type="body-test">
        Do you need more time to make a purchase decision?
        No pressure, your product will be waiting for you in the cart.
      </awe-body-text>
    </div>
    <div footer>
      <awe-button label="GO TO THE CART" variant="primary" (click)="isTopRightModalOpen = false"></awe-button>
      <awe-button label="Close" variant="secondary" (click)="isTopRightModalOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isBottomLeftModalOpen" size="medium"  position="bottom-left" [animation]="true"  animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomLeftModalOpen = false">
    <div content>
      <awe-input label="name" placeholder="Enter your name"></awe-input>
      <awe-input label="email" placeholder="Enter your email"></awe-input>
    </div>
    <div footer>
      <awe-checkbox label="I have read and agree to the terms" size="small"></awe-checkbox>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isBottomRightModalOpen" size="medium" position="bottom-right" [animation]="true"  animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomRightModalOpen = false">
    <div header>
      <awe-heading variant="h3" type="regular">Related article</awe-heading>
    </div>
    <div content>
      Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quod itaque voluptate nesciunt laborum incidunt. Officia, quam consectetur. Earum eligendi aliquam illum alias.
    </div>
    <div footer>
      <awe-button label="READ MORE" variant="primary" (click)="isBottomRightModalOpen = false"></awe-button>
      <awe-button label="Close" variant="primary" (click)="isBottomRightModalOpen = false"></awe-button>
    </div>
  </awe-modal>
  <button (click)="isTopLeftModalOpen = true">Open Top Left Modal</button>
  <button (click)="isTopRightModalOpen = true">Open Top Right Modal</button>
  <button (click)="isBottomLeftModalOpen = true">Open Bottom Left Modal</button>
  <button (click)="isBottomRightModalOpen = true">Open Bottom Right Modal</button>
</section>

<section>
  <h2>Fullscreen Modal</h2>
  <awe-modal [isOpen]="isFullscreenModalOpen" size="fullscreen"  position="center" [hasHeader]="true" [hasFooter]="true" (close)="isFullscreenModalOpen = false">
    <div header>
      <awe-heading variant="h1" type="regular">Checkout</awe-heading>
    </div>
    <div content>
      <awe-input label="First name" placeholder="Enter your first name"></awe-input>
      <awe-input label="Last name" placeholder="Enter your last name"></awe-input>
      <awe-input label="Compant name" placeholder="Enter your company name"></awe-input>
      <awe-input label="email" placeholder="Enter your email"></awe-input>
      <awe-input label="phone" placeholder="Enter your phone"></awe-input>
      <awe-checkbox label="Create an account?" size="small"></awe-checkbox>
    </div>
    <div footer>
      <awe-button label="Open" variant="primary" (click)="isFullscreenModalOpen = false"></awe-button>
      <awe-button label="Close" variant="primary" (click)="isFullscreenModalOpen = false"></awe-button>
    </div>
  </awe-modal>
  <button (click)="isFullscreenModalOpen = true">Open Fullscreen Modal</button>
</section>


<section>
  <h2>Frame Modal</h2>
   <awe-modal [isOpen]="isTopFrameModalOpen" size="medium"  position="top" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isTopFrameModalOpen = false">
    <div content>We have a gift for you! Use this code to get a 10% discount.</div>
    <div footer>
      <awe-button label="USE IT" variant="primary" (click)="isTopFrameModalOpen = false"></awe-button>
      <awe-button label="NO,THANKS" variant="primary" (click)="isTopFrameModalOpen = false"></awe-button>
    </div>
  </awe-modal>
  <awe-modal [isOpen]="isBottomFrameModalOpen" size="medium" position="bottom" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isBottomFrameModalOpen = false">
    <div content>We use cookies to improve your website experience</div>
    <div footer>
      <awe-button label="OK,THANKS" variant="primary" (click)="isBottomFrameModalOpen = false"></awe-button>
      <awe-button label="LEARN MORE" variant="primary" (click)="isBottomFrameModalOpen = false"></awe-button>
      </div>
  </awe-modal>
</section>

<button (click)="isTopFrameModalOpen = true">Open Top Frame Modal</button>
<button (click)="isBottomFrameModalOpen = true">Open Bottom Frame Modal</button>
 -->



 <div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Modal Component</h1>
        <p class="description">
          A versatile modal component offering various sizes, themes, positions, and animation effects. It can be used for displaying dialogs, forms, or any other content that requires user interaction.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ModalComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Modals -->
            <ng-container *ngSwitchCase="'Basic Modals'">
              <awe-modal [isOpen]="isSmallLightOpen" theme="light" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallLightOpen = false">
                <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
                <div content>
                  <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
                </div>
                <div footer>
                  <awe-button label="Label" variant="secondary" (click)="isSmallLightOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isSmallLightOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Small Modal" variant="primary" (click)="isSmallLightOpen = true"></awe-button>
            </ng-container>

            <!-- Modal Sizes -->
            <ng-container *ngSwitchCase="'Modal Sizes'">
              <awe-modal [isOpen]="isSmallLightOpen" theme="light" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallLightOpen = false">
                <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
                <div content>
                  <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
                </div>
                <div footer>
                  <awe-button label="Label" variant="secondary" (click)="isSmallLightOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isSmallLightOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Small Modal" variant="primary" (click)="isSmallLightOpen = true"></awe-button>

              <awe-modal [isOpen]="isMediumLightOpen" theme="light" size="medium" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isMediumLightOpen = false">
                <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
                <div content>
                  <awe-body-text type="body-test">
                    <ng-container>
                      "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
                      <ul>
                        <li>Generate content in seconds.</li>
                        <li>Analyze data effortlessly.</li>
                        <li>Get personalized recommendations tailored to your needs."</li>
                      </ul>
                    </ng-container>
                  </awe-body-text>
                </div>
                <div footer class="footerbutton">
                  <awe-button label="Label" variant="secondary" (click)="isMediumLightOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isMediumLightOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Medium Modal" variant="primary" (click)="isMediumLightOpen = true"></awe-button>

              <awe-modal [isOpen]="isLargeLightOpen" theme="light" size="large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isLargeLightOpen = false">
                <div content>
                  <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
                  <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-powered dashboard.</awe-heading>
                  <awe-body-text type="body-test">
                    <ng-container>
                      <ul>
                        <li>Real-time data visualization.</li>
                        <li>Customizable widgets for tailored insights.</li>
                        <li>AI-driven recommendations to optimize performance.</li>
                      </ul>
                    </ng-container>
                  </awe-body-text>
                </div>
                <div footer class="footerbutton">
                  <awe-button label="Label" variant="secondary" (click)="isLargeLightOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isLargeLightOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Large Modal" variant="primary" (click)="isLargeLightOpen = true"></awe-button>

              <awe-modal [isOpen]="isExtraLargeLightOpen" theme="light" size="extra-large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeLightOpen = false">
                <div header>
                  <awe-heading variant="h2" type="regular">From validation</awe-heading>
                </div>
                <div content>
                  <awe-input label="Username" placeholder="Enter your username"></awe-input>
                  <awe-input label="name" placeholder="Enter your name"></awe-input>
                  <awe-input label="email" placeholder="Enter your email"></awe-input>
                </div>
                <div footer>
                  <awe-button label="Close" variant="secondary" (click)="isExtraLargeLightOpen = false"></awe-button>
                  <awe-button label="Open" variant="primary" (click)="isExtraLargeLightOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Extra Large Modal" variant="primary" (click)="isExtraLargeLightOpen = true"></awe-button>
            </ng-container>

            <!-- Modal Themes -->
            <ng-container *ngSwitchCase="'Modal Themes'">
              <awe-modal [isOpen]="isSmallDarkOpen" theme="dark" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallDarkOpen = false">
                <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
                <div content>
                  <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
                </div>
                <div footer>
                  <awe-button label="Label" variant="secondary" (click)="isSmallDarkOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isSmallDarkOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Small Modal" variant="primary" (click)="isSmallDarkOpen = true"></awe-button>

              <awe-modal [isOpen]="isMediumDarkOpen" theme="dark" size="medium" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isMediumDarkOpen = false">
                <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
                <div content>
                  <awe-body-text type="body-test">
                    <ng-container>
                      "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
                      <ul>
                        <li>Generate content in seconds.</li>
                        <li>Analyze data effortlessly.</li>
                        <li>Get personalized recommendations tailored to your needs."</li>
                      </ul>
                    </ng-container>
                  </awe-body-text>
                </div>
                <div footer class="footerbutton">
                  <awe-button label="Label" variant="secondary" (click)="isMediumDarkOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isMediumDarkOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Medium Modal" variant="primary" (click)="isMediumDarkOpen = true"></awe-button>

              <awe-modal [isOpen]="isLargeDarkOpen" theme="dark" size="large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isLargeDarkOpen = false">
                <div content>
                  <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
                  <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-<br>powered dashboard.</awe-heading>
                  <awe-body-text type="body-test">
                    <ng-container>
                      <ul>
                        <li>Real-time data visualization.</li>
                        <li>Customizable widgets for tailored insights.</li>
                        <li>AI-driven recommendations to optimize performance.</li>
                      </ul>
                    </ng-container>
                  </awe-body-text>
                </div>
                <div footer class="footerbutton">
                  <awe-button label="Label" variant="secondary" (click)="isLargeDarkOpen = false"></awe-button>
                  <awe-button label="Label" variant="primary" (click)="isLargeDarkOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Large Modal" variant="primary" (click)="isLargeDarkOpen = true"></awe-button>

              <awe-modal [isOpen]="isExtraLargeDarkOpen" theme="dark" size="extra-large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeDarkOpen = false">
                <div header>
                  <awe-heading variant="h2" type="regular">From validation</awe-heading>
                </div>
                <div content>
                  <awe-input label="Username" placeholder="Enter your username"></awe-input>
                  <awe-input label="name" placeholder="Enter your name"></awe-input>
                  <awe-input label="email" placeholder="Enter your email"></awe-input>
                </div>
                <div footer>
                  <awe-button label="Close" variant="secondary" (click)="isExtraLargeDarkOpen = false"></awe-button>
                  <awe-button label="Open" variant="primary" (click)="isExtraLargeDarkOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Extra Large Modal" variant="primary" (click)="isExtraLargeDarkOpen = true"></awe-button>
            </ng-container>

            <!-- Modal Animations -->
            <ng-container *ngSwitchCase="'Modal Animations'">
              <awe-modal [isOpen]="isTopLeftModalOpen" size="medium" position="top-left" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopLeftModalOpen = false">
                <div header>
                  <awe-heading variant="h2" type="regular">Be always up to date</awe-heading>
                </div>
                <div content>
                  <awe-body-text type="body-test">
                    Do you want to receive the push notification about the newest posts?
                  </awe-body-text>
                </div>
                <div footer>
                  <awe-button label="No" variant="secondary" (click)="isTopLeftModalOpen = false"></awe-button>
                  <awe-button label="Yes" variant="primary" (click)="isTopLeftModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Top Left Modal" variant="primary" (click)="isTopLeftModalOpen = true"></awe-button>

              <awe-modal [isOpen]="isTopRightModalOpen" size="medium" position="top-right" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopRightModalOpen = false">
                <div header>
                  <awe-heading variant="h2" type="regular">Product in the cart</awe-heading>
                </div>
                <div content>
                  <awe-body-text type="body-test">
                    Do you need more time to make a purchase decision?
                    No pressure, your product will be waiting for you in the cart.
                  </awe-body-text>
                </div>
                <div footer>
                  <awe-button label="Close" variant="secondary" (click)="isTopRightModalOpen = false"></awe-button>
                  <awe-button label="GO TO THE CART" variant="primary" (click)="isTopRightModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Top Right Modal" variant="primary" (click)="isTopRightModalOpen = true"></awe-button>

              <awe-modal [isOpen]="isBottomLeftModalOpen" size="medium" position="bottom-left" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomLeftModalOpen = false">
                <div content>
                  <awe-input label="name" placeholder="Enter your name"></awe-input>
                  <awe-input label="email" placeholder="Enter your email"></awe-input>
                </div>
                <div footer>
                  <awe-checkbox label="I have read and agree to the terms" size="small"></awe-checkbox>
                </div>
              </awe-modal>
              <awe-button label="Open Bottom Left Modal" variant="primary" (click)="isBottomLeftModalOpen = true"></awe-button>

              <awe-modal [isOpen]="isBottomRightModalOpen" size="medium" position="bottom-right" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomRightModalOpen = false">
                <div header>
                  <awe-heading variant="h3" type="regular">Related article</awe-heading>
                </div>
                <div content>
                  Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quod itaque voluptate nesciunt laborum incidunt. Officia, quam consectetur. Earum eligendi aliquam illum alias.
                </div>
                <div footer>
                  <awe-button label="Close" variant="secondary" (click)="isBottomRightModalOpen = false"></awe-button>
                  <awe-button label="READ MORE" variant="primary" (click)="isBottomRightModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Bottom Right Modal" variant="primary" (click)="isBottomRightModalOpen = true"></awe-button>
            </ng-container>

            <!-- Fullscreen Modal -->
            <ng-container *ngSwitchCase="'Fullscreen Modal'">
              <awe-modal [isOpen]="isFullscreenModalOpen" size="fullscreen" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isFullscreenModalOpen = false">
                <div header>
                  <awe-heading variant="h1" type="regular">Checkout</awe-heading>
                </div>
                <div content>
                  <awe-input label="First name" placeholder="Enter your first name"></awe-input>
                  <awe-input label="Last name" placeholder="Enter your last name"></awe-input>
                  <awe-input label="Company name" placeholder="Enter your company name"></awe-input>
                  <awe-input label="email" placeholder="Enter your email"></awe-input>
                  <awe-input label="phone" placeholder="Enter your phone"></awe-input>
                  <awe-checkbox label="Create an account?" size="small"></awe-checkbox>
                </div>
                <div footer>
                  <awe-button label="Close" variant="secondary" (click)="isFullscreenModalOpen = false"></awe-button>
                  <awe-button label="Open" variant="primary" (click)="isFullscreenModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Fullscreen Modal" variant="primary" (click)="isFullscreenModalOpen = true"></awe-button>
            </ng-container>

            <!-- Frame Modal -->
            <ng-container *ngSwitchCase="'Frame Modal'">
              <awe-modal [isOpen]="isTopFrameModalOpen" size="medium" position="top" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isTopFrameModalOpen = false">
                <div content>We have a gift for you! Use this code to get a 10% discount.</div>
                <div footer>
                  <awe-button label="NO,THANKS" variant="secondary" (click)="isTopFrameModalOpen = false"></awe-button>
                  <awe-button label="USE IT" variant="primary" (click)="isTopFrameModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Top Frame Modal" variant="primary" (click)="isTopFrameModalOpen = true"></awe-button>

              <awe-modal [isOpen]="isBottomFrameModalOpen" size="medium" position="bottom" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isBottomFrameModalOpen = false">
                <div content>We use cookies to improve your website experience</div>
                <div footer>
                  <awe-button label="OK,THANKS" variant="secondary" (click)="isBottomFrameModalOpen = false"></awe-button>
                  <awe-button label="LEARN MORE" variant="primary" (click)="isBottomFrameModalOpen = false"></awe-button>
                </div>
              </awe-modal>
              <awe-button label="Open Bottom Frame Modal" variant="primary" (click)="isBottomFrameModalOpen = true"></awe-button>
            </ng-container>



            <ng-container *ngSwitchCase="'Experience studio'">
            <awe-modal [isOpen]="isExperienceStudioModalOpen" size="custom-variant" position="center" [hasHeader]="true" [hasFooter]="true" theme="light" (close)="isExperienceStudioModalOpen = false">
              <div header>
                <div class="modal-header">
                  <awe-heading variant="h3" type="bold">Export Project</awe-heading>
                  <awe-icons iconName="awe_close" (click)="isExperienceStudioModalOpen = false"></awe-icons>
                </div>
              
                <awe-heading variant="h6" type="regular">Project Name</awe-heading>
              
                <div class="input-button-wrapper">
                  <input class="input" type="text" placeholder="Enter your project name">
                 <awe-button
                    label="Save"
                    variant="primary"
                    class="save-button"
                    width="80px"
                    height="48px"
                    gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
                    hoverEffect="slide-bg">
                  </awe-button>
               </div>
              </div>
              
              <div content class="button-pill-grid">
                <div class="pill-row">
                  <div class="button-pill-item">
                  <awe-button variant="secondary" iconName="awe_exp_polygon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Connect to Database</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" iconName="awe_exp_squareicon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Unit Test Cases</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" iconName="awe_exp_dimondicon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Generate API</span>
                  </div>
                </div>
              
                <div class="divider-line"></div> <!-- horizontal line here -->
              
                <div class="pill-row">
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_download" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Download</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_vscode" iconColor="vscodeblue" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">VSCode</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_github"  iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">GitHub</span>
                  </div>
                </div>
              </div>
              
            </awe-modal>
            <awe-button label="Export" variant="primary" (click)="isExperienceStudioModalOpen = true"></awe-button>
          </ng-container>
        </ng-container>
       </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td><code>{{ event.name }}</code></td>
          <td><code>{{ event.type }}</code></td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
