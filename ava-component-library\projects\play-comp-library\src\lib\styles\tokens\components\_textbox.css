/* Component: Textbox
 * Purpose: Design tokens for Textbox component
 */
:root {
  /* Base */
  --color-border-hover: var(--color-border-default);
  
  --textbox-gap: var(--global-spacing-2);
  --textbox-gap-sm: var(--global-spacing-1);
  --textbox-gap-lg: var(--global-spacing-3);

  /* Label */
  --textbox-label-font: var(--font-label);
  --textbox-label-color: var(--color-text-primary);
  --textbox-label-margin: var(--global-spacing-1);
  --textbox-label-weight: 500;
  --textbox-required-color: var(--color-text-error);

  /* Container */
  --textbox-background: var(--app-surface);
  --textbox-border-color: var(--color-border-default);
  --textbox-border-radius: var(--global-radius-sm);
  --textbox-border-hover-color: var(--app-surface);
  --textbox-border-focus-color: var(--color-border-focus);
  --textbox-focus-shadow: var(--app-light);

  /* States */
  --textbox-background-disabled: var(--app-surface);
  --textbox-border-disabled-color: var(--color-border-disabled);
  --textbox-background-readonly: var(--color-background-subtle);
  --textbox-border-readonly-color: var(--color-border-subtle);

  /* Variants */
  --textbox-border-primary-color: var(--color-border-primary);
  --textbox-border-success-color: var(--color-border-success);
  --textbox-border-error-color: var(--color-border-error);
  --textbox-border-warning-color: var(--color-border-warning);
  --textbox-border-info-color: var(--color-border-info);

  /* Input */
  --textbox-input-font: var(--font-body-1);
  --textbox-input-color: var(--color-text-primary);
  --textbox-input-padding: 0.65rem 0;
  --textbox-input-min-height: 2.1rem;
  --textbox-placeholder-color: var(--color-text-placeholder);
  --textbox-input-disabled-color: var(--color-text-disabled);
  --textbox-input-readonly-color: var(--color-text-secondary);

  /* Input Sizes */
  --textbox-input-padding-sm: 0.5rem 0;
  --textbox-input-min-height-sm: 1.7rem;
  --textbox-input-font-size-sm: 0.875rem;
  --textbox-input-padding-lg: 0.85rem 0;
  --textbox-input-min-height-lg: 2.5rem;
  --textbox-input-font-size-lg: 1.05rem;

  /* Input with Icons */
  --textbox-input-icon-padding-start: 1.75rem;
  --textbox-input-icon-padding-end: 1.75rem;

  /* Icons */
  --textbox-icon-color: var(--color-text-secondary);
  --textbox-icon-focus-color: var(--color-text-primary);
  --textbox-icon-disabled-color: var(--color-text-disabled);
  --textbox-icon-position-start: 0.75rem;
  --textbox-icon-position-end: 0.75rem;

  /* Prefix/Suffix */
  --textbox-affix-padding: 0 0.75rem;
  --textbox-affix-color: var(--color-text-secondary);
  --textbox-affix-font-size: 0.875rem;
  --textbox-affix-background: var(--color-background-subtle);
  --textbox-affix-border-radius: 0;
  --textbox-affix-border-color: var(--color-border-subtle);
  --textbox-affix-disabled-color: var(--color-text-disabled);
  --textbox-affix-disabled-background: var(--color-background-disabled);

  /* Error */
  --textbox-error-gap: var(--global-spacing-1);
  --textbox-error-color: var(--color-text-error);
  --textbox-error-font-size: 0.875rem;

  /* Helper */
  --textbox-helper-gap: var(--global-spacing-1);
  --textbox-helper-color: var(--color-text-secondary);
  --textbox-helper-font-size: 0.875rem;

  /* Textarea */
  --textbox-textarea-min-height: 5rem;

  /* Border Width */
  --textbox-border-width: 0.5px;

  /* Glass (Surface) */
  --textbox-background: var(--app-surface);
  --textbox-background-hover: var(--app-surface);
  --textbox-background-disabled: var(--app-surface);

  /* Light (Feedback) */
  --textbox-focus-shadow: var(--app-light);
  --textbox-hover-shadow: var(--app-light);
  --textbox-error-shadow: var(--app-light);

  /* Liquid (Motion) */
  --textbox-transition: box-shadow 0.2s var(--app-motion), border-color 0.2s var(--app-motion);

  /* Gradient (optional, for expressive UIs) */
  --textbox-gradient: var(--app-gradient);

  /* Play+ Metaphor Tokens for ava-textbox */
  /* Glass Metaphor */
  --textbox-glass-bg: var(--surface-glass-bg);
  --textbox-glass-border: 2px solid var(--surface-glass-border);
  --textbox-glass-shadow: 0 4px 32px 0 var(--surface-glass-shadow);

  /* Light Metaphor */
  --textbox-light-shadow: 0 0 24px 6px var(--color-light-glow), 0 2px 8px var(--color-light-shadow);
  --textbox-light-border: 2px solid var(--color-light-border);
  --textbox-light-shadow-focus: 0 0 36px 12px var(--color-light-glow-focus), 0 2px 8px var(--color-light-shadow);
  --textbox-light-border-focus: var(--color-light-border-focus);

  /* Liquid Metaphor */
  --textbox-liquid-shimmer: linear-gradient(120deg, var(--color-liquid-shimmer-start) 0%, var(--color-liquid-shimmer-end) 100%);

  /* =======================
     TEXTBOX INTENSITY SCALES - Play+ Metaphor System
     Maps global intensity scales to textbox-specific semantic tokens
     ======================= */

  /* Glass (Surface) Intensity - Textbox Specific */
  --textbox-surface-0:   var(--surface-0);
  --textbox-surface-10:  var(--surface-10);
  --textbox-surface-20:  var(--surface-20);
  --textbox-surface-30:  var(--surface-30);
  --textbox-surface-40:  var(--surface-40);
  --textbox-surface-50:  var(--surface-50);
  --textbox-surface-60:  var(--surface-60);
  --textbox-surface-70:  var(--surface-70);
  --textbox-surface-80:  var(--surface-80);
  --textbox-surface-90:  var(--surface-90);
  --textbox-surface-100: var(--surface-100);

  /* Glass Blur - Textbox Specific */
  --textbox-surface-blur-0:   var(--surface-blur-0);
  --textbox-surface-blur-10:  var(--surface-blur-10);
  --textbox-surface-blur-20:  var(--surface-blur-20);
  --textbox-surface-blur-30:  var(--surface-blur-30);
  --textbox-surface-blur-40:  var(--surface-blur-40);
  --textbox-surface-blur-50:  var(--surface-blur-50);
  --textbox-surface-blur-60:  var(--surface-blur-60);
  --textbox-surface-blur-70:  var(--surface-blur-70);
  --textbox-surface-blur-80:  var(--surface-blur-80);
  --textbox-surface-blur-90:  var(--surface-blur-90);
  --textbox-surface-blur-100: var(--surface-blur-100);

  /* Light (Feedback) Intensity - Textbox Specific */
  --textbox-light-0:   var(--light-0);
  --textbox-light-10:  var(--light-10);
  --textbox-light-20:  var(--light-20);
  --textbox-light-30:  var(--light-30);
  --textbox-light-40:  var(--light-40);
  --textbox-light-50:  var(--light-50);
  --textbox-light-60:  var(--light-60);
  --textbox-light-70:  var(--light-70);
  --textbox-light-80:  var(--light-80);
  --textbox-light-90:  var(--light-90);
  --textbox-light-100: var(--light-100);

  /* Light Ring - Textbox Specific */
  --textbox-light-ring-0:   var(--light-ring-0);
  --textbox-light-ring-10:  var(--light-ring-10);
  --textbox-light-ring-20:  var(--light-ring-20);
  --textbox-light-ring-30:  var(--light-ring-30);
  --textbox-light-ring-40:  var(--light-ring-40);
  --textbox-light-ring-50:  var(--light-ring-50);
  --textbox-light-ring-60:  var(--light-ring-60);
  --textbox-light-ring-70:  var(--light-ring-70);
  --textbox-light-ring-80:  var(--light-ring-80);
  --textbox-light-ring-90:  var(--light-ring-90);
  --textbox-light-ring-100: var(--light-ring-100);

  /* Liquid (Motion) Intensity - Textbox Specific */
  --textbox-motion-0:   var(--motion-0);
  --textbox-motion-10:  var(--motion-10);
  --textbox-motion-20:  var(--motion-20);
  --textbox-motion-30:  var(--motion-30);
  --textbox-motion-40:  var(--motion-40);
  --textbox-motion-50:  var(--motion-50);
  --textbox-motion-60:  var(--motion-60);
  --textbox-motion-70:  var(--motion-70);
  --textbox-motion-80:  var(--motion-80);
  --textbox-motion-90:  var(--motion-90);
  --textbox-motion-100: var(--motion-100);

  /* Motion Duration - Textbox Specific */
  --textbox-motion-duration-0:   var(--motion-duration-0);
  --textbox-motion-duration-10:  var(--motion-duration-10);
  --textbox-motion-duration-20:  var(--motion-duration-20);
  --textbox-motion-duration-30:  var(--motion-duration-30);
  --textbox-motion-duration-40:  var(--motion-duration-40);
  --textbox-motion-duration-50:  var(--motion-duration-50);
  --textbox-motion-duration-60:  var(--motion-duration-60);
  --textbox-motion-duration-70:  var(--motion-duration-70);
  --textbox-motion-duration-80:  var(--motion-duration-80);
  --textbox-motion-duration-90:  var(--motion-duration-90);
  --textbox-motion-duration-100: var(--motion-duration-100);

  /* Gradient Intensity - Textbox Specific */
  --textbox-gradient-0:   var(--gradient-0);
  --textbox-gradient-10:  var(--gradient-10);
  --textbox-gradient-20:  var(--gradient-20);
  --textbox-gradient-30:  var(--gradient-30);
  --textbox-gradient-40:  var(--gradient-40);
  --textbox-gradient-50:  var(--gradient-50);
  --textbox-gradient-60:  var(--gradient-60);
  --textbox-gradient-70:  var(--gradient-70);
  --textbox-gradient-80:  var(--gradient-80);
  --textbox-gradient-90:  var(--gradient-90);
  --textbox-gradient-100: var(--gradient-100);

  /* Gradient Glass - Textbox Specific */
  --textbox-gradient-glass-0:   var(--gradient-glass-0);
  --textbox-gradient-glass-10:  var(--gradient-glass-10);
  --textbox-gradient-glass-20:  var(--gradient-glass-20);
  --textbox-gradient-glass-30:  var(--gradient-glass-30);
  --textbox-gradient-glass-40:  var(--gradient-glass-40);
  --textbox-gradient-glass-50:  var(--gradient-glass-50);
  --textbox-gradient-glass-60:  var(--gradient-glass-60);
  --textbox-gradient-glass-70:  var(--gradient-glass-70);
  --textbox-gradient-glass-80:  var(--gradient-glass-80);
  --textbox-gradient-glass-90:  var(--gradient-glass-90);
  --textbox-gradient-glass-100: var(--gradient-glass-100);
} 