// import { Component } from '@angular/core';
// import { ChatWindowComponent } from "../../../../../play-comp-library/src/lib/components/chat-window/chat-window.component";

// type IconStatus = "default" | "active" | "disable";

// @Component({
//   selector: 'app-app-chat-window',
//   standalone: true,
//   imports: [ChatWindowComponent],
//   templateUrl: './app-chat-window.component.html',
//   styleUrls: ['./app-chat-window.component.scss']
// })
// export class AppChatWindowComponent {
//   darkPrompt: string = '';
//   lightPrompt: string = '';

//   darkMessages: { text: string, from: 'user' | 'ai', theme: 'dark' }[] = [
//     { text: "This is a dark theme intro message from AI.", from: 'ai', theme: 'dark' }
//   ];

//   lightMessages: { text: string, from: 'user' | 'ai', theme: 'light' }[] = [
//     { text: "This is a light theme intro message from AI.", from: 'ai', theme: 'light' }
//   ];

//   rightIcons: { name: string; status: IconStatus }[] = [
//     { name: 'awe_enhanced_alternate', status: "default" },
//     { name: 'awe_enhance', status: "active" },
//     { name: 'awe_enhanced_send', status: "active" }
//   ];

//   handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
//     const normalizedIconName = event.name.toLowerCase();
//     switch (normalizedIconName) {
//       case "awe_enhanced_alternate":
//         this.handleEnhancedAlternate();
//         break;
//       case "awe_enhance":
//         this.handleEnhanceText();
//         break;
//       case "awe_enhanced_send":
//         if (event.theme === 'dark') {
//           this.handleEnhancedSendDark();
//         } else if (event.theme === 'light') {
//           this.handleEnhancedSendLight();
//         }
//         break;
//     }
//   }

//   handleEnhancedSendDark(): void {
//     if (!this.darkPrompt.trim()) return;

//     this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

//     setTimeout(() => {
//       this.darkMessages.push({
//         text: "This is an AI-generated reply to your message (dark theme).",
//         from: 'ai',
//         theme: 'dark'
//       });
//     }, 100);

//     this.darkPrompt = '';
//   }

//   handleEnhancedSendLight(): void {
//     if (!this.lightPrompt.trim()) return;

//     this.lightMessages.push({ text: this.lightPrompt, from: 'user', theme: 'light' });

//     setTimeout(() => {
//       this.lightMessages.push({
//         text: "This is an AI-generated reply to your message (light theme).",
//         from: 'ai',
//         theme: 'light'
//       });
//     }, 100);

//     this.lightPrompt = '';
//   }

//   handleEnhancedAlternate(): void {
//     const fileInput = document.createElement('input');
//     fileInput.type = 'file';
//     fileInput.style.display = 'none';

//     fileInput.addEventListener('change', (event: Event) => {
//       const input = event.target as HTMLInputElement;
//       if (input.files?.length) {
//         alert('File uploaded: ' + input.files[0].name);
//       }
//     });

//     document.body.appendChild(fileInput);
//     fileInput.click();
//     document.body.removeChild(fileInput);
//   }

//   handleEnhanceText(): void {
//     console.log('Enhance text logic not implemented yet.');
//   }
// }
import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { ChatWindowComponent } from "../../../../../play-comp-library/src/lib/components/chat-window/chat-window.component";
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';

@Component({
  selector: 'app-app-chat-window',
  imports: [ChatWindowComponent, CommonModule, IconsComponent],
  templateUrl: './app-chat-window.component.html',
  styleUrls: ['./app-chat-window.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppChatWindowComponent {
  @ViewChild('codeBlock') codeBlock!: ElementRef;

  sections = [
    {
      title: 'Basic Usage',
      description: 'Demonstrates the basic functionality of the chat window with light theme.',
      showCode: false,
    },
    {
      title: 'Dark Theme',
      description: 'Demonstrates the chat window with dark theme.',
      showCode: false,
    }
  ];

  apiProps = [
    { name: 'theme', type: '"light" | "dark"', default: '"light"', description: 'The theme of the chat window.' },
    { name: 'defaultText', type: 'string', default: '"Ask me"', description: 'The default text to display in the input field.' },
    { name: 'rightIcons', type: '{ name: string; status: "default" | "active" | "disable" }[]', default: '[]', description: 'The icons to display on the right side of the input field.' },
    { name: 'textValue', type: 'string', default: '""', description: 'The current text value in the input field.' },
    { name: 'chatMessages', type: '{ text: string, from: "user" | "ai", theme: "light" | "dark" }[]', default: '[]', description: 'The list of chat messages.' }

  ];

  events = [
    { name: 'iconClicked', type: 'EventEmitter<{ name: string; side: string; index: number; theme: string }>', description: 'Event emitted when an icon is clicked.' },
    { name: 'enterPressed', type: 'EventEmitter<void>', description: 'Event emitted when the enter key is pressed.' }
  ];

  rightIcons: { name: string; status: "default" | "active" | "disable" }[] = [
    { name: 'awe_enhanced_alternate', status: "default" },
    { name: 'awe_enhance', status: "active" },
    { name: 'awe_enhanced_send', status: "active" }
  ];

  darkPrompt: string = '';
  lightPrompt: string = '';

  darkMessages: { text: string, from: 'user' | 'ai', theme: 'dark' }[] = [
    { text: "This is a dark theme intro message from AI.", from: 'ai', theme: 'dark' }
  ];

  lightMessages: { text: string, from: 'user' | 'ai', theme: 'light' }[] = [
    { text: "This is a light theme intro message from AI.", from: 'ai', theme: 'light' }
  ];

  toggleSection(index: number): void {
    this.sections.forEach((section, i) => {
      section.showCode = (i === index) ? !section.showCode : false;
    });
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (this.codeBlock && !this.codeBlock.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  getChatWindowCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'basic usage': `
import { Component } from '@angular/core';
import { ChatWindowComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-chat-window',
  standalone: true,
  imports: [ChatWindowComponent],
  template: \`
    <section class="comp-container">
      <awe-chat-window
        [theme]="'light'"
        [defaultText]="'Ask me'"
        [rightIcons]="rightIcons"
        [(textValue)]="lightPrompt"
        [chatMessages]="lightMessages"
        (iconClicked)="handleIconClick($event)"
        (enterPressed)="handleEnhancedSendLight()">
      </awe-chat-window>
    </section>
  \`
})
export class BasicChatWindowComponent {
  lightPrompt: string = '';
  rightIcons: { name: string; status: "default" | "active" | "disable" }[] = [
    { name: 'awe_enhanced_alternate', status: "default" },
    { name: 'awe_enhance', status: "active" },
    { name: 'awe_enhanced_send', status: "active" }
  ];
  lightMessages: { text: string, from: 'user' | 'ai', theme: 'light' }[] = [
    { text: "This is a light theme intro message from AI.", from: 'ai', theme: 'light' }
  ];

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    // Handle icon click logic
  }

  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;

    this.lightMessages.push({ text: this.lightPrompt, from: 'user', theme: 'light' });

    setTimeout(() => {
      this.lightMessages.push({
        text: "This is an AI-generated reply to your message (light theme).",
        from: 'ai',
        theme: 'light'
      });
    }, 100);

    this.lightPrompt = '';
  }
}`,
      'dark theme': `
import { Component } from '@angular/core';
import { ChatWindowComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-dark-chat-window',
  standalone: true,
  imports: [ChatWindowComponent],
  template: \`
    <section class="comp-container">
      <awe-chat-window
        [theme]="'dark'"
        [defaultText]="'Ask me'"
        [rightIcons]="rightIcons"
        [(textValue)]="darkPrompt"
        [chatMessages]="darkMessages"
        (iconClicked)="handleIconClick($event)"
        (enterPressed)="handleEnhancedSendDark()">
      </awe-chat-window>
    </section>
  \`
})
export class DarkChatWindowComponent {
  darkPrompt: string = '';
  rightIcons: { name: string; status: "default" | "active" | "disable" }[] = [
    { name: 'awe_enhanced_alternate', status: "default" },
    { name: 'awe_enhance', status: "active" },
    { name: 'awe_enhanced_send', status: "active" }
  ];
  darkMessages: { text: string, from: 'user' | 'ai', theme: 'dark' }[] = [
    { text: "This is a dark theme intro message from AI.", from: 'ai', theme: 'dark' }
  ];

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    // Handle icon click logic
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: "This is an AI-generated reply to your message (dark theme).",
        from: 'ai',
        theme: 'dark'
      });
    }, 100);

    this.darkPrompt = '';
  }
}`
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getChatWindowCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case "awe_enhanced_alternate":
        this.handleEnhancedAlternate();
        break;
      case "awe_enhance":
        this.handleEnhanceText();
        break;
      case "awe_enhanced_send":
        if (event.theme === 'dark') {
          this.handleEnhancedSendDark();
        } else if (event.theme === 'light') {
          this.handleEnhancedSendLight();
        }
        break;
    }
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: "This is an AI-generated reply to your message (dark theme).",
        from: 'ai',
        theme: 'dark'
      });
    }, 100);

    this.darkPrompt = '';
  }

  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;

    this.lightMessages.push({ text: this.lightPrompt, from: 'user', theme: 'light' });

    setTimeout(() => {
      this.lightMessages.push({
        text: "This is an AI-generated reply to your message (light theme).",
        from: 'ai',
        theme: 'light'
      });
    }, 100);

    this.lightPrompt = '';
  }

  handleEnhancedAlternate(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  handleEnhanceText(): void {
    console.log('Enhance text logic not implemented yet.');
  }
}
