<div class="documentation">
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Badges Component</h1>
        <p class="description">
          A versatile badge component that displays numerical values with
          different color schemes and sizes. Built with accessibility and user
          experience in mind.
        </p>
      </header>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} BadgesComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <div class="doc-sections">
    <section class="doc-section" *ngFor="let section of sections; let i = index">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Large Size Badges'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-badges state="information" size="large" [count]="1"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="information" size="large" [count]="10"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="information" size="large" [count]="100"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="information" size="large" [count]="1000"></ava-badges>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Medium Size Badges'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-badges state="low-priority" size="medium" [count]="1"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="low-priority" size="medium" [count]="10"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="low-priority" size="medium" [count]="100"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="low-priority" size="medium" [count]="1000"></ava-badges>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Small Size Badges'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-badges state="medium-priority" size="small" [count]="1"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="medium-priority" size="small" [count]="10"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="medium-priority" size="small" [count]="100"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="medium-priority" size="small" [count]="1000"></ava-badges>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Badges with Icons'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-badges state="high-priority" size="small" iconName="Mail" iconColor="white" [iconSize]="12"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="high-priority" size="medium" iconName="wifi" iconColor="white" [iconSize]="12"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="high-priority" size="large" iconName="user" iconColor="white" [iconSize]="12"></ava-badges>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Badges with Text'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-badges state="high-priority" size="large" [count]="3"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="medium-priority" size="medium" [count]="15"></ava-badges>
                </div>
                <div class="col-12 col-sm-auto">
                  <ava-badges state="low-priority" size="small" [count]="999"></ava-badges>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Types of Badges'">
              <div class="row g-3">
                <div class="state-container">
                  <h4>High Priority:</h4>
                  <div class="badge-row">
                    <ava-badges state="high-priority" size="small" [count]="3"></ava-badges>
                    <ava-badges state="high-priority" size="medium" [count]="3"></ava-badges>
                    <ava-badges state="high-priority" size="large" [count]="3"></ava-badges>
                  </div>
                </div>
                <div class="state-container">
                  <h4>Medium Priority:</h4>
                  <div class="badge-row">
                    <ava-badges state="medium-priority" size="small" [count]="4"></ava-badges>
                    <ava-badges state="medium-priority" size="medium" [count]="4"></ava-badges>
                    <ava-badges state="medium-priority" size="large" [count]="4"></ava-badges>
                  </div>
                </div>
                <div class="state-container">
                  <h4>Low Priority:</h4>
                  <div class="badge-row">
                    <ava-badges state="low-priority" size="small" [count]="4"></ava-badges>
                    <ava-badges state="low-priority" size="medium" [count]="4"></ava-badges>
                    <ava-badges state="low-priority" size="large" [count]="4"></ava-badges>
                  </div>
                </div>
                <div class="state-container">
                  <h4>Neutral:</h4>
                  <div class="badge-row">
                    <ava-badges state="neutral" size="small" [count]="4"></ava-badges>
                    <ava-badges state="neutral" size="medium" [count]="4"></ava-badges>
                    <ava-badges state="neutral" size="large" [count]="4"></ava-badges>
                  </div>
                </div>
                <div class="state-container">
                  <h4>Information:</h4>
                  <div class="badge-row">
                    <ava-badges state="information" size="small" [count]="4"></ava-badges>
                    <ava-badges state="information" size="medium" [count]="4"></ava-badges>
                    <ava-badges state="information" size="large" [count]="4"></ava-badges>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getBadgeCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- Icon for copy button -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td><code>{{ prop.name }}</code></td>
          <td><code>{{ prop.type }}</code></td>
          <td><code>{{ prop.default }}</code></td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</div>
