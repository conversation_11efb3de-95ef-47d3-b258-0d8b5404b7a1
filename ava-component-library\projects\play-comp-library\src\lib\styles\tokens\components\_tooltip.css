/**
 * =========================================================================
 * Play+ Design System: Tooltip Component Tokens
 *
 * Component-specific semantic tokens for tooltip elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for tooltip styling.
 * =========================================================================
 */

:root {
  /* --- Tooltip Base --- */
  --tooltip-background: var(--global-color-senary);
  --tooltip-text: var(--color-text-on-brand);
  --tooltip-font: var(--font-body-2);
  --tooltip-opacity: 0.9;
  --tooltip-border-radius: var(--global-radius-sm);
  --tooltip-shadow: var(--global-elevation-03);
  --tooltip-padding: var(--global-spacing-3) var(--global-spacing-4);
  --tooltip-max-width: 300px;
  --tooltip-z-index: 9999;

  /* --- Tooltip Arrow --- */
  --tooltip-arrow-size: 8px;
  --tooltip-arrow-color: var(--tooltip-background);
  --tooltip-arrow-border: var(--tooltip-arrow-size) solid transparent;

  /* --- Tooltip Positions --- */
  --tooltip-position-top-arrow: var(--tooltip-arrow-border);
  --tooltip-position-top-arrow-border-color: var(--tooltip-arrow-color) transparent transparent transparent;

  --tooltip-position-bottom-arrow: var(--tooltip-arrow-border);
  --tooltip-position-bottom-arrow-border-color: transparent transparent var(--tooltip-arrow-color) transparent;

  --tooltip-position-left-arrow: var(--tooltip-arrow-border);
  --tooltip-position-left-arrow-border-color: transparent transparent transparent var(--tooltip-arrow-color);

  --tooltip-position-right-arrow: var(--tooltip-arrow-border);
  --tooltip-position-right-arrow-border-color: transparent var(--tooltip-arrow-color) transparent transparent;

  /* --- Tooltip Sizes --- */
  --tooltip-size-sm-font: var(--font-caption);
  --tooltip-size-sm-padding: var(--global-spacing-2) var(--global-spacing-3);
  --tooltip-size-sm-max-width: 200px;

  --tooltip-size-md-font: var(--font-label);
  --tooltip-size-md-padding: var(--global-spacing-3) var(--global-spacing-4);
  --tooltip-size-md-max-width: 300px;

  --tooltip-size-lg-font: var(--font-body-2);
  --tooltip-size-lg-padding: var(--global-spacing-4) var(--global-spacing-5);
  --tooltip-size-lg-max-width: 400px;

  /* --- Tooltip Animation --- */
  --tooltip-transition: var(--motion-pattern-fade);
  --tooltip-animation-duration: var(--global-motion-duration-standard);
  --tooltip-animation-easing: var(--global-motion-easing-enter);

  /* --- Tooltip Delay --- */
  --tooltip-show-delay: 0.5s;
  --tooltip-hide-delay: 0s;

  /* --- Tooltip Variants --- */
  --tooltip-info-background: var(--global-color-blue-info-500);
  --tooltip-info-text: var(--color-text-on-brand);
  --tooltip-info-arrow: var(--tooltip-info-background);

  --tooltip-success-background: var(--global-color-green-500);
  --tooltip-success-text: var(--color-text-on-brand);
  --tooltip-success-arrow: var(--tooltip-success-background);

  --tooltip-warning-background: var(--global-color-yellow-500);
  --tooltip-warning-text: var(--global-color-black);
  --tooltip-warning-arrow: var(--tooltip-warning-background);

  --tooltip-error-background: var(--global-color-red-500);
  --tooltip-error-text: var(--color-text-on-brand);
  --tooltip-error-arrow: var(--tooltip-error-background);
} 