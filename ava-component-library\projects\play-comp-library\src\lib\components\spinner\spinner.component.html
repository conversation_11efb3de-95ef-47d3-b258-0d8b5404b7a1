<div class="ava-spinner-container">
 <div *ngIf="isStandardSpinner"
     class="spinner"
     [ngClass]="[
        type,
        sizeClass,
        animation ? 'animated' : '',
        color,
        progressIndex !== undefined ? 'progress-spinner' : '',
        progressClass
     ]">
</div>

  <div *ngIf="type === 'gradient'" class="spinner-wrapper" [ngClass]="sizeClass">
    <div class="spinner gradient" [ngClass]="[sizeClass, animation ? 'animated' : '',color]">
      <div class="gradient-inner"></div>
    </div>
  </div>

  <div *ngIf="type === 'double'" class="spinner-wrapper" [ngClass]="sizeClass">
    <div class="spinner double-outer" [ngClass]="[sizeClass, animation ? 'animated' : '',color]"></div>
    <div class="spinner double-inner" [ngClass]="[sizeClass, animation ? 'animated' : '',color]"></div>
  </div>
</div>