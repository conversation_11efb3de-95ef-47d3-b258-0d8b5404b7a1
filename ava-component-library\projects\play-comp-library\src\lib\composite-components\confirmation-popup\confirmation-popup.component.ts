import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { PopupComponent } from '../../components/popup/popup.component';
import { CommonModule } from '@angular/common';
import { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';

@Component({
  selector: 'ava-confirmation-popup',
  standalone: true,
  imports: [CommonModule, PopupComponent, AvaTextareaComponent],
  templateUrl: './confirmation-popup.component.html',
  styleUrl: './confirmation-popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfirmationPopupComponent {

  // Input: Confirm button label text (default: 'Yes')
  @Input() confirmationLabel = 'Yes';

  // Input: Title text of the popup
  @Input() title = 'title';

  // Input: Message/body text of the popup
  @Input() message = 'message';

  // Input: Controls visibility of the popup
  @Input() show = false;

  // Output: Emits when the popup is closed (via close button or confirm)
  @Output() closed = new EventEmitter<void>();



  // Called when confirm button is clicked
  handleConfirm(text: string): void {
    console.log(text);
    this.closed.emit(); // Emit closed event
  }

  // Called when cancel button is clicked
  handleCancel(): void {
    console.log('Cancel clicked');
    this.show = false; // Hide the popup
  }

  // Called when close icon is clicked
  handleClose(): void {
    this.show = false; // Hide the popup
  }
}
