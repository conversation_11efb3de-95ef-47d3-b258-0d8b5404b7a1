import { Routes } from '@angular/router';
import { AppButtonComponent } from './components/app-button/app-button.component';
import { AppAccordionComponent } from './components/app-accordion/app-accordion.component';
import { AppTabsComponent } from './components/app-tabs/app-tabs.component';
import { AppTextboxComponent } from './components/app-textbox/app-textbox.component';
import { AppCheckboxComponent } from './components/app-checkbox/app-checkbox.component';
import { AppToggleComponent } from './components/app-toggle/app-toggle.component';
import { AppBadgesComponent } from './components/app-badges/app-badges.component';
import { AppSpinnersComponent } from './components/app-spinners/app-spinners.component';
import { AppStepperComponent } from './components/app-stepper/app-stepper.component';
import { AppIconsComponent } from './components/app-icons/app-icons.component';
import { AppAvatarsComponent } from './components/app-avatars/app-avatars.component';
import { AppCardsComponent } from './components/app-cards/app-cards.component';
import { AppTagsComponent } from './components/app-tags/app-tags.component';
import { AppTextareaComponent } from './components/app-textarea/app-textarea.component';
import { AppAutocompleteComponent } from './components/app-autocomplete/app-autocomplete.component';
import { AppPaginationcontrolsComponent } from './components/app-paginationcontrols/app-paginationcontrols.component'; import { AppActionlinksComponent } from './components/app-actionlinks/app-actionlinks.component';
import { AppDropdownComponent } from './components/app-dropdown/app-dropdown.component';
import { AppRadiobuttonComponent } from './components/app-radiobutton/app-radiobutton.component';
import { AppCascadingDropdownComponent } from './components/app-cascading-dropdown/app-cascading-dropdown.component';
import { AppPopupComponent } from './components/app-popup/app-popup.component';
import { AppDateinputcalendarComponent } from './components/app-dateinputcalendar/app-dateinputcalendar.component'; import { AppSidebarComponent } from './components/app-sidebar/app-sidebar.component';
import { AppTableComponent } from './components/app-table/app-table.component';
import { AppFileuploadComponent } from './components/app-fileupload/app-fileupload.component';
import { AppSnackbarComponent } from './components/app-snackbar/app-snackbar.component';

import { AppSliderComponent } from './components/app-slider/app-slider.component';
import { AppFileAttachPillComponent } from './components/app-file-attach-pill/app-file-attach-pill.component';
import { AppProgressbarComponent } from './components/app-progressbar/app-progressbar.component';

/*
import { ReactiveFormComponent } from '../../../play-comp-library/src/lib/components/reactive-form/reactive-form.component';
import { TemplateFormComponent } from '../../../play-comp-library/src/lib/components/template-form/template-form.component';

import { AppSliderComponent } from './components/app-slider/app-slider.component';
import { AppRadiobuttonComponent } from './components/app-radiobutton/app-radiobutton.component';
import { AppToggleComponent } from './components/app-toggle/app-toggle.component';

import { AppActionlinksComponent } from './components/app-actionlinks/app-actionlinks.component';
import { AppInputComponent } from './components/app-input/app-input.component';
import { AppDividersComponent } from './components/app-dividers/app-dividers.component';
import { AppTooltipComponent } from './components/app-tooltip/app-tooltip.component';
import { AppDateinputcalendarComponent } from './components/app-dateinputcalendar/app-dateinputcalendar.component';
import { AppPopupComponent } from './components/app-popup/app-popup.component';
import { AppToastmessageComponent } from './components/app-toastmessage/app-toastmessage.component';
import { AppAccordionComponent } from './components/app-accordion/app-accordion.component';
import { AppBadgesComponent } from './components/app-badges/app-badges.component';
import { AppAvatarsComponent } from './components/app-avatars/app-avatars.component';
import { AppPaginationcontrolsComponent } from './components/app-paginationcontrols/app-paginationcontrols.component';
import { AppSearchfieldsComponent } from './components/app-searchfields/app-searchfields.component';
import { AppProgressbarComponent } from './components/app-progressbar/app-progressbar.component';
import { AppInlinetextelementsComponent } from './components/app-inlinetextelements/app-inlinetextelements.component';
import { AppHeaderComponent } from './components/app-header/app-header.component';
import { AppInputgroupsComponent } from './components/app-inputgroups/app-inputgroups.component';
import { AppTimepickerComponent } from './components/app-timepicker/app-timepicker.component';
import { AppModalsComponent } from './components/app-modals/app-modals.component';
import { AppBreadcrumbsComponent } from './components/app-breadcrumbs/app-breadcrumbs.component';
import { AppSnackbarComponent } from './components/app-snackbar/app-snackbar.component';
import { AppAuthenticationflowcomponentComponent } from './components/app-authenticationflowcomponent/app-authenticationflowcomponent.component';
import { AppFooterComponent } from './components/app-footer/app-footer.component';
import { AppSidebarComponent } from './components/app-sidebar/app-sidebar.component';
import { AppTableheadingcellComponent } from './components/app-tableheadingcell/app-tableheadingcell.component';
import { AppTablecontentcellComponent } from './components/app-tablecontentcell/app-tablecontentcell.component';
import { AppTableComponent } from './components/app-table/app-table.component';
import { AppFileuploadComponent } from './components/app-fileupload/app-fileupload.component';
import { AppPromptbarComponent } from './components/app-promptbar/app-promptbar.component';
import { AppGridComponent } from './components/app-grid/app-grid.component';
import { AppCardsComponent } from './components/app-cards/app-cards.component';
import { AppTabsComponent } from './components/app-tabs/app-tabs.component';
import { AppTypographyComponent } from './components/app-typography/app-typography.component';
import { AppExperienceHistoryCardsComponent } from './components/app-experience-history-cards/app-experience-history-cards.component';
import { AppNavbarComponent } from './components/app-navbar/app-navbar.component';
import { AppSplitScreenComponent } from './components/app-split-screen/app-split-screen.component';
import { AppChatWindowComponent } from './components/app-chat-window/app-chat-window.component';
import { AppDropdownComponent } from './components/app-dropdown/app-dropdown.component';
import { AppStepperComponent } from './components/app-stepper/app-stepper.component';
import { AppIconPillComponent } from './components/app-icon-pill/app-icon-pill.component';
import { AppFileAttachPillComponent } from './components/app-file-attach-pill/app-file-attach-pill.component';
import { AppChartComponent } from './components/app-chart/app-chart.component';
import { AppTagsComponent } from './components/app-tags/app-tags.component';

*/
export const routes: Routes = [
  { path: 'app-button', component: AppButtonComponent },
  { path: 'app-accordion', component: AppAccordionComponent },
  { path: 'app-spinners', component: AppSpinnersComponent },
  { path: 'app-checkbox', component: AppCheckboxComponent },
  { path: 'app-toggle', component: AppToggleComponent },
  { path: 'app-icons', component: AppIconsComponent },
  { path: 'app-badges', component: AppBadgesComponent },
  { path: 'app-avatars', component: AppAvatarsComponent },
  { path: 'app-pagination', component: AppPaginationcontrolsComponent },
  { path: 'app-avatars', component: AppAvatarsComponent },
  { path: 'app-date-input', component: AppDateinputcalendarComponent },
  { path: 'app-dropdown', component: AppDropdownComponent },
  { path: 'app-cascading-dropdown', component: AppCascadingDropdownComponent },
  { path: 'app-tags', component: AppTagsComponent },
  { path: 'app-cards', component: AppCardsComponent },
  { path: 'app-tabs', component: AppTabsComponent },
  { path: 'app-textbox', component: AppTextboxComponent },
  { path: 'app-textarea', component: AppTextareaComponent },
  { path: 'app-autocomplete', component: AppAutocompleteComponent },
  { path: 'app-radiobutton', component: AppRadiobuttonComponent },
  { path: 'app-links', component: AppActionlinksComponent },
  { path: 'app-stepper', component: AppStepperComponent },
  { path: 'app-table', component: AppTableComponent },
  { path: 'app-popup', component: AppPopupComponent },
  { path: 'app-slider', component: AppSliderComponent },
  { path: 'app-file-attach-pill', component: AppFileAttachPillComponent },
  { path:'app-progress-bar',component:AppProgressbarComponent},

  /*
    {path: 'template-driven-forms', component: TemplateFormComponent},
    {path: 'reactive-forms', component: ReactiveFormComponent},
    { path: '', redirectTo: '/app-button', pathMatch: 'full' }, // Default route
   
    
    { path: 'app-radio-button', component: AppRadiobuttonComponent },
   
    { path: 'app-action-links', component: AppActionlinksComponent },
    { path: 'app-input', component: AppInputComponent },
    { path: 'app-dividers', component: AppDividersComponent },
    { path: 'app-tooltip', component: AppTooltipComponent },
  
    { path: 'app-dropdown', component: AppDropdownComponent },
    { path: 'app-popup', component: AppPopupComponent },
    { path: 'app-toast', component: AppToastmessageComponent },
    { path: 'app-accordion', component: AppAccordionComponent },
    { path: 'app-button', component: AppButtonComponent },
    { path: 'app-spinners', component: AppSpinnersComponent },
    { path: 'app-checkbox', component: AppCheckboxComponent },
    { path: 'app-toggle', component: AppToggleComponent },
    { path: 'app-icons', component: AppIconsComponent },
    { path: 'app-badges', component: AppBadgesComponent },
    { path: 'app-avatars', component: AppAvatarsComponent },
    { path: 'app-cards', component: AppCardsComponent },
    { path: 'app-autocomplete', component: AppAutocompleteComponent },

    { path: 'app-tags', component: AppTagsComponent },
    { path: 'app-textarea', component: AppTextareaComponent },


    /*
   {path: 'template-driven-forms', component: TemplateFormComponent},
   {path: 'reactive-forms', component: ReactiveFormComponent},
   { path: '', redirectTo: '/app-button', pathMatch: 'full' }, // Default route
  
   {path: 'app-slider', component: AppSliderComponent},
   { path: 'app-radio-button', component: AppRadiobuttonComponent },
  
   { path: 'app-action-links', component: AppActionlinksComponent },
   { path: 'app-input', component: AppInputComponent },
   { path: 'app-dividers', component: AppDividersComponent },
   { path: 'app-tooltip', component: AppTooltipComponent },
   { path: 'app-date-input', component: AppDateinputcalendarComponent },
   { path: 'app-dropdown', component: AppDropdownComponent },
   { path: 'app-popup', component: AppPopupComponent },
   { path: 'app-toast', component: AppToastmessageComponent },
   { path: 'app-accordion', component: AppAccordionComponent },
   { path: 'app-badges', component: AppBadgesComponent },
   { path: 'app-avatars', component: AppAvatarsComponent },
   { path: 'app-pagination', component: AppPaginationcontrolsComponent },
   { path: 'app-search', component: AppSearchfieldsComponent },
   { path: 'app-progress-bar', component: AppProgressbarComponent },
   { path: 'app-inline-text', component: AppInlinetextelementsComponent },
   { path: 'app-header', component: AppHeaderComponent },
   { path: 'app-input-groups', component: AppInputgroupsComponent },
   { path: 'app-time-picker', component: AppTimepickerComponent },
   { path: 'app-modals', component: AppModalsComponent },
   { path: 'app-breadcrumbs', component: AppBreadcrumbsComponent },
   { path: 'app-snackbar', component: AppSnackbarComponent },
   { path: 'app-authentication-flow', component: AppAuthenticationflowcomponentComponent },
   { path: 'app-stepper', component: AppStepperComponent},
   { path: 'app-footer', component: AppFooterComponent },
   { path: 'app-sidebar', component: AppSidebarComponent },
   { path: 'app-table-heading-cell', component: AppTableheadingcellComponent },
   { path: 'app-table-content-cell', component: AppTablecontentcellComponent },
   { path: 'app-table', component: AppTableComponent },
   { path: 'app-file-upload', component: AppFileuploadComponent },
   { path: 'app-prompt-bar', component: AppPromptbarComponent },
   { path: 'app-grid', component: AppGridComponent },
   { path: 'app-cards', component: AppCardsComponent },
   { path: 'app-tabs', component: AppTabsComponent },
   { path: 'app-typography', component: AppTypographyComponent },
   {path:'app-history-card', component:AppExperienceHistoryCardsComponent},
   { path: 'app-navbar', component: AppNavbarComponent },
   { path: 'app-split-screen', component: AppSplitScreenComponent },
   { path: 'app-chat-window',component:AppChatWindowComponent},
   { path: 'app-icon-pill', component: AppIconPillComponent },
   { path: 'app-chart', component: AppChartComponent },
  
   { path: 'app-tags', component: AppTagsComponent },*/
  { path: 'app-cards', component: AppCardsComponent },
  { path: 'app-tabs', component: AppTabsComponent },
  { path: 'app-textbox', component: AppTextboxComponent },
  { path: 'app-app-snackbar', component: AppSnackbarComponent },

  { path: 'app-sidebar', component: AppSidebarComponent },
  { path: 'app-sidebar', component: AppSidebarComponent },
  { path: 'app-file-upload', component: AppFileuploadComponent }
];
