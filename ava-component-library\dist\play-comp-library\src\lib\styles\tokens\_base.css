/**
 * =========================================================================
 * Play+ Design System: Base Tokens (_base.css)
 *
 * This file is the foundational layer of the Play+ Design System.
 * It contains two core tiers of design tokens:
 *
 * 1. Global Tokens:
 * The raw, context-agnostic primitive values of the system (the palette).
 * These should never be used directly in component styling.
 *
 * 2. Semantic Tokens:
 * The contextual design decisions that give purpose to global tokens.
 * This layer creates the default theme and is consumed by components.
 *
 * This file directly implements the token manifesto.
 * =========================================================================
 */

:root {
  /*
  ==========================================================================
  1. GLOBAL TOKENS (The Raw Palette)
  ==========================================================================
  */

  /* --- Global Palette: Base Brand (Default Theme) --- */
  --global-color-pink-500: #e91e63;
  --global-color-pink-700: #c2185b;
  --global-color-purple-500: #9c27b0;
  --global-color-blue-100: #e3f2fd;

  /* --- Global Palette: Extended Colors for Theming --- */
  --global-color-deep-purple-500: #673ab7;
  --global-color-violet-500: #7c3aed;
  --global-color-violet-700: #5829a8;
  --global-color-royal-blue-500: #2563eb;
  --global-color-royal-blue-700: #1a46a7;
  --global-color-cyan-500: #03bdd4;
  --global-color-cyan-700: #028697;
  --global-color-spearmint-500: #43bd90;
  --global-color-spearmint-700: #308666;
  --global-color-rose-500: #fa709a;
  --global-color-rose-700: #b2506d;
  --global-color-marigold-500: #fee140;
  --global-color-amber-500: #fedea3;

  /* --- Global Palette: Neutral --- */
  --global-color-white: #ffffff;
  --global-color-black: #000000;
  --global-color-gray-50: #f5f5f5;
  --global-color-gray-100: #eeeeee;
  --global-color-gray-200: #dddddd;
  --global-color-gray-300: #bbbec5;
  --global-color-gray-400: #a1a1a1;
  --global-color-gray-500: #6b7280;
  --global-color-gray-600: #8d9096;
  --global-color-gray-700: #6b7280;
  --global-color-gray-800: #1f2937;
  --global-color-gray-900: #111827;

  /* --- Global Palette: Feedback --- */
  --global-color-green-500: #4caf50;
  --global-color-green-600: #059669;
  --global-color-green-700: #047857;
  --global-color-red-500: #f44336;
  --global-color-red-600: #dc2626;
  --global-color-red-700: #b91c1c;
  --global-color-yellow-500: #ff9800;
  --global-color-yellow-600: #ca8a04;
  --global-color-yellow-700: #a16207;
  --global-color-blue-info-500: #2196f3;

  /* --- Global Typography: Font Families --- */
  --global-font-family-display: "PP Neue Machina", sans-serif;
  --global-font-family-heading: "Mulish", sans-serif;
  --global-font-family-body: "Inter", sans-serif;

  /* --- Global Typography: Font Weights --- */
  --global-font-weight-regular: 400;
  --global-font-weight-medium: 500;
  --global-font-weight-semibold: 600;
  --global-font-weight-bold: 700;

  /* --- Global Typography: Font Sizes --- */
  --global-font-size-xs: 0.75rem;
  --global-font-size-sm: 0.875rem;
  --global-font-size-md: 1rem;
  --global-font-size-lg: 1.25rem;
  --global-font-size-xl: 1.5rem;
  --global-font-size-xxl: 2rem;
  --global-font-size-xxxl: 3rem;

  /* --- Global Typography: Line Heights --- */
  --global-line-height-none: 1;
  --global-line-height-tight: 1.2;
  --global-line-height-snug: 1.375;
  --global-line-height-normal: 1.5;
  --global-line-height-relaxed: 1.625;
  --global-line-height-loose: 1.75;
  --global-line-height-extra-loose: 2;
  --global-letter-spacing: 0.01em;

  /* --- Global Spacing --- */
  --global-spacing-0: 0rem;
  --global-spacing-1: 0.25rem;
  --global-spacing-2: 0.5rem;
  --global-spacing-3: 0.75rem;
  --global-spacing-4: 1rem;
  --global-spacing-5: 1.5rem;
  --global-spacing-6: 2rem;
  --global-spacing-7: 3rem;
  --global-spacing-8: 4rem;
  --global-spacing-9: 2rem;
  --global-spacing-10: 2.25rem;
  --global-spacing-11: 2.5rem;
  --global-spacing-12: 3rem;
  --global-spacing-13: 3.5rem;
  --global-spacing-14: 4rem;
  --global-spacing-15: 4.5rem;
  --global-spacing-negative-4: -0.25rem;
  --global-spacing-negative-8: -0.5rem;

  /* --- Global Radius --- */
  --global-radius-sm: 0.5rem;
  --global-radius-md: 0.75rem;
  --global-radius-lg: 1.5rem;
  --global-radius-xl: 0.75rem;
  --global-radius-2xl: 1rem;
  --global-radius-pill: 9999px;
  --global-radius-circle: 50%;
  --global-radius-none: 0;

  /* --- Global Iconography --- */
  --global-icon-size-sm: 16px;
  --global-icon-size-md: 20px;
  --global-icon-size-lg: 24px;
  --global-icon-size-xl: 32px;

  /* --- Global Elevation & Motion --- */
  --global-elevation-01: 0px 2px 4px rgba(0, 0, 0, 0.08);
  --global-elevation-02: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --global-elevation-03: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --global-elevation-04: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --global-elevation-05: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
  --global-motion-duration-swift: 150ms;
  --global-motion-duration-standard: 300ms;
  --global-motion-duration-emphasis: 450ms;
  --global-motion-duration-fast: 150ms;
  --global-motion-duration-slow: 500ms;
  --global-motion-easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --global-motion-easing-enter: cubic-bezier(0, 0, 0.2, 1);
  --global-motion-easing-exit: cubic-bezier(0.4, 0, 1, 1);

  /* --- Global Grid & Layout --- */
  --global-column: 0.75rem;
  --global-gutter: 1.5rem;
  --global-margin: 4.5rem;
  --global-stroke: 0.125rem;

  /* --- Global Elevation Fill Light --- */
  --global-elevation-light-fill-30: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.24) 2.05%,
    rgba(240, 240, 245, 0.24) 100%
  );
  --global-elevation-light-fill-60: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.48) 2.05%,
    rgba(240, 240, 245, 0.48) 100%
  );
  --global-elevation-light-fill-90: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.72) 2.05%,
    rgba(240, 240, 245, 0.72) 100%
  );

  /* --- Global Elevation Fill Dark --- */
  --global-elevation-dark-fill-30: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.12) 2.05%,
    rgba(20, 27, 31, 0.12) 100%
  );
  --global-elevation-dark-fill-60: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
  --global-elevation-dark-fill-90: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.36) 2.05%,
    rgba(20, 27, 31, 0.36) 100%
  );

  /* --- Global Elevation Stroke Light --- */
  --global-elevation-light-stroke-30: #f0f0f5;
  --global-elevation-light-stroke-60: #f0f0f5;
  --global-elevation-light-stroke-90: #f0f0f5;

  /* --- Global Elevation Stroke Dark --- */
  --global-elevation-dark-stroke-30: #f0f0f5;
  --global-elevation-dark-stroke-60: #f0f0f5;
  --global-elevation-dark-stroke-90: #fff;

  /* --- Global Glassmorphic Effects --- */
  --global-glassmorphic-light-30: 0px 2px 4px 0px rgba(0, 0, 0, 0.16),
    0px 4px 24px 0px rgba(20, 22, 31, 0.16);
  --global-glassmorphic-light-60: 0px 2px 4px 0px rgba(0, 0, 0, 0.16),
    0px 4px 24px 0px rgba(20, 22, 31, 0.16);
  --global-glassmorphic-light-90: 0px 2px 4px 0px rgba(0, 0, 0, 0.16),
    0px 4px 24px 0px rgba(20, 22, 31, 0.16);
  --global-glassmorphic-blur-30: blur(15px);
  --global-glassmorphic-blur-60: blur(30px);
  --global-glassmorphic-blur-90: blur(45px);

  /*
  ==========================================================================
  2. SEMANTIC TOKENS (The Design Decisions - Out of box theme)
  ==========================================================================
  */

  /* --- Semantic Colors --- */
  --color-brand-primary: var(--global-color-pink-500);
  --color-brand-secondary: var(--global-color-purple-500);
  --color-brand-primary-hover: var(--global-color-pink-700);
  --color-brand-primary-active: var(--global-color-pink-700);
  --color-text-primary: var(--global-color-gray-700);
  --color-text-secondary: var(--global-color-gray-600);
  --color-text-placeholder: var(--global-color-gray-400);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-pink-500);
  --color-text-interactive-hover: var(--global-color-pink-700);
  --color-text-success: var(--global-color-green-500);
  --color-text-error: var(--global-color-red-500);
  --color-background-primary: var(--global-color-white);
  --color-background-secondary: var(--global-color-gray-50);
  --color-background-disabled: var(--global-color-gray-100);
  --color-surface-interactive-default: var(--global-color-pink-500);
  --color-surface-interactive-hover: var(--global-color-pink-700);
  --color-surface-interactive-active: var(--global-color-pink-700);
  --color-surface-disabled: var(--global-color-gray-200);
  --color-surface-subtle: var(--global-color-gray-100);
  --color-surface-subtle-hover: var(--global-color-gray-100);
  --color-surface-subtle-active: var(--global-color-gray-200);
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  --color-border-interactive: var(--global-color-pink-500);
  --color-border-focus: var(--global-color-pink-500);
  --color-border-error: var(--global-color-red-500);
  --color-border-disabled: var(--global-color-gray-200);

  /* --- Semantic Typography --- */
  --font-family-display: var(--global-font-family-display);
  --font-family-heading: var(--global-font-family-heading);
  --font-family-body: var(--global-font-family-body);
  --font-body-1: var(--global-font-weight-regular) var(--global-font-size-md) /
    var(--global-line-height-normal) var(--font-family-body);
  --font-body-2: var(--global-font-weight-regular) var(--global-font-size-sm) /
    var(--global-line-height-normal) var(--font-family-body);
  --font-label: var(--global-font-weight-medium) var(--global-font-size-xs) /
    var(--global-line-height-tight) var(--font-family-body);
  --font-heading-h1: var(--global-font-weight-bold) var(--global-font-size-xxxl) /
    var(--global-line-height-tight) var(--font-family-heading);
  --font-heading-h2: var(--global-font-weight-bold) var(--global-font-size-xxl) /
    var(--global-line-height-tight) var(--font-family-heading);
  --font-heading-h3: var(--global-font-weight-semibold)
    var(--global-font-size-xl) / var(--global-line-height-tight)
    var(--font-family-heading);
  --font-heading-h4: var(--global-font-weight-semibold)
    var(--global-font-size-lg) / var(--global-line-height-tight)
    var(--font-family-heading);
  --font-heading-h5: var(--global-font-weight-semibold)
    var(--global-font-size-heading-5) / var(--global-line-height-heading-s)
    var(--font-family-heading);
  --font-heading-h6: var(--global-font-weight-semibold)
    var(--global-font-size-heading-6) / var(--global-line-height-heading-xs)
    var(--font-family-heading);

  /* --- Semantic Motion Patterns --- */
  --motion-pattern-fade: var(--global-motion-duration-swift)
    var(--global-motion-easing-standard);
  --motion-pattern-slide: var(--global-motion-duration-standard)
    var(--global-motion-easing-enter);
  --motion-pattern-pop: var(--global-motion-duration-emphasis)
    var(--global-motion-easing-standard);

  /* --- Semantic Layout --- */
  --layout-max-width: 1440px;
  --layout-gutter-default: var(--global-spacing-4);
  --layout-margin-mobile: var(--global-spacing-4);
  --layout-margin-desktop: var(--global-spacing-6);

  /* --- Semantic Iconography --- */
  --icon-size-sm: var(--global-icon-size-sm);
  --icon-size-md: var(--global-icon-size-md);
  --icon-size-lg: var(--global-icon-size-lg);
  --icon-size-xl: var(--global-icon-size-xl);

  /* --- Semantic Content --- */
  --content-line-measure: 75ch;
  --content-paragraph-spacing: var(--global-spacing-4);

  /* --- Semantic Accessibility --- */
  --accessibility-focus-ring-color: var(--color-border-focus);
  --accessibility-focus-ring-style: solid;
  --accessibility-focus-ring-width: 2px;
  --accessibility-focus-ring-offset: 2px;

  /* --- Semantic Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /* =======================
     RGB HELPER VARIABLES - For Alpha Transparency
     ======================= */
  --rgb-brand-primary: var(--color-brand-primary); /* From --color-brand-primary */
  --rgb-brand-secondary: 156, 39, 176; /* From --color-brand-secondary */
  --rgb-violet: 124, 58, 237; /* From --global-color-violet-500 */
  --rgb-royal-blue: 37, 99, 235; /* From --global-color-royal-blue-500 */
  --rgb-rose: 250, 112, 154; /* From --global-color-rose-500 */
  --rgb-marigold: 254, 225, 64; /* From --global-color-marigold-500 */
  --rgb-spearmint: 67, 189, 144; /* From --global-color-spearmint-500 */
  --rgb-white: 255, 255, 255; /* From --global-color-white */
  --rgb-black: 0, 0, 0; /* From --global-color-black */

  /* =======================
     GLASS (Surface) Intensity - Industry Leading Values
     ======================= */
  --surface-0:   var(--color-background-primary); /* Fully solid, no translucency */
  --surface-10:  rgba(var(--rgb-white), 0.95); /* Barely noticeable translucency */
  --surface-20:  rgba(var(--rgb-white), 0.90); /* Subtle translucency, maintains solidity */
  --surface-30:  rgba(var(--rgb-white), 0.85); /* Light glass effect, good readability */
  --surface-40:  rgba(var(--rgb-white), 0.80); /* Noticeable glass, enterprise friendly */
  --surface-50:  rgba(var(--rgb-white), 0.70); /* Medium frosted glass, balanced visibility */
  --surface-60:  rgba(var(--rgb-white), 0.60); /* Strong glass effect, creative contexts */
  --surface-70:  rgba(var(--rgb-white), 0.50); /* Heavy glass, floating UI elements */
  --surface-80:  rgba(var(--rgb-white), 0.40); /* Deep translucency, overlay effects */
  --surface-90:  rgba(var(--rgb-white), 0.30); /* Near-transparent, spotlight UIs */
  --surface-100: rgba(var(--rgb-white), 0.20); /* Maximum translucency, ambient overlays */

  /* Glass Blur Effects */
  --surface-blur-0:   0px;
  --surface-blur-10:  blur(2px);
  --surface-blur-20:  blur(4px);
  --surface-blur-30:  blur(6px);
  --surface-blur-40:  blur(8px);
  --surface-blur-50:  blur(12px);
  --surface-blur-60:  blur(16px);
  --surface-blur-70:  blur(20px);
  --surface-blur-80:  blur(24px);
  --surface-blur-90:  blur(30px);
  --surface-blur-100: blur(40px);

  /* =======================
     LIGHT (Feedback) Intensity - Premium Glow System
     ======================= */
  --light-0:   none; /* No feedback */
  --light-10:  0 1px 2px rgba(var(--rgb-black), 0.05); /* Minimal elevation */
  --light-20:  0 2px 4px rgba(var(--rgb-black), 0.08); /* Subtle hover hint */
  --light-30:  0 4px 8px rgba(var(--rgb-black), 0.12), 0 0 4px rgba(var(--rgb-brand-primary), 0.08); /* Brand tint */
  --light-40:  0 6px 12px rgba(var(--rgb-black), 0.15), 0 0 8px rgba(var(--rgb-brand-primary), 0.12); /* Clear interaction */
  --light-50:  0 8px 16px rgba(var(--rgb-black), 0.18), 0 0 12px rgba(var(--rgb-brand-secondary), 0.15); /* Focus state */
  --light-60:  0 12px 24px rgba(var(--rgb-black), 0.20), 0 0 16px rgba(var(--rgb-violet), 0.18); /* Active state */
  --light-70:  0 16px 32px rgba(var(--rgb-black), 0.22), 0 0 20px rgba(var(--rgb-royal-blue), 0.20); /* Strong emphasis */
  --light-80:  0 20px 40px rgba(var(--rgb-black), 0.25), 0 0 24px rgba(var(--rgb-brand-primary), 0.22); /* High impact */
  --light-90:  0 24px 48px rgba(var(--rgb-black), 0.28), 0 0 32px rgba(var(--rgb-brand-secondary), 0.25); /* Dramatic effect */
  --light-100: 0 32px 64px rgba(var(--rgb-black), 0.30), 0 0 40px rgba(var(--rgb-violet), 0.28); /* Maximum drama */

  /* Light Ring Effects for Focus */
  --light-ring-0:   none;
  --light-ring-10:  0 0 0 1px rgba(var(--rgb-brand-primary), 0.10);
  --light-ring-20:  0 0 0 2px rgba(var(--rgb-brand-primary), 0.15);
  --light-ring-30:  0 0 0 3px rgba(var(--rgb-brand-secondary), 0.18);
  --light-ring-40:  0 0 0 4px rgba(var(--rgb-violet), 0.20);
  --light-ring-50:  0 0 0 5px rgba(var(--rgb-royal-blue), 0.22);
  --light-ring-60:  0 0 0 6px rgba(var(--rgb-brand-primary), 0.25);
  --light-ring-70:  0 0 0 7px rgba(var(--rgb-brand-secondary), 0.28);
  --light-ring-80:  0 0 0 8px rgba(var(--rgb-violet), 0.30);
  --light-ring-90:  0 0 0 10px rgba(var(--rgb-royal-blue), 0.32);
  --light-ring-100: 0 0 0 12px rgba(var(--rgb-brand-primary), 0.35);

  /* =======================
     LIQUID (Motion) Intensity - Fluid Animation System
     ======================= */
  --motion-0:   linear; /* No easing, instant */
  --motion-10:  cubic-bezier(0.25, 0.1, 0.25, 1); /* Gentle ease */
  --motion-20:  cubic-bezier(0.4, 0, 0.2, 1); /* Material standard */
  --motion-30:  cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Smooth ease-out */
  --motion-40:  cubic-bezier(0.23, 1, 0.32, 1); /* Quart ease-out */
  --motion-50:  cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Back ease-out */
  --motion-60:  cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Back spring */
  --motion-70:  cubic-bezier(0.34, 1.56, 0.64, 1); /* Elastic ease-out */
  --motion-80:  cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Quint ease-out */
  --motion-90:  cubic-bezier(0.19, 1, 0.22, 1); /* Expo ease-out */
  --motion-100: cubic-bezier(0.86, 0, 0.07, 1); /* Extreme elastic */

  /* Motion Durations */
  --motion-duration-0:   0ms;
  --motion-duration-10:  100ms;
  --motion-duration-20:  150ms;
  --motion-duration-30:  200ms;
  --motion-duration-40:  250ms;
  --motion-duration-50:  300ms;
  --motion-duration-60:  400ms;
  --motion-duration-70:  500ms;
  --motion-duration-80:  600ms;
  --motion-duration-90:  800ms;
  --motion-duration-100: 1000ms;

  /* =======================
     GRADIENT (Expressive Layer) - Cinematic Gradients
     ======================= */
  --gradient-0:   none; /* No gradient */
  --gradient-10:  linear-gradient(90deg, transparent 0%, rgba(var(--rgb-brand-primary), 0.05) 100%); /* Subtle brand hint */
  --gradient-20:  linear-gradient(90deg, rgba(var(--rgb-brand-primary), 0.08) 0%, rgba(var(--rgb-brand-secondary), 0.08) 100%); /* Soft duo-tone */
  --gradient-30:  linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.12) 0%, rgba(var(--rgb-violet), 0.12) 100%); /* Diagonal flow */
  --gradient-40:  linear-gradient(120deg, rgba(var(--rgb-brand-primary), 0.15) 0%, rgba(var(--rgb-royal-blue), 0.15) 100%); /* Brand spectrum */
  --gradient-50:  linear-gradient(90deg, rgba(var(--rgb-brand-primary), 0.20) 0%, rgba(var(--rgb-violet), 0.15) 50%, rgba(var(--rgb-royal-blue), 0.20) 100%); /* Triple stop */
  --gradient-60:  linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.25) 0%, rgba(var(--rgb-marigold), 0.20) 50%, rgba(var(--rgb-violet), 0.25) 100%); /* Warm to cool */
  --gradient-70:  linear-gradient(120deg, rgba(var(--rgb-brand-primary), 0.30) 0%, rgba(var(--rgb-rose), 0.25) 25%, rgba(var(--rgb-violet), 0.30) 75%, rgba(var(--rgb-royal-blue), 0.25) 100%); /* Complex spectrum */
  --gradient-80:  radial-gradient(circle at 30% 30%, rgba(var(--rgb-brand-primary), 0.35) 0%, rgba(var(--rgb-violet), 0.30) 50%, rgba(var(--rgb-royal-blue), 0.35) 100%); /* Radial burst */
  --gradient-90:  conic-gradient(from 0deg at 50% 50%, rgba(var(--rgb-brand-primary), 0.40) 0deg, rgba(var(--rgb-marigold), 0.35) 72deg, rgba(var(--rgb-violet), 0.40) 144deg, rgba(var(--rgb-royal-blue), 0.35) 216deg, rgba(var(--rgb-brand-primary), 0.40) 288deg, rgba(var(--rgb-brand-primary), 0.40) 360deg); /* Conic spectrum */
  --gradient-100: linear-gradient(135deg, rgba(var(--rgb-brand-primary), 0.45) 0%, rgba(var(--rgb-rose), 0.40) 20%, rgba(var(--rgb-marigold), 0.35) 40%, rgba(var(--rgb-spearmint), 0.40) 60%, rgba(var(--rgb-violet), 0.45) 80%, rgba(var(--rgb-royal-blue), 0.40) 100%); /* Ultimate rainbow */

  /* Gradient Overlays for Glass Surfaces */
  --gradient-glass-0:   none;
  --gradient-glass-10:  linear-gradient(135deg, rgba(var(--rgb-white), 0.1) 0%, rgba(var(--rgb-white), 0.05) 100%);
  --gradient-glass-20:  linear-gradient(135deg, rgba(var(--rgb-white), 0.15) 0%, rgba(var(--rgb-brand-primary), 0.05) 100%);
  --gradient-glass-30:  linear-gradient(135deg, rgba(var(--rgb-white), 0.20) 0%, rgba(var(--rgb-brand-secondary), 0.08) 100%);
  --gradient-glass-40:  linear-gradient(135deg, rgba(var(--rgb-white), 0.25) 0%, rgba(var(--rgb-violet), 0.10) 100%);
  --gradient-glass-50:  linear-gradient(135deg, rgba(var(--rgb-white), 0.30) 0%, rgba(var(--rgb-royal-blue), 0.12) 50%, rgba(var(--rgb-brand-primary), 0.10) 100%);
  --gradient-glass-60:  linear-gradient(135deg, rgba(var(--rgb-white), 0.35) 0%, rgba(var(--rgb-marigold), 0.15) 50%, rgba(var(--rgb-violet), 0.15) 100%);
  --gradient-glass-70:  linear-gradient(135deg, rgba(var(--rgb-white), 0.40) 0%, rgba(var(--rgb-rose), 0.18) 30%, rgba(var(--rgb-royal-blue), 0.18) 100%);
  --gradient-glass-80:  linear-gradient(135deg, rgba(var(--rgb-white), 0.45) 0%, rgba(var(--rgb-brand-primary), 0.20) 25%, rgba(var(--rgb-violet), 0.20) 75%, rgba(var(--rgb-royal-blue), 0.18) 100%);
  --gradient-glass-90:  linear-gradient(135deg, rgba(var(--rgb-white), 0.50) 0%, rgba(var(--rgb-marigold), 0.22) 33%, rgba(var(--rgb-brand-primary), 0.22) 66%, rgba(var(--rgb-violet), 0.22) 100%);
  --gradient-glass-100: linear-gradient(135deg, rgba(var(--rgb-white), 0.55) 0%, rgba(var(--rgb-rose), 0.25) 20%, rgba(var(--rgb-marigold), 0.20) 40%, rgba(var(--rgb-spearmint), 0.22) 60%, rgba(var(--rgb-violet), 0.25) 80%, rgba(var(--rgb-royal-blue), 0.22) 100%);

  /* =======================
     APP CATEGORY PRESETS (Example)
     ======================= */

  /* Enterprise: Solid, minimal feedback, subtle motion, subtle gradient */
  /* [data-app-category="enterprise"] {
    --app-surface: var(--surface-100);
    --app-light: var(--light-100);
    --app-motion: var(--motion-100);
    --app-gradient: var(--gradient-100);
  } */

  /* Consumer: Balanced glass, feedback, motion, and gradient */
  /* [data-app-category="consumer"] {
    --app-surface: var(--surface-50);
    --app-light: var(--light-50);
    --app-motion: var(--motion-50);
    --app-gradient: var(--gradient-50);
  } */

  /* Marketing: Maximum glass, feedback, motion, and gradient */
  /* [data-app-category="marketing"] {
    --app-surface: var(--surface-80);
    --app-light: var(--light-90);
    --app-motion: var(--motion-80);
    --app-gradient: var(--gradient-100);
  } */

  /* Usage: Set data-app-category="enterprise" | "consumer" | "marketing" on <html> or <body> */
  /* Then use var(--app-surface), var(--app-light), etc. in your component tokens/styles */

  /* Play+ Metaphor Semantic Variables */
  /* Glass Metaphor */
  --surface-glass-bg: rgba(var(--rgb-white), 0.15); /* fallback for glass background */
  --surface-glass-border: rgba(var(--rgb-white), 0.7); /* fallback for glass border */
  --surface-glass-shadow: rgba(var(--rgb-white), 0.18); /* fallback for glass shadow */

  /* Light Metaphor */
  --color-brand-primary-glow: rgba(var(--rgb-brand-primary), 0.45); /* fallback for main glow */
  --color-brand-primary-glow-focus: rgba(var(--rgb-brand-primary), 0.65); /* focus/hover glow */
  --color-brand-secondary-glow: rgba(var(--rgb-brand-secondary), 0.10); /* secondary shadow */
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-success);
  --color-liquid-shimmer-end: var(--color-brand-primary);
}
