<div class="documentation container">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Authentication Flow Component</h1>
        <p class="description">
          A versatile authentication flow component that supports animations and user interactions. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} AuthenticationComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
      <div class="row">
        <div class="col-12">
          <div
            class="section-header"
            tabindex="0"
            role="button"
            (click)="toggleSection(i)"
            (keydown.enter)="toggleSection(i)"
            (keydown.space)="toggleSection(i)"
          >
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <!-- Basic Usage -->
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12">
                  <button class="auth-toggle-button" (click)="toggleAuth(true)">
                    Open Authentication with Animations
                  </button>
                  <button class="auth-toggle-button" (click)="toggleAuth(false)">
                    Open Authentication without Animations
                  </button>
                </div>
                <div class="col-12" *ngIf="showAuth">
                  <div class="auth-container">
                    <h3>{{ enableAnimations ? 'With Animations' : 'Without Animations' }}</h3>
                    <app-authentication [enableAnimation]="enableAnimations"></app-authentication>
                    <button class="auth-toggle-button" (click)="closeAuth()">
                      Close Authentication
                    </button>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button
            class="copy-button"
            (click)="copyCode(section.title.toLowerCase())"
          >
            <awe-icons name="awe_copy_filled"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>userClick</code></td>
              <td><code>EventEmitter&lt;Event&gt;</code></td>
              <td>Emitted when the button is clicked</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>



