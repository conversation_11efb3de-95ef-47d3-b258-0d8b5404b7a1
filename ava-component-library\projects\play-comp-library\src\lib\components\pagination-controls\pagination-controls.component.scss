.pagination-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;

  .nav-btn,
  .page-btn {
    border: var(--pagination-border);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    background: var(--pagination-background);
    color: var(--pagination-item-text);
    font-weight: var(-pagination-font-weight-md); //500;
    transition: all 0.2s ease;

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    &.active {
      background: rgb(224, 188, 231);
      color: var(--pagination-item-text);
    }
  }

  .dots {
    padding: 0.5rem;
    color: var(--pagination-item-text);
  }
}

.pagination-container.standard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 1rem 0;

  .nav-left,
  .nav-right {
    width: 7.5rem;
    display: flex;
    justify-content: center;
  }

  .pages {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex: 1;
  }

  .nav-btn {
    background: none;
    border: none;
    background: var(--pagination-background);
    color: var(--pagination-item-text);
    font-weight: var(-pagination-font-weight-md);
    font-size: 0.875rem;
    cursor: pointer;

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
  }

  .page-btn {
    background: none;
    border: none;
    background: var(--pagination-background);
    color: var(--pagination-item-text);
    font-weight: var(-pagination-font-weight-md);
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
      background-color: var(--pagination-background);
    }

    &.active {
      background-color: rgb(224, 188, 231);
      color: var(--pagination-dark-text); // #111827;
      font-weight: 600;
    }
  }

  .dots {
    padding: 0.5rem;
    color: var(--pagination-item-disabled-tex); //#9ca3af;
  }
}

.pagination-container.page {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1rem 0;

  .nav-btn {
    padding: 0.5rem 1rem;
    border: var(--pagination-border); //1px solid #9333ea;
    border-radius: 0.5rem;
    background: var(--color-background-primary);
    color: var(--pagination-item-text); //#9333ea;
    font-weight: var(--pagination-font-weight-md); //500
    cursor: pointer;
    transition: all 0.2s ease;

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
  }

  .page-label {
    font-weight: var(--pagination-font-weight-md);
    color: var(--pagination-item-text);
    font-size: 0.875rem;
    text-align: center;
    flex: 1;
  }
}
.pagination-container.simplepage {
  display: flex;
  justify-content: center; // center everything
  align-items: center;
  gap: 1.5rem; // space between arrow and text

  .icon-btn {
    background: transparent;
    border: none;
    color: var(--pagination-item-text);
    font-size: 1.2rem;
    cursor: pointer;

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
  }

  .page-label {
    font-weight: var(--pagination-font-weight-md);
    font-size: 0.875rem;
    color: var(--pagination-item-text);
  }
}
