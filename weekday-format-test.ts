// Test file to verify weekday format functionality
// This demonstrates how the weekday format feature works

interface WeekdayData {
  full: string;
  three: string;
  two: string;
  one: string;
}

// Mock the weekday data structure from the calendar component
const weekDaysBase: WeekdayData[] = [
  { full: 'Monday', three: 'Mon', two: 'Mo', one: 'M' },
  { full: 'Tuesday', three: 'Tue', two: 'Tu', one: 'T' },
  { full: 'Wednesday', three: 'Wed', two: 'We', one: 'W' },
  { full: 'Thursday', three: 'Thu', two: 'Th', one: 'T' },
  { full: 'Friday', three: 'Fri', two: 'Fr', one: 'F' },
  { full: 'Saturday', three: 'Sat', two: 'Sa', one: 'S' },
  { full: 'Sunday', three: 'Sun', two: 'Su', one: 'S' }
];

// Function to generate weekdays based on format (mimics the calendar component logic)
function getWeekDays(weekdayFormat: 1 | 2 | 3): string[] {
  const formatKey = weekdayFormat === 1 ? 'one' : 
                   weekdayFormat === 2 ? 'two' : 'three';
  return weekDaysBase.map(day => day[formatKey as keyof WeekdayData]);
}

// Test the functionality
console.log('=== Calendar Weekday Format Test ===\n');

console.log('Format 3 (Three letters):');
console.log(getWeekDays(3));
console.log('Expected: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]\n');

console.log('Format 2 (Two letters):');
console.log(getWeekDays(2));
console.log('Expected: ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"]\n');

console.log('Format 1 (Single letter):');
console.log(getWeekDays(1));
console.log('Expected: ["M", "T", "W", "T", "F", "S", "S"]\n');

// Test with different scenarios
function testWeekdayFormat() {
  const testCases = [
    { format: 3 as const, expected: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    { format: 2 as const, expected: ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'] },
    { format: 1 as const, expected: ['M', 'T', 'W', 'T', 'F', 'S', 'S'] }
  ];

  let allTestsPassed = true;

  testCases.forEach(({ format, expected }) => {
    const result = getWeekDays(format);
    const passed = JSON.stringify(result) === JSON.stringify(expected);
    
    console.log(`Test format ${format}: ${passed ? 'PASSED' : 'FAILED'}`);
    if (!passed) {
      console.log(`  Expected: ${JSON.stringify(expected)}`);
      console.log(`  Got: ${JSON.stringify(result)}`);
      allTestsPassed = false;
    }
  });

  console.log(`\n=== Test Results ===`);
  console.log(`All tests ${allTestsPassed ? 'PASSED' : 'FAILED'} ✓`);
  
  return allTestsPassed;
}

// Run the tests
testWeekdayFormat();

// Example usage in Angular component
console.log('\n=== Example Angular Component Usage ===');
console.log(`
// In your component template:
<ava-calendar [weekdayFormat]="3"></ava-calendar>  <!-- Mon, Tue, Wed... -->
<ava-calendar [weekdayFormat]="2"></ava-calendar>  <!-- Mo, Tu, We... -->
<ava-calendar [weekdayFormat]="1"></ava-calendar>  <!-- M, T, W... -->

// In your component TypeScript:
export class MyComponent {
  weekdayFormat: 1 | 2 | 3 = 3; // Default to 3 letters
  
  changeFormat(format: 1 | 2 | 3) {
    this.weekdayFormat = format;
  }
}
`);

export { getWeekDays, testWeekdayFormat };
